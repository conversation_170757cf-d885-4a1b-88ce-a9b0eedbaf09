/**
 * DL-Engine 物理世界测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { PhysicsWorld } from '../PhysicsWorld'
import { Entity } from '@dl-engine/engine-ecs'

// Mock Rapier physics engine
const mockRigidBody = {
  translation: () => ({ x: 0, y: 0, z: 0 }),
  setTranslation: vi.fn(),
  rotation: () => ({ x: 0, y: 0, z: 0, w: 1 }),
  setRotation: vi.fn(),
  linvel: () => ({ x: 0, y: 0, z: 0 }),
  setLinvel: vi.fn(),
  angvel: () => ({ x: 0, y: 0, z: 0 }),
  setAngvel: vi.fn(),
  applyImpulse: vi.fn(),
  applyForce: vi.fn(),
  setBodyType: vi.fn(),
  setGravityScale: vi.fn(),
  userData: null
}

const mockCollider = {
  translation: () => ({ x: 0, y: 0, z: 0 }),
  setTranslation: vi.fn(),
  rotation: () => ({ x: 0, y: 0, z: 0, w: 1 }),
  setRotation: vi.fn(),
  userData: null
}

const mockWorld = {
  step: vi.fn(),
  createRigidBody: vi.fn(() => mockRigidBody),
  createCollider: vi.fn(() => mockCollider),
  removeRigidBody: vi.fn(),
  removeCollider: vi.fn(),
  gravity: { x: 0, y: -9.81, z: 0 },
  setGravity: vi.fn(),
  intersectionsWith: vi.fn(),
  contactsWith: vi.fn(),
  castRay: vi.fn(),
  castShape: vi.fn()
}

const mockRapier = {
  init: vi.fn().mockResolvedValue(true),
  World: vi.fn(() => mockWorld),
  RigidBodyDesc: {
    dynamic: vi.fn().mockReturnThis(),
    kinematicPositionBased: vi.fn().mockReturnThis(),
    fixed: vi.fn().mockReturnThis(),
    setTranslation: vi.fn().mockReturnThis(),
    setRotation: vi.fn().mockReturnThis()
  },
  ColliderDesc: {
    cuboid: vi.fn().mockReturnThis(),
    ball: vi.fn().mockReturnThis(),
    capsule: vi.fn().mockReturnThis(),
    cylinder: vi.fn().mockReturnThis(),
    cone: vi.fn().mockReturnThis(),
    trimesh: vi.fn().mockReturnThis(),
    convexHull: vi.fn().mockReturnThis(),
    setTranslation: vi.fn().mockReturnThis(),
    setRotation: vi.fn().mockReturnThis(),
    setFriction: vi.fn().mockReturnThis(),
    setRestitution: vi.fn().mockReturnThis(),
    setDensity: vi.fn().mockReturnThis(),
    setSensor: vi.fn().mockReturnThis()
  },
  Vector3: vi.fn((x, y, z) => ({ x, y, z })),
  Quaternion: vi.fn((x, y, z, w) => ({ x, y, z, w })),
  Ray: vi.fn((origin, direction) => ({ origin, direction }))
}

// Mock Rapier module
vi.mock('@dimforge/rapier3d', () => ({
  default: mockRapier
}))

describe('PhysicsWorld', () => {
  let physicsWorld: PhysicsWorld
  
  beforeEach(async () => {
    physicsWorld = PhysicsWorld.getInstance()
    await physicsWorld.initialize()
    
    // 重置所有mock
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    physicsWorld.destroy()
  })
  
  describe('Initialization', () => {
    it('should initialize physics world successfully', async () => {
      expect(mockRapier.init).toHaveBeenCalled()
      expect(physicsWorld.isInitialized()).toBe(true)
    })
    
    it('should set default gravity', async () => {
      const gravity = physicsWorld.getGravity()
      expect(gravity.y).toBe(-9.81)
    })
    
    it('should handle initialization errors', async () => {
      mockRapier.init.mockRejectedValueOnce(new Error('Rapier init failed'))
      
      const newWorld = new (PhysicsWorld as any)()
      await expect(newWorld.initialize()).rejects.toThrow('Rapier init failed')
    })
  })
  
  describe('Rigid Body Management', () => {
    let entity: Entity
    
    beforeEach(() => {
      entity = 1 as Entity
    })
    
    it('should create dynamic rigid body', () => {
      const bodyId = physicsWorld.createRigidBody(entity, {
        type: 'dynamic',
        position: { x: 1, y: 2, z: 3 },
        rotation: { x: 0, y: 0, z: 0, w: 1 }
      })
      
      expect(bodyId).toBeDefined()
      expect(mockWorld.createRigidBody).toHaveBeenCalled()
      expect(physicsWorld.hasRigidBody(entity)).toBe(true)
    })
    
    it('should create kinematic rigid body', () => {
      const bodyId = physicsWorld.createRigidBody(entity, {
        type: 'kinematic',
        position: { x: 0, y: 0, z: 0 }
      })
      
      expect(bodyId).toBeDefined()
      expect(mockRapier.RigidBodyDesc.kinematicPositionBased).toHaveBeenCalled()
    })
    
    it('should create static rigid body', () => {
      const bodyId = physicsWorld.createRigidBody(entity, {
        type: 'static',
        position: { x: 0, y: 0, z: 0 }
      })
      
      expect(bodyId).toBeDefined()
      expect(mockRapier.RigidBodyDesc.fixed).toHaveBeenCalled()
    })
    
    it('should remove rigid body', () => {
      const bodyId = physicsWorld.createRigidBody(entity, {
        type: 'dynamic',
        position: { x: 0, y: 0, z: 0 }
      })
      
      expect(physicsWorld.hasRigidBody(entity)).toBe(true)
      
      physicsWorld.removeRigidBody(entity)
      
      expect(physicsWorld.hasRigidBody(entity)).toBe(false)
      expect(mockWorld.removeRigidBody).toHaveBeenCalled()
    })
    
    it('should update rigid body transform', () => {
      physicsWorld.createRigidBody(entity, {
        type: 'dynamic',
        position: { x: 0, y: 0, z: 0 }
      })
      
      physicsWorld.setRigidBodyTransform(entity, {
        position: { x: 5, y: 10, z: 15 },
        rotation: { x: 0, y: 0, z: 0, w: 1 }
      })
      
      expect(mockRigidBody.setTranslation).toHaveBeenCalledWith({ x: 5, y: 10, z: 15 }, true)
      expect(mockRigidBody.setRotation).toHaveBeenCalledWith({ x: 0, y: 0, z: 0, w: 1 }, true)
    })
  })
  
  describe('Collider Management', () => {
    let entity: Entity
    
    beforeEach(() => {
      entity = 1 as Entity
      physicsWorld.createRigidBody(entity, {
        type: 'dynamic',
        position: { x: 0, y: 0, z: 0 }
      })
    })
    
    it('should create box collider', () => {
      const colliderId = physicsWorld.createCollider(entity, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      })
      
      expect(colliderId).toBeDefined()
      expect(mockRapier.ColliderDesc.cuboid).toHaveBeenCalledWith(0.5, 0.5, 0.5)
    })
    
    it('should create sphere collider', () => {
      const colliderId = physicsWorld.createCollider(entity, {
        shape: 'sphere',
        radius: 2
      })
      
      expect(colliderId).toBeDefined()
      expect(mockRapier.ColliderDesc.ball).toHaveBeenCalledWith(2)
    })
    
    it('should create capsule collider', () => {
      const colliderId = physicsWorld.createCollider(entity, {
        shape: 'capsule',
        height: 2,
        radius: 0.5
      })
      
      expect(colliderId).toBeDefined()
      expect(mockRapier.ColliderDesc.capsule).toHaveBeenCalledWith(1, 0.5)
    })
    
    it('should set collider material properties', () => {
      physicsWorld.createCollider(entity, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 },
        friction: 0.8,
        restitution: 0.3,
        density: 2.0
      })
      
      expect(mockRapier.ColliderDesc.setFriction).toHaveBeenCalledWith(0.8)
      expect(mockRapier.ColliderDesc.setRestitution).toHaveBeenCalledWith(0.3)
      expect(mockRapier.ColliderDesc.setDensity).toHaveBeenCalledWith(2.0)
    })
    
    it('should create sensor collider', () => {
      physicsWorld.createCollider(entity, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 },
        isSensor: true
      })
      
      expect(mockRapier.ColliderDesc.setSensor).toHaveBeenCalledWith(true)
    })
  })
  
  describe('Physics Simulation', () => {
    it('should step physics simulation', () => {
      const deltaTime = 1/60 // 60 FPS
      
      physicsWorld.step(deltaTime)
      
      expect(mockWorld.step).toHaveBeenCalled()
    })
    
    it('should handle variable time steps', () => {
      physicsWorld.step(1/30) // 30 FPS
      physicsWorld.step(1/120) // 120 FPS
      
      expect(mockWorld.step).toHaveBeenCalledTimes(2)
    })
    
    it('should accumulate time for fixed timestep', () => {
      // 配置固定时间步长
      physicsWorld.setFixedTimestep(1/60)
      
      // 提供较大的时间步长
      physicsWorld.step(1/30)
      
      // 应该执行两次物理步骤
      expect(mockWorld.step).toHaveBeenCalledTimes(2)
    })
  })
  
  describe('Forces and Impulses', () => {
    let entity: Entity
    
    beforeEach(() => {
      entity = 1 as Entity
      physicsWorld.createRigidBody(entity, {
        type: 'dynamic',
        position: { x: 0, y: 0, z: 0 }
      })
    })
    
    it('should apply impulse to rigid body', () => {
      physicsWorld.applyImpulse(entity, { x: 10, y: 0, z: 0 })
      
      expect(mockRigidBody.applyImpulse).toHaveBeenCalledWith({ x: 10, y: 0, z: 0 }, true)
    })
    
    it('should apply force to rigid body', () => {
      physicsWorld.applyForce(entity, { x: 100, y: 0, z: 0 })
      
      expect(mockRigidBody.applyForce).toHaveBeenCalledWith({ x: 100, y: 0, z: 0 }, true)
    })
    
    it('should set linear velocity', () => {
      physicsWorld.setLinearVelocity(entity, { x: 5, y: 0, z: 0 })
      
      expect(mockRigidBody.setLinvel).toHaveBeenCalledWith({ x: 5, y: 0, z: 0 }, true)
    })
    
    it('should set angular velocity', () => {
      physicsWorld.setAngularVelocity(entity, { x: 0, y: 1, z: 0 })
      
      expect(mockRigidBody.setAngvel).toHaveBeenCalledWith({ x: 0, y: 1, z: 0 }, true)
    })
  })
  
  describe('Collision Detection', () => {
    let entity1: Entity
    let entity2: Entity
    
    beforeEach(() => {
      entity1 = 1 as Entity
      entity2 = 2 as Entity
      
      physicsWorld.createRigidBody(entity1, {
        type: 'dynamic',
        position: { x: 0, y: 0, z: 0 }
      })
      
      physicsWorld.createRigidBody(entity2, {
        type: 'dynamic',
        position: { x: 2, y: 0, z: 0 }
      })
      
      physicsWorld.createCollider(entity1, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      })
      
      physicsWorld.createCollider(entity2, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      })
    })
    
    it('should detect intersections', () => {
      mockWorld.intersectionsWith.mockReturnValue([mockCollider])
      
      const intersections = physicsWorld.getIntersections(entity1)
      
      expect(mockWorld.intersectionsWith).toHaveBeenCalled()
      expect(intersections).toHaveLength(1)
    })
    
    it('should detect contacts', () => {
      mockWorld.contactsWith.mockReturnValue([
        { collider1: mockCollider, collider2: mockCollider }
      ])
      
      const contacts = physicsWorld.getContacts(entity1)
      
      expect(mockWorld.contactsWith).toHaveBeenCalled()
      expect(contacts).toHaveLength(1)
    })
  })
  
  describe('Raycasting', () => {
    it('should cast ray and return hit result', () => {
      const hitResult = {
        collider: mockCollider,
        toi: 5.0,
        point: { x: 5, y: 0, z: 0 },
        normal: { x: -1, y: 0, z: 0 }
      }
      
      mockWorld.castRay.mockReturnValue(hitResult)
      
      const result = physicsWorld.raycast(
        { x: 0, y: 0, z: 0 },
        { x: 1, y: 0, z: 0 },
        10
      )
      
      expect(mockWorld.castRay).toHaveBeenCalled()
      expect(result).toEqual({
        hit: true,
        distance: 5.0,
        point: { x: 5, y: 0, z: 0 },
        normal: { x: -1, y: 0, z: 0 },
        entity: null
      })
    })
    
    it('should return no hit when ray misses', () => {
      mockWorld.castRay.mockReturnValue(null)
      
      const result = physicsWorld.raycast(
        { x: 0, y: 0, z: 0 },
        { x: 1, y: 0, z: 0 },
        10
      )
      
      expect(result.hit).toBe(false)
    })
  })
  
  describe('World Configuration', () => {
    it('should set gravity', () => {
      physicsWorld.setGravity({ x: 0, y: -20, z: 0 })
      
      expect(mockWorld.setGravity).toHaveBeenCalledWith({ x: 0, y: -20, z: 0 })
    })
    
    it('should get current gravity', () => {
      const gravity = physicsWorld.getGravity()
      
      expect(gravity).toEqual({ x: 0, y: -9.81, z: 0 })
    })
    
    it('should set fixed timestep', () => {
      physicsWorld.setFixedTimestep(1/120)
      
      const timestep = physicsWorld.getFixedTimestep()
      expect(timestep).toBe(1/120)
    })
  })
  
  describe('Performance', () => {
    it('should handle many rigid bodies efficiently', () => {
      const startTime = performance.now()
      
      // 创建大量刚体
      for (let i = 0; i < 100; i++) {
        const entity = i as Entity
        physicsWorld.createRigidBody(entity, {
          type: 'dynamic',
          position: { x: i, y: 0, z: 0 }
        })
        
        physicsWorld.createCollider(entity, {
          shape: 'box',
          size: { x: 1, y: 1, z: 1 }
        })
      }
      
      const creationTime = performance.now() - startTime
      expect(creationTime).toBeLessThan(100) // 应该在100ms内完成
    })
    
    it('should maintain stable performance during simulation', () => {
      // 创建一些刚体
      for (let i = 0; i < 10; i++) {
        const entity = i as Entity
        physicsWorld.createRigidBody(entity, {
          type: 'dynamic',
          position: { x: i, y: 10, z: 0 }
        })
        
        physicsWorld.createCollider(entity, {
          shape: 'box',
          size: { x: 1, y: 1, z: 1 }
        })
      }
      
      const startTime = performance.now()
      
      // 运行多个物理步骤
      for (let i = 0; i < 60; i++) {
        physicsWorld.step(1/60)
      }
      
      const simulationTime = performance.now() - startTime
      expect(simulationTime).toBeLessThan(50) // 60帧应该在50ms内完成
    })
  })
})
