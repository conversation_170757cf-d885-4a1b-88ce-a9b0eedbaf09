/**
 * DL-Engine 构建脚本
 * 支持多种构建模式和优化选项
 */

import { execSync } from 'child_process'
import { existsSync, rmSync, mkdirSync, writeFileSync, readFileSync } from 'fs'
import { join } from 'path'
import { performance } from 'perf_hooks'

interface BuildOptions {
  mode: 'development' | 'production' | 'test'
  target: 'lib' | 'app' | 'docs'
  analyze: boolean
  clean: boolean
  sourcemap: boolean
  minify: boolean
  watch: boolean
  verbose: boolean
}

interface BuildResult {
  success: boolean
  duration: number
  outputSize: number
  errors: string[]
  warnings: string[]
}

/**
 * 构建管理器
 */
class BuildManager {
  private options: BuildOptions
  private startTime: number = 0
  
  constructor(options: Partial<BuildOptions> = {}) {
    this.options = {
      mode: 'production',
      target: 'lib',
      analyze: false,
      clean: true,
      sourcemap: true,
      minify: true,
      watch: false,
      verbose: false,
      ...options
    }
  }
  
  /**
   * 执行构建
   */
  async build(): Promise<BuildResult> {
    this.startTime = performance.now()
    
    console.log('🚀 Starting DL-Engine build...')
    console.log(`Mode: ${this.options.mode}`)
    console.log(`Target: ${this.options.target}`)
    console.log(`Analyze: ${this.options.analyze}`)
    
    try {
      // 1. 清理输出目录
      if (this.options.clean) {
        await this.cleanOutput()
      }
      
      // 2. 验证环境
      await this.validateEnvironment()
      
      // 3. 构建类型声明
      await this.buildTypes()
      
      // 4. 执行主构建
      await this.runMainBuild()
      
      // 5. 后处理
      await this.postProcess()
      
      // 6. 生成构建报告
      const result = await this.generateBuildReport()
      
      console.log('✅ Build completed successfully!')
      console.log(`Duration: ${result.duration.toFixed(2)}ms`)
      console.log(`Output size: ${(result.outputSize / 1024 / 1024).toFixed(2)}MB`)
      
      return result
      
    } catch (error) {
      console.error('❌ Build failed:', error)
      return {
        success: false,
        duration: performance.now() - this.startTime,
        outputSize: 0,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: []
      }
    }
  }
  
  /**
   * 清理输出目录
   */
  private async cleanOutput(): Promise<void> {
    console.log('🧹 Cleaning output directories...')
    
    const dirsToClean = ['dist', 'coverage', '.vite']
    
    for (const dir of dirsToClean) {
      const dirPath = join(process.cwd(), dir)
      if (existsSync(dirPath)) {
        rmSync(dirPath, { recursive: true, force: true })
        if (this.options.verbose) {
          console.log(`   Cleaned: ${dir}`)
        }
      }
    }
  }
  
  /**
   * 验证构建环境
   */
  private async validateEnvironment(): Promise<void> {
    console.log('🔍 Validating build environment...')
    
    // 检查Node.js版本
    const nodeVersion = process.version
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ required, current: ${nodeVersion}`)
    }
    
    // 检查必要的依赖
    const requiredDeps = [
      'three',
      '@dimforge/rapier3d',
      '@hookstate/core',
      'vite'
    ]
    
    const packageJson = JSON.parse(readFileSync('package.json', 'utf-8'))
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    }
    
    for (const dep of requiredDeps) {
      if (!allDeps[dep]) {
        throw new Error(`Required dependency missing: ${dep}`)
      }
    }
    
    if (this.options.verbose) {
      console.log('   Environment validation passed')
    }
  }
  
  /**
   * 构建TypeScript类型声明
   */
  private async buildTypes(): Promise<void> {
    console.log('📝 Building TypeScript declarations...')
    
    try {
      execSync('npx tsc --emitDeclarationOnly --outDir dist/types', {
        stdio: this.options.verbose ? 'inherit' : 'pipe'
      })
      
      if (this.options.verbose) {
        console.log('   TypeScript declarations built')
      }
    } catch (error) {
      console.warn('⚠️  TypeScript declaration build failed, continuing...')
    }
  }
  
  /**
   * 执行主构建
   */
  private async runMainBuild(): Promise<void> {
    console.log('🔨 Running main build...')
    
    const buildCommand = this.getBuildCommand()
    
    try {
      execSync(buildCommand, {
        stdio: this.options.verbose ? 'inherit' : 'pipe',
        env: {
          ...process.env,
          NODE_ENV: this.options.mode,
          ANALYZE: this.options.analyze ? 'true' : '',
          VITE_BUILD_TARGET: this.options.target
        }
      })
      
      if (this.options.verbose) {
        console.log('   Main build completed')
      }
    } catch (error) {
      throw new Error(`Build command failed: ${buildCommand}`)
    }
  }
  
  /**
   * 获取构建命令
   */
  private getBuildCommand(): string {
    let command = 'npx vite build'
    
    if (this.options.mode === 'development') {
      command += ' --mode development'
    }
    
    if (this.options.watch) {
      command += ' --watch'
    }
    
    if (!this.options.minify) {
      command += ' --minify false'
    }
    
    return command
  }
  
  /**
   * 后处理
   */
  private async postProcess(): Promise<void> {
    console.log('⚙️  Running post-processing...')
    
    // 创建package.json for dist
    await this.createDistPackageJson()
    
    // 复制重要文件
    await this.copyImportantFiles()
    
    // 生成模块映射
    await this.generateModuleMap()
    
    if (this.options.verbose) {
      console.log('   Post-processing completed')
    }
  }
  
  /**
   * 创建dist目录的package.json
   */
  private async createDistPackageJson(): Promise<void> {
    const originalPackage = JSON.parse(readFileSync('package.json', 'utf-8'))
    
    const distPackage = {
      name: originalPackage.name,
      version: originalPackage.version,
      description: originalPackage.description,
      main: 'index.cjs.js',
      module: 'index.esm.js',
      types: 'types/index.d.ts',
      exports: {
        '.': {
          import: './index.esm.js',
          require: './index.cjs.js',
          types: './types/index.d.ts'
        },
        './core': {
          import: './core.esm.js',
          require: './core.cjs.js',
          types: './types/engine/core/index.d.ts'
        },
        './ecs': {
          import: './ecs.esm.js',
          require: './ecs.cjs.js',
          types: './types/engine/ecs/index.d.ts'
        },
        './physics': {
          import: './physics.esm.js',
          require: './physics.cjs.js',
          types: './types/engine/physics/index.d.ts'
        },
        './state': {
          import: './state.esm.js',
          require: './state.cjs.js',
          types: './types/engine/state/index.d.ts'
        },
        './xr': {
          import: './xr.esm.js',
          require: './xr.cjs.js',
          types: './types/engine/xr/index.d.ts'
        },
        './ai': {
          import: './ai.esm.js',
          require: './ai.cjs.js',
          types: './types/engine/ai/index.d.ts'
        }
      },
      files: [
        'dist',
        'types',
        '*.js',
        '*.d.ts'
      ],
      peerDependencies: originalPackage.peerDependencies || {},
      keywords: originalPackage.keywords || [],
      author: originalPackage.author,
      license: originalPackage.license,
      repository: originalPackage.repository,
      bugs: originalPackage.bugs,
      homepage: originalPackage.homepage
    }
    
    writeFileSync(
      join('dist', 'package.json'),
      JSON.stringify(distPackage, null, 2)
    )
  }
  
  /**
   * 复制重要文件
   */
  private async copyImportantFiles(): Promise<void> {
    const filesToCopy = ['README.md', 'LICENSE', 'CHANGELOG.md']
    
    for (const file of filesToCopy) {
      if (existsSync(file)) {
        const content = readFileSync(file, 'utf-8')
        writeFileSync(join('dist', file), content)
      }
    }
  }
  
  /**
   * 生成模块映射
   */
  private async generateModuleMap(): Promise<void> {
    const moduleMap = {
      name: 'DL-Engine',
      version: JSON.parse(readFileSync('package.json', 'utf-8')).version,
      buildTime: new Date().toISOString(),
      modules: {
        core: 'Core rendering and scene management',
        ecs: 'Entity Component System',
        physics: 'Physics simulation with Rapier3D',
        state: 'Reactive state management',
        xr: 'WebXR and immersive experiences',
        ai: 'AI integration with Ollama'
      },
      exports: {
        main: 'index.esm.js',
        commonjs: 'index.cjs.js',
        umd: 'index.umd.js',
        types: 'types/index.d.ts'
      }
    }
    
    writeFileSync(
      join('dist', 'module-map.json'),
      JSON.stringify(moduleMap, null, 2)
    )
  }
  
  /**
   * 生成构建报告
   */
  private async generateBuildReport(): Promise<BuildResult> {
    const duration = performance.now() - this.startTime
    let outputSize = 0
    
    // 计算输出大小
    try {
      const { execSync } = require('child_process')
      const sizeOutput = execSync('du -sb dist', { encoding: 'utf-8' })
      outputSize = parseInt(sizeOutput.split('\t')[0])
    } catch (error) {
      // 在Windows上可能失败，使用备用方法
      outputSize = 0
    }
    
    const report: BuildResult = {
      success: true,
      duration,
      outputSize,
      errors: [],
      warnings: []
    }
    
    // 保存构建报告
    writeFileSync(
      join('dist', 'build-report.json'),
      JSON.stringify({
        ...report,
        buildOptions: this.options,
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform
      }, null, 2)
    )
    
    return report
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)
  
  const options: Partial<BuildOptions> = {
    mode: args.includes('--dev') ? 'development' : 'production',
    analyze: args.includes('--analyze'),
    clean: !args.includes('--no-clean'),
    watch: args.includes('--watch'),
    verbose: args.includes('--verbose'),
    minify: !args.includes('--no-minify')
  }
  
  const buildManager = new BuildManager(options)
  const result = await buildManager.build()
  
  if (!result.success) {
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Build script failed:', error)
    process.exit(1)
  })
}

export { BuildManager, main as runBuild }
