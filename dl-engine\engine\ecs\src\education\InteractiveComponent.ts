/**
 * DL-Engine 互动组件
 * 处理教育场景中的交互元素
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 互动类型枚举
 */
export enum InteractionType {
  CLICK = 'click',
  HOVER = 'hover',
  DRAG = 'drag',
  DROP = 'drop',
  TOUCH = 'touch',
  VOICE = 'voice',
  GESTURE = 'gesture',
  GAZE = 'gaze', // VR/AR 注视
  PROXIMITY = 'proximity' // 接近触发
}

/**
 * 互动状态枚举
 */
export enum InteractionState {
  IDLE = 'idle',
  HIGHLIGHTED = 'highlighted',
  ACTIVE = 'active',
  DISABLED = 'disabled',
  COMPLETED = 'completed'
}

/**
 * 互动反馈类型
 */
export enum FeedbackType {
  VISUAL = 'visual',
  AUDIO = 'audio',
  HAPTIC = 'haptic',
  TEXT = 'text'
}

/**
 * 互动反馈配置
 */
export interface InteractionFeedback {
  type: FeedbackType
  content: string | number
  duration: number
  delay: number
  intensity: number
}

/**
 * 互动触发条件
 */
export interface InteractionTrigger {
  type: InteractionType
  requiredInput: string[]
  cooldownMs: number
  maxActivations: number
  currentActivations: number
}

/**
 * 互动奖励
 */
export interface InteractionReward {
  points: number
  experience: number
  items: string[]
  achievements: string[]
}

/**
 * 互动数据
 */
export interface InteractiveData {
  /** 互动ID */
  id: string
  
  /** 互动名称 */
  name: string
  
  /** 互动描述 */
  description: string
  
  /** 互动类型 */
  type: InteractionType
  
  /** 当前状态 */
  state: InteractionState
  
  /** 是否启用 */
  enabled: boolean
  
  /** 是否可见 */
  visible: boolean
  
  /** 触发条件 */
  triggers: InteractionTrigger[]
  
  /** 反馈配置 */
  feedback: InteractionFeedback[]
  
  /** 奖励配置 */
  reward: InteractionReward
  
  /** 互动次数 */
  interactionCount: number
  
  /** 最后互动时间 */
  lastInteractionTime: Date | null
  
  /** 互动持续时间（毫秒） */
  interactionDuration: number
  
  /** 成功率 */
  successRate: number
  
  /** 难度等级 */
  difficulty: number
  
  /** 提示文本 */
  hintText: string
  
  /** 错误提示 */
  errorText: string
  
  /** 成功提示 */
  successText: string
  
  /** 互动区域（3D边界框） */
  interactionBounds: {
    min: { x: number; y: number; z: number }
    max: { x: number; y: number; z: number }
  }
  
  /** 动画配置 */
  animations: {
    idle: string
    highlight: string
    active: string
    success: string
    error: string
  }
  
  /** 音效配置 */
  sounds: {
    hover: string
    click: string
    success: string
    error: string
  }
  
  /** 自定义属性 */
  customProperties: Record<string, any>
}

/**
 * 互动组件
 */
export const InteractiveComponent = defineComponent({
  name: 'Interactive',
  schema: {
    id: '',
    name: '',
    description: '',
    type: InteractionType.CLICK,
    state: InteractionState.IDLE,
    enabled: true,
    visible: true,
    triggers: [] as InteractionTrigger[],
    feedback: [] as InteractionFeedback[],
    reward: {
      points: 0,
      experience: 0,
      items: [],
      achievements: []
    } as InteractionReward,
    interactionCount: 0,
    lastInteractionTime: null as Date | null,
    interactionDuration: 0,
    successRate: 0,
    difficulty: 1,
    hintText: '',
    errorText: '',
    successText: '',
    interactionBounds: {
      min: { x: -1, y: -1, z: -1 },
      max: { x: 1, y: 1, z: 1 }
    },
    animations: {
      idle: '',
      highlight: '',
      active: '',
      success: '',
      error: ''
    },
    sounds: {
      hover: '',
      click: '',
      success: '',
      error: ''
    },
    customProperties: {} as Record<string, any>
  } as InteractiveData,
  
  onAdd: (entity, component) => {
    console.log(`Interactive component added: ${component.name}`)
    component.state = InteractionState.IDLE
    
    // 添加默认触发器
    if (component.triggers.length === 0) {
      component.triggers.push({
        type: component.type,
        requiredInput: [],
        cooldownMs: 100,
        maxActivations: -1, // 无限制
        currentActivations: 0
      })
    }
  },
  
  onRemove: (entity, component) => {
    console.log(`Interactive component removed: ${component.name}`)
  },
  
  onSet: (entity, component) => {
    // 更新成功率
    if (component.interactionCount > 0) {
      // 这里需要根据实际的成功/失败记录来计算
      // 暂时使用简单的计算方式
    }
  }
})

/**
 * 互动工具函数
 */
export const InteractiveUtils = {
  /**
   * 触发互动
   */
  triggerInteraction: (interactive: InteractiveData, inputType: InteractionType): boolean => {
    if (!interactive.enabled || interactive.state === InteractionState.DISABLED) {
      return false
    }
    
    // 检查触发条件
    const trigger = interactive.triggers.find(t => t.type === inputType)
    if (!trigger) {
      return false
    }
    
    // 检查冷却时间
    if (interactive.lastInteractionTime) {
      const timeSinceLastInteraction = Date.now() - interactive.lastInteractionTime.getTime()
      if (timeSinceLastInteraction < trigger.cooldownMs) {
        return false
      }
    }
    
    // 检查最大激活次数
    if (trigger.maxActivations > 0 && trigger.currentActivations >= trigger.maxActivations) {
      return false
    }
    
    // 执行互动
    interactive.state = InteractionState.ACTIVE
    interactive.interactionCount++
    interactive.lastInteractionTime = new Date()
    trigger.currentActivations++
    
    console.log(`Interaction triggered: ${interactive.name}`)
    return true
  },
  
  /**
   * 设置互动状态
   */
  setState: (interactive: InteractiveData, state: InteractionState): void => {
    interactive.state = state
  },
  
  /**
   * 启用/禁用互动
   */
  setEnabled: (interactive: InteractiveData, enabled: boolean): void => {
    interactive.enabled = enabled
    if (!enabled) {
      interactive.state = InteractionState.DISABLED
    } else if (interactive.state === InteractionState.DISABLED) {
      interactive.state = InteractionState.IDLE
    }
  },
  
  /**
   * 添加反馈
   */
  addFeedback: (interactive: InteractiveData, feedback: InteractionFeedback): void => {
    interactive.feedback.push(feedback)
  },
  
  /**
   * 播放反馈
   */
  playFeedback: (interactive: InteractiveData, feedbackType: FeedbackType): void => {
    const feedback = interactive.feedback.filter(f => f.type === feedbackType)
    feedback.forEach(f => {
      // 这里会触发实际的反馈播放逻辑
      console.log(`Playing ${f.type} feedback: ${f.content}`)
    })
  },
  
  /**
   * 重置互动
   */
  reset: (interactive: InteractiveData): void => {
    interactive.state = InteractionState.IDLE
    interactive.interactionCount = 0
    interactive.lastInteractionTime = null
    interactive.triggers.forEach(trigger => {
      trigger.currentActivations = 0
    })
  },
  
  /**
   * 检查点是否在互动区域内
   */
  isPointInBounds: (interactive: InteractiveData, point: { x: number; y: number; z: number }): boolean => {
    const bounds = interactive.interactionBounds
    return (
      point.x >= bounds.min.x && point.x <= bounds.max.x &&
      point.y >= bounds.min.y && point.y <= bounds.max.y &&
      point.z >= bounds.min.z && point.z <= bounds.max.z
    )
  },
  
  /**
   * 获取互动统计
   */
  getStats: (interactive: InteractiveData) => ({
    interactionCount: interactive.interactionCount,
    successRate: interactive.successRate,
    lastInteractionTime: interactive.lastInteractionTime,
    state: interactive.state,
    enabled: interactive.enabled,
    difficulty: interactive.difficulty
  })
}
