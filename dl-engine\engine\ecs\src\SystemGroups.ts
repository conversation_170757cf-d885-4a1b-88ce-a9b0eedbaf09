/**
 * DL-Engine 系统组定义
 * 定义系统执行的分组和顺序
 */

import { defineSystem } from './SystemFunctions'

/**
 * 系统组类型
 */
export enum SystemGroupType {
  INPUT = 'Input',
  SIMULATION = 'Simulation', 
  ANIMATION = 'Animation',
  PRESENTATION = 'Presentation'
}

/**
 * 系统组接口
 */
export interface SystemGroup {
  name: string
  type: SystemGroupType
  priority: number
  systems: Set<string>
  enabled: boolean
}

/**
 * 输入系统组
 * 处理用户输入、设备输入等
 */
export const InputSystemGroup: SystemGroup = {
  name: 'InputSystemGroup',
  type: SystemGroupType.INPUT,
  priority: 0,
  systems: new Set(),
  enabled: true
}

/**
 * 模拟系统组
 * 处理物理模拟、游戏逻辑等
 */
export const SimulationSystemGroup: SystemGroup = {
  name: 'SimulationSystemGroup', 
  type: SystemGroupType.SIMULATION,
  priority: 1,
  systems: new Set(),
  enabled: true
}

/**
 * 动画系统组
 * 处理动画更新、插值等
 */
export const AnimationSystemGroup: SystemGroup = {
  name: 'AnimationSystemGroup',
  type: SystemGroupType.ANIMATION,
  priority: 2,
  systems: new Set(),
  enabled: true
}

/**
 * 表现系统组
 * 处理渲染、音频输出等
 */
export const PresentationSystemGroup: SystemGroup = {
  name: 'PresentationSystemGroup',
  type: SystemGroupType.PRESENTATION,
  priority: 3,
  systems: new Set(),
  enabled: true
}

/**
 * 所有系统组
 */
export const SystemGroups = {
  InputSystemGroup,
  SimulationSystemGroup,
  AnimationSystemGroup,
  PresentationSystemGroup
} as const

/**
 * 系统组数组（按优先级排序）
 */
export const SystemGroupsArray: SystemGroup[] = [
  InputSystemGroup,
  SimulationSystemGroup,
  AnimationSystemGroup,
  PresentationSystemGroup
]

/**
 * 系统组映射表
 */
export const SystemGroupMap = new Map<string, SystemGroup>([
  [InputSystemGroup.name, InputSystemGroup],
  [SimulationSystemGroup.name, SimulationSystemGroup],
  [AnimationSystemGroup.name, AnimationSystemGroup],
  [PresentationSystemGroup.name, PresentationSystemGroup]
])

/**
 * 获取系统组
 */
export function getSystemGroup(name: string): SystemGroup | undefined {
  return SystemGroupMap.get(name)
}

/**
 * 添加系统到系统组
 */
export function addSystemToGroup(systemName: string, group: SystemGroup): void {
  group.systems.add(systemName)
}

/**
 * 从系统组移除系统
 */
export function removeSystemFromGroup(systemName: string, group: SystemGroup): void {
  group.systems.delete(systemName)
}

/**
 * 启用系统组
 */
export function enableSystemGroup(group: SystemGroup): void {
  group.enabled = true
}

/**
 * 禁用系统组
 */
export function disableSystemGroup(group: SystemGroup): void {
  group.enabled = false
}

/**
 * 获取启用的系统组
 */
export function getEnabledSystemGroups(): SystemGroup[] {
  return SystemGroupsArray.filter(group => group.enabled)
}

/**
 * 按优先级执行系统组
 */
export function executeSystemGroups(deltaTime: number): void {
  const enabledGroups = getEnabledSystemGroups()
  
  for (const group of enabledGroups) {
    if (group.systems.size > 0) {
      console.debug(`Executing ${group.name} with ${group.systems.size} systems`)
      // 这里会调用具体的系统执行逻辑
    }
  }
}

/**
 * 获取系统组统计信息
 */
export function getSystemGroupStats() {
  return SystemGroupsArray.map(group => ({
    name: group.name,
    type: group.type,
    priority: group.priority,
    systemCount: group.systems.size,
    enabled: group.enabled
  }))
}

/**
 * 重置所有系统组
 */
export function resetSystemGroups(): void {
  SystemGroupsArray.forEach(group => {
    group.systems.clear()
    group.enabled = true
  })
}

/**
 * 教育模式系统组配置
 */
export function configureEducationMode(): void {
  // 在教育模式下可能需要特殊的系统组配置
  console.log('Configuring system groups for education mode')
  
  // 例如：降低某些系统的优先级，启用教育特定系统等
}

/**
 * 性能模式系统组配置
 */
export function configurePerformanceMode(): void {
  // 在性能模式下优化系统组配置
  console.log('Configuring system groups for performance mode')
  
  // 例如：禁用某些非必要系统，调整执行频率等
}
