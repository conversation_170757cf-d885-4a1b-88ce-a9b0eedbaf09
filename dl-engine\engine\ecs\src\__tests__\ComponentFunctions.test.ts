/**
 * DL-Engine 组件函数测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  defineComponent,
  addComponent,
  removeComponent,
  getComponent,
  hasComponent,
  setComponent,
  getAllComponents,
  ComponentMap
} from '../ComponentFunctions'
import { createEntity, removeEntity } from '../EntityFunctions'

// 测试组件定义
const TestComponent = defineComponent({
  name: 'TestComponent',
  schema: {
    value: 0,
    name: '',
    active: true
  }
})

const AnotherComponent = defineComponent({
  name: 'AnotherComponent',
  schema: {
    x: 0,
    y: 0,
    z: 0
  }
})

describe('ComponentFunctions', () => {
  let testEntity: number
  
  beforeEach(() => {
    testEntity = createEntity()
    // 清理组件映射
    ComponentMap.clear()
  })
  
  afterEach(() => {
    removeEntity(testEntity)
  })
  
  describe('defineComponent', () => {
    it('should define a component with schema', () => {
      const component = defineComponent({
        name: 'NewComponent',
        schema: {
          id: 0,
          data: ''
        }
      })
      
      expect(component).toBeDefined()
      expect(typeof component).toBe('symbol')
    })
    
    it('should define component with lifecycle hooks', () => {
      const onAdd = vi.fn()
      const onRemove = vi.fn()
      const onSet = vi.fn()
      
      const component = defineComponent({
        name: 'LifecycleComponent',
        schema: { value: 0 },
        onAdd,
        onRemove,
        onSet
      })
      
      expect(component).toBeDefined()
    })
    
    it('should throw error for duplicate component names', () => {
      defineComponent({
        name: 'DuplicateComponent',
        schema: { value: 0 }
      })
      
      expect(() => {
        defineComponent({
          name: 'DuplicateComponent',
          schema: { value: 1 }
        })
      }).toThrow('Component DuplicateComponent already exists')
    })
  })
  
  describe('addComponent', () => {
    it('should add component to entity', () => {
      const componentData = { value: 42, name: 'test', active: true }
      
      addComponent(testEntity, TestComponent, componentData)
      
      expect(hasComponent(testEntity, TestComponent)).toBe(true)
      
      const retrieved = getComponent(testEntity, TestComponent)
      expect(retrieved).toEqual(componentData)
    })
    
    it('should call onAdd lifecycle hook', () => {
      const onAdd = vi.fn()
      
      const LifecycleComponent = defineComponent({
        name: 'LifecycleAddComponent',
        schema: { value: 0 },
        onAdd
      })
      
      const componentData = { value: 10 }
      addComponent(testEntity, LifecycleComponent, componentData)
      
      expect(onAdd).toHaveBeenCalledWith(testEntity, componentData)
    })
    
    it('should overwrite existing component', () => {
      const initialData = { value: 1, name: 'initial', active: true }
      const newData = { value: 2, name: 'updated', active: false }
      
      addComponent(testEntity, TestComponent, initialData)
      addComponent(testEntity, TestComponent, newData)
      
      const retrieved = getComponent(testEntity, TestComponent)
      expect(retrieved).toEqual(newData)
    })
  })
  
  describe('removeComponent', () => {
    beforeEach(() => {
      addComponent(testEntity, TestComponent, { value: 42, name: 'test', active: true })
    })
    
    it('should remove component from entity', () => {
      expect(hasComponent(testEntity, TestComponent)).toBe(true)
      
      removeComponent(testEntity, TestComponent)
      
      expect(hasComponent(testEntity, TestComponent)).toBe(false)
      expect(getComponent(testEntity, TestComponent)).toBeUndefined()
    })
    
    it('should call onRemove lifecycle hook', () => {
      const onRemove = vi.fn()
      
      const LifecycleComponent = defineComponent({
        name: 'LifecycleRemoveComponent',
        schema: { value: 0 },
        onRemove
      })
      
      const componentData = { value: 10 }
      addComponent(testEntity, LifecycleComponent, componentData)
      
      removeComponent(testEntity, LifecycleComponent)
      
      expect(onRemove).toHaveBeenCalledWith(testEntity, componentData)
    })
    
    it('should handle removing non-existent component gracefully', () => {
      const entity2 = createEntity()
      
      expect(() => {
        removeComponent(entity2, TestComponent)
      }).not.toThrow()
      
      removeEntity(entity2)
    })
  })
  
  describe('getComponent', () => {
    it('should return component data', () => {
      const componentData = { value: 42, name: 'test', active: true }
      addComponent(testEntity, TestComponent, componentData)
      
      const retrieved = getComponent(testEntity, TestComponent)
      expect(retrieved).toEqual(componentData)
    })
    
    it('should return undefined for non-existent component', () => {
      const retrieved = getComponent(testEntity, TestComponent)
      expect(retrieved).toBeUndefined()
    })
  })
  
  describe('hasComponent', () => {
    it('should return true for existing component', () => {
      addComponent(testEntity, TestComponent, { value: 42, name: 'test', active: true })
      
      expect(hasComponent(testEntity, TestComponent)).toBe(true)
    })
    
    it('should return false for non-existent component', () => {
      expect(hasComponent(testEntity, TestComponent)).toBe(false)
    })
  })
  
  describe('setComponent', () => {
    beforeEach(() => {
      addComponent(testEntity, TestComponent, { value: 42, name: 'test', active: true })
    })
    
    it('should update existing component', () => {
      const newData = { value: 100, name: 'updated', active: false }
      
      setComponent(testEntity, TestComponent, newData)
      
      const retrieved = getComponent(testEntity, TestComponent)
      expect(retrieved).toEqual(newData)
    })
    
    it('should call onSet lifecycle hook', () => {
      const onSet = vi.fn()
      
      const LifecycleComponent = defineComponent({
        name: 'LifecycleSetComponent',
        schema: { value: 0 },
        onSet
      })
      
      addComponent(testEntity, LifecycleComponent, { value: 10 })
      const newData = { value: 20 }
      setComponent(testEntity, LifecycleComponent, newData)
      
      expect(onSet).toHaveBeenCalledWith(testEntity, newData)
    })
    
    it('should create component if it does not exist', () => {
      const entity2 = createEntity()
      const componentData = { value: 50, name: 'new', active: true }
      
      setComponent(entity2, TestComponent, componentData)
      
      expect(hasComponent(entity2, TestComponent)).toBe(true)
      expect(getComponent(entity2, TestComponent)).toEqual(componentData)
      
      removeEntity(entity2)
    })
  })
  
  describe('getAllComponents', () => {
    it('should return all components for entity', () => {
      const testData = { value: 42, name: 'test', active: true }
      const anotherData = { x: 1, y: 2, z: 3 }
      
      addComponent(testEntity, TestComponent, testData)
      addComponent(testEntity, AnotherComponent, anotherData)
      
      const allComponents = getAllComponents(testEntity)
      
      expect(allComponents.size).toBe(2)
      expect(allComponents.get(TestComponent)).toEqual(testData)
      expect(allComponents.get(AnotherComponent)).toEqual(anotherData)
    })
    
    it('should return empty map for entity with no components', () => {
      const allComponents = getAllComponents(testEntity)
      
      expect(allComponents.size).toBe(0)
    })
  })
  
  describe('Component Schema Validation', () => {
    it('should validate component data against schema', () => {
      const validData = { value: 42, name: 'test', active: true }
      
      expect(() => {
        addComponent(testEntity, TestComponent, validData)
      }).not.toThrow()
    })
    
    it('should handle partial component data', () => {
      const partialData = { value: 42 }
      
      addComponent(testEntity, TestComponent, partialData)
      
      const retrieved = getComponent(testEntity, TestComponent)
      expect(retrieved.value).toBe(42)
      expect(retrieved.name).toBe('')  // 默认值
      expect(retrieved.active).toBe(true)  // 默认值
    })
  })
  
  describe('Memory Management', () => {
    it('should clean up component data when entity is removed', () => {
      addComponent(testEntity, TestComponent, { value: 42, name: 'test', active: true })
      
      expect(hasComponent(testEntity, TestComponent)).toBe(true)
      
      removeEntity(testEntity)
      
      // 创建新实体来测试清理
      const newEntity = createEntity()
      expect(hasComponent(newEntity, TestComponent)).toBe(false)
      
      testEntity = newEntity  // 更新测试实体引用
    })
    
    it('should handle multiple components per entity efficiently', () => {
      const startTime = performance.now()
      
      // 添加多个组件
      for (let i = 0; i < 1000; i++) {
        const entity = createEntity()
        addComponent(entity, TestComponent, { value: i, name: `test${i}`, active: true })
        addComponent(entity, AnotherComponent, { x: i, y: i * 2, z: i * 3 })
        removeEntity(entity)
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // 性能测试：1000个实体的组件操作应该在合理时间内完成
      expect(duration).toBeLessThan(1000)  // 1秒内完成
    })
  })
})
