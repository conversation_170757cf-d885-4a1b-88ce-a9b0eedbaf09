/**
 * DL-Engine 定时器系统
 * 高精度定时器和时间管理
 */

/**
 * 获取当前高精度时间（毫秒）
 */
export function nowMilliseconds(): number {
  return performance.now()
}

/**
 * 获取当前时间戳（毫秒）
 */
export function nowTimestamp(): number {
  return Date.now()
}

/**
 * 定时器接口
 */
export interface Timer {
  /** 启动定时器 */
  start(): void
  
  /** 停止定时器 */
  stop(): void
  
  /** 暂停定时器 */
  pause(): void
  
  /** 恢复定时器 */
  resume(): void
  
  /** 清理定时器 */
  clear(): void
  
  /** 是否正在运行 */
  isRunning(): boolean
  
  /** 是否暂停 */
  isPaused(): boolean
  
  /** 获取已运行时间 */
  getElapsedTime(): number
  
  /** 重置定时器 */
  reset(): void
}

/**
 * 高精度定时器实现
 */
export class HighResolutionTimer implements Timer {
  private startTime: number = 0
  private pauseTime: number = 0
  private totalPausedTime: number = 0
  private running: boolean = false
  private paused: boolean = false

  /**
   * 启动定时器
   */
  start(): void {
    if (this.running) {
      return
    }
    
    this.startTime = nowMilliseconds()
    this.totalPausedTime = 0
    this.running = true
    this.paused = false
  }

  /**
   * 停止定时器
   */
  stop(): void {
    this.running = false
    this.paused = false
  }

  /**
   * 暂停定时器
   */
  pause(): void {
    if (!this.running || this.paused) {
      return
    }
    
    this.pauseTime = nowMilliseconds()
    this.paused = true
  }

  /**
   * 恢复定时器
   */
  resume(): void {
    if (!this.running || !this.paused) {
      return
    }
    
    this.totalPausedTime += nowMilliseconds() - this.pauseTime
    this.paused = false
  }

  /**
   * 清理定时器
   */
  clear(): void {
    this.stop()
    this.reset()
  }

  /**
   * 是否正在运行
   */
  isRunning(): boolean {
    return this.running
  }

  /**
   * 是否暂停
   */
  isPaused(): boolean {
    return this.paused
  }

  /**
   * 获取已运行时间（毫秒）
   */
  getElapsedTime(): number {
    if (!this.running) {
      return 0
    }
    
    const currentTime = nowMilliseconds()
    const elapsed = currentTime - this.startTime - this.totalPausedTime
    
    if (this.paused) {
      return elapsed - (currentTime - this.pauseTime)
    }
    
    return elapsed
  }

  /**
   * 重置定时器
   */
  reset(): void {
    this.startTime = 0
    this.pauseTime = 0
    this.totalPausedTime = 0
  }
}

/**
 * 帧率限制定时器
 */
export class FrameRateTimer implements Timer {
  private timer: HighResolutionTimer
  private targetFPS: number
  private frameInterval: number
  private lastFrameTime: number = 0
  private frameCount: number = 0
  private actualFPS: number = 0
  private fpsUpdateInterval: number = 1000 // 1秒更新一次FPS
  private lastFPSUpdate: number = 0

  constructor(targetFPS: number = 60) {
    this.timer = new HighResolutionTimer()
    this.setTargetFPS(targetFPS)
  }

  /**
   * 设置目标帧率
   */
  setTargetFPS(fps: number): void {
    this.targetFPS = Math.max(1, Math.min(240, fps))
    this.frameInterval = 1000 / this.targetFPS
  }

  /**
   * 获取目标帧率
   */
  getTargetFPS(): number {
    return this.targetFPS
  }

  /**
   * 获取实际帧率
   */
  getActualFPS(): number {
    return this.actualFPS
  }

  /**
   * 检查是否应该渲染下一帧
   */
  shouldRenderFrame(): boolean {
    const currentTime = nowMilliseconds()
    const deltaTime = currentTime - this.lastFrameTime
    
    if (deltaTime >= this.frameInterval) {
      this.lastFrameTime = currentTime
      this.frameCount++
      
      // 更新实际FPS
      if (currentTime - this.lastFPSUpdate >= this.fpsUpdateInterval) {
        this.actualFPS = this.frameCount / ((currentTime - this.lastFPSUpdate) / 1000)
        this.frameCount = 0
        this.lastFPSUpdate = currentTime
      }
      
      return true
    }
    
    return false
  }

  /**
   * 获取帧间隔时间
   */
  getFrameInterval(): number {
    return this.frameInterval
  }

  /**
   * 获取距离下一帧的时间
   */
  getTimeToNextFrame(): number {
    const currentTime = nowMilliseconds()
    const deltaTime = currentTime - this.lastFrameTime
    return Math.max(0, this.frameInterval - deltaTime)
  }

  // Timer接口实现
  start(): void {
    this.timer.start()
    this.lastFrameTime = nowMilliseconds()
    this.lastFPSUpdate = this.lastFrameTime
    this.frameCount = 0
  }

  stop(): void {
    this.timer.stop()
  }

  pause(): void {
    this.timer.pause()
  }

  resume(): void {
    this.timer.resume()
    this.lastFrameTime = nowMilliseconds()
  }

  clear(): void {
    this.timer.clear()
    this.frameCount = 0
    this.actualFPS = 0
  }

  isRunning(): boolean {
    return this.timer.isRunning()
  }

  isPaused(): boolean {
    return this.timer.isPaused()
  }

  getElapsedTime(): number {
    return this.timer.getElapsedTime()
  }

  reset(): void {
    this.timer.reset()
    this.frameCount = 0
    this.actualFPS = 0
    this.lastFrameTime = 0
    this.lastFPSUpdate = 0
  }
}

/**
 * 时间工具函数
 */
export const TimeUtils = {
  /**
   * 创建高精度定时器
   */
  createTimer: (): Timer => new HighResolutionTimer(),

  /**
   * 创建帧率定时器
   */
  createFrameRateTimer: (targetFPS?: number): FrameRateTimer => new FrameRateTimer(targetFPS),

  /**
   * 延迟执行
   */
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 等待下一帧
   */
  nextFrame: (): Promise<number> => {
    return new Promise(resolve => requestAnimationFrame(resolve))
  },

  /**
   * 格式化时间
   */
  formatTime: (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000) % 60
    const minutes = Math.floor(milliseconds / (1000 * 60)) % 60
    const hours = Math.floor(milliseconds / (1000 * 60 * 60))

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  },

  /**
   * 格式化毫秒
   */
  formatMilliseconds: (milliseconds: number): string => {
    if (milliseconds < 1000) {
      return `${milliseconds.toFixed(2)}ms`
    } else if (milliseconds < 60000) {
      return `${(milliseconds / 1000).toFixed(2)}s`
    } else {
      return TimeUtils.formatTime(milliseconds)
    }
  }
}
