/**
 * DL-Engine 通用工具函数
 */

import { v4 as uuidv4 } from 'uuid'
import { Vector3, Quaternion, Transform } from '../types'
import { MATH_CONSTANTS, VECTOR3_ZERO, QUATERNION_IDENTITY } from '../constants'

// UUID 生成
export const generateUUID = (): string => uuidv4()

// 数学工具函数
export const MathUtils = {
  /**
   * 角度转弧度
   */
  degToRad: (degrees: number): number => degrees * MATH_CONSTANTS.DEG_TO_RAD,

  /**
   * 弧度转角度
   */
  radToDeg: (radians: number): number => radians * MATH_CONSTANTS.RAD_TO_DEG,

  /**
   * 限制数值在指定范围内
   */
  clamp: (value: number, min: number, max: number): number => 
    Math.max(min, Math.min(max, value)),

  /**
   * 线性插值
   */
  lerp: (a: number, b: number, t: number): number => a + (b - a) * t,

  /**
   * 检查两个数值是否近似相等
   */
  approximately: (a: number, b: number, epsilon = MATH_CONSTANTS.EPSILON): boolean =>
    Math.abs(a - b) < epsilon,

  /**
   * 生成指定范围内的随机数
   */
  randomRange: (min: number, max: number): number => 
    Math.random() * (max - min) + min,

  /**
   * 生成指定范围内的随机整数
   */
  randomInt: (min: number, max: number): number =>
    Math.floor(Math.random() * (max - min + 1)) + min
}

// 向量工具函数
export const Vector3Utils = {
  /**
   * 创建新的Vector3
   */
  create: (x = 0, y = 0, z = 0): Vector3 => ({ x, y, z }),

  /**
   * 复制Vector3
   */
  clone: (v: Vector3): Vector3 => ({ x: v.x, y: v.y, z: v.z }),

  /**
   * 向量加法
   */
  add: (a: Vector3, b: Vector3): Vector3 => ({
    x: a.x + b.x,
    y: a.y + b.y,
    z: a.z + b.z
  }),

  /**
   * 向量减法
   */
  subtract: (a: Vector3, b: Vector3): Vector3 => ({
    x: a.x - b.x,
    y: a.y - b.y,
    z: a.z - b.z
  }),

  /**
   * 向量数乘
   */
  multiply: (v: Vector3, scalar: number): Vector3 => ({
    x: v.x * scalar,
    y: v.y * scalar,
    z: v.z * scalar
  }),

  /**
   * 向量点积
   */
  dot: (a: Vector3, b: Vector3): number => a.x * b.x + a.y * b.y + a.z * b.z,

  /**
   * 向量叉积
   */
  cross: (a: Vector3, b: Vector3): Vector3 => ({
    x: a.y * b.z - a.z * b.y,
    y: a.z * b.x - a.x * b.z,
    z: a.x * b.y - a.y * b.x
  }),

  /**
   * 向量长度
   */
  length: (v: Vector3): number => Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z),

  /**
   * 向量长度的平方
   */
  lengthSquared: (v: Vector3): number => v.x * v.x + v.y * v.y + v.z * v.z,

  /**
   * 向量归一化
   */
  normalize: (v: Vector3): Vector3 => {
    const length = Vector3Utils.length(v)
    if (length === 0) return { ...VECTOR3_ZERO }
    return Vector3Utils.multiply(v, 1 / length)
  },

  /**
   * 向量距离
   */
  distance: (a: Vector3, b: Vector3): number => 
    Vector3Utils.length(Vector3Utils.subtract(a, b)),

  /**
   * 向量线性插值
   */
  lerp: (a: Vector3, b: Vector3, t: number): Vector3 => ({
    x: MathUtils.lerp(a.x, b.x, t),
    y: MathUtils.lerp(a.y, b.y, t),
    z: MathUtils.lerp(a.z, b.z, t)
  }),

  /**
   * 检查两个向量是否近似相等
   */
  approximately: (a: Vector3, b: Vector3, epsilon = MATH_CONSTANTS.EPSILON): boolean =>
    MathUtils.approximately(a.x, b.x, epsilon) &&
    MathUtils.approximately(a.y, b.y, epsilon) &&
    MathUtils.approximately(a.z, b.z, epsilon)
}

// 四元数工具函数
export const QuaternionUtils = {
  /**
   * 创建新的Quaternion
   */
  create: (x = 0, y = 0, z = 0, w = 1): Quaternion => ({ x, y, z, w }),

  /**
   * 复制Quaternion
   */
  clone: (q: Quaternion): Quaternion => ({ x: q.x, y: q.y, z: q.z, w: q.w }),

  /**
   * 四元数乘法
   */
  multiply: (a: Quaternion, b: Quaternion): Quaternion => ({
    x: a.w * b.x + a.x * b.w + a.y * b.z - a.z * b.y,
    y: a.w * b.y + a.y * b.w + a.z * b.x - a.x * b.z,
    z: a.w * b.z + a.z * b.w + a.x * b.y - a.y * b.x,
    w: a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z
  }),

  /**
   * 四元数归一化
   */
  normalize: (q: Quaternion): Quaternion => {
    const length = Math.sqrt(q.x * q.x + q.y * q.y + q.z * q.z + q.w * q.w)
    if (length === 0) return { ...QUATERNION_IDENTITY }
    return {
      x: q.x / length,
      y: q.y / length,
      z: q.z / length,
      w: q.w / length
    }
  },

  /**
   * 从欧拉角创建四元数
   */
  fromEuler: (x: number, y: number, z: number): Quaternion => {
    const cx = Math.cos(x * 0.5)
    const sx = Math.sin(x * 0.5)
    const cy = Math.cos(y * 0.5)
    const sy = Math.sin(y * 0.5)
    const cz = Math.cos(z * 0.5)
    const sz = Math.sin(z * 0.5)

    return {
      x: sx * cy * cz - cx * sy * sz,
      y: cx * sy * cz + sx * cy * sz,
      z: cx * cy * sz - sx * sy * cz,
      w: cx * cy * cz + sx * sy * sz
    }
  }
}

// 变换工具函数
export const TransformUtils = {
  /**
   * 创建新的Transform
   */
  create: (
    position: Vector3 = VECTOR3_ZERO,
    rotation: Quaternion = QUATERNION_IDENTITY,
    scale: Vector3 = { x: 1, y: 1, z: 1 }
  ): Transform => ({
    position: Vector3Utils.clone(position),
    rotation: QuaternionUtils.clone(rotation),
    scale: Vector3Utils.clone(scale)
  }),

  /**
   * 复制Transform
   */
  clone: (transform: Transform): Transform => ({
    position: Vector3Utils.clone(transform.position),
    rotation: QuaternionUtils.clone(transform.rotation),
    scale: Vector3Utils.clone(transform.scale)
  })
}

// 时间工具函数
export const TimeUtils = {
  /**
   * 获取当前时间戳（毫秒）
   */
  now: (): number => Date.now(),

  /**
   * 获取高精度时间戳
   */
  nowHighRes: (): number => performance.now(),

  /**
   * 格式化时间
   */
  formatTime: (timestamp: number, format = 'YYYY-MM-DD HH:mm:ss'): string => {
    const date = new Date(timestamp)
    return format
      .replace('YYYY', date.getFullYear().toString())
      .replace('MM', (date.getMonth() + 1).toString().padStart(2, '0'))
      .replace('DD', date.getDate().toString().padStart(2, '0'))
      .replace('HH', date.getHours().toString().padStart(2, '0'))
      .replace('mm', date.getMinutes().toString().padStart(2, '0'))
      .replace('ss', date.getSeconds().toString().padStart(2, '0'))
  }
}

// 字符串工具函数
export const StringUtils = {
  /**
   * 首字母大写
   */
  capitalize: (str: string): string => 
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),

  /**
   * 驼峰命名转换
   */
  toCamelCase: (str: string): string =>
    str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase()),

  /**
   * 短横线命名转换
   */
  toKebabCase: (str: string): string =>
    str.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, ''),

  /**
   * 生成随机字符串
   */
  randomString: (length: number, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string => {
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

// 对象工具函数
export const ObjectUtils = {
  /**
   * 深度克隆对象
   */
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
    if (obj instanceof Array) return obj.map(item => ObjectUtils.deepClone(item)) as unknown as T
    
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = ObjectUtils.deepClone(obj[key])
      }
    }
    return cloned
  },

  /**
   * 检查对象是否为空
   */
  isEmpty: (obj: any): boolean => {
    if (obj === null || obj === undefined) return true
    if (typeof obj === 'string' || Array.isArray(obj)) return obj.length === 0
    if (typeof obj === 'object') return Object.keys(obj).length === 0
    return false
  }
}
