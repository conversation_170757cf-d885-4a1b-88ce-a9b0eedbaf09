/**
 * DL-Engine 教育组件模块
 * 导出所有教育场景专用组件
 */

// 学习进度组件
export * from './LearningProgressComponent'

// 互动组件
export * from './InteractiveComponent'

// 评估组件
export * from './AssessmentComponent'

// 教育组件工具函数
export const EducationComponents = {
  LearningProgress: 'LearningProgress',
  Interactive: 'Interactive',
  Assessment: 'Assessment'
} as const

/**
 * 教育组件类型联合
 */
export type EducationComponentType = typeof EducationComponents[keyof typeof EducationComponents]

/**
 * 教育模块初始化
 */
export function initializeEducationComponents(): void {
  console.log('🎓 Education components initialized')
  console.log('Available components:', Object.values(EducationComponents))
}

/**
 * 教育组件统计
 */
export function getEducationComponentStats() {
  return {
    totalComponents: Object.keys(EducationComponents).length,
    components: Object.values(EducationComponents)
  }
}
