/**
 * DL-Engine 通用类型定义
 */

// 基础类型
export type EntityID = string
export type ComponentID = string
export type SystemID = string
export type UserID = string
export type ProjectID = string
export type SceneID = string

// 数学类型
export interface Vector3 {
  x: number
  y: number
  z: number
}

export interface Quaternion {
  x: number
  y: number
  z: number
  w: number
}

export interface Transform {
  position: Vector3
  rotation: Quaternion
  scale: Vector3
}

// 网络类型
export interface NetworkMessage {
  type: string
  data: any
  timestamp: number
  sender?: UserID
}

// 教育相关类型
export interface Course {
  id: string
  title: string
  description: string
  createdAt: Date
  updatedAt: Date
  authorId: UserID
}

export interface Assignment {
  id: string
  courseId: string
  title: string
  description: string
  dueDate: Date
  maxScore: number
}

export interface Assessment {
  id: string
  assignmentId: string
  studentId: UserID
  score: number
  submittedAt: Date
  feedback?: string
}

// 用户类型
export interface User {
  id: UserID
  phone: string
  username: string
  email?: string
  avatar?: string
  role: 'student' | 'teacher' | 'admin'
  createdAt: Date
  lastLoginAt?: Date
}

// 项目类型
export interface Project {
  id: ProjectID
  name: string
  description: string
  ownerId: UserID
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
}

// 场景类型
export interface Scene {
  id: SceneID
  projectId: ProjectID
  name: string
  data: any // 场景数据
  thumbnail?: string
  createdAt: Date
  updatedAt: Date
}

// 资产类型
export interface Asset {
  id: string
  name: string
  type: 'model' | 'texture' | 'audio' | 'video' | 'image'
  url: string
  size: number
  mimeType: string
  uploadedBy: UserID
  uploadedAt: Date
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 分页类型
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}
