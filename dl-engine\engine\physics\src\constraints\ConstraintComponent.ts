/**
 * DL-Engine 约束组件
 * 为实体添加物理约束功能
 */

import { defineComponent } from '@dl-engine/engine-ecs'
import { ConstraintConfig, ConstraintType } from './ConstraintSystem'

/**
 * 约束组件数据
 */
export interface ConstraintComponentData {
  /** 约束ID */
  constraintId: string | null
  
  /** 约束配置 */
  config: ConstraintConfig
  
  /** 是否自动创建约束 */
  autoCreate: boolean
  
  /** 是否已创建 */
  created: boolean
  
  /** 创建时间 */
  createdAt: Date | null
  
  /** 错误信息 */
  error: string | null
}

/**
 * 约束组件
 */
export const ConstraintComponent = defineComponent({
  name: 'Constraint',
  schema: {
    constraintId: null as string | null,
    config: {
      type: ConstraintType.FIXED,
      entityA: 0 as any,
      entityB: 0 as any,
      anchorA: { x: 0, y: 0, z: 0 },
      anchorB: { x: 0, y: 0, z: 0 },
      enableCollision: false,
      strength: 1.0,
      breakable: false,
      breakForce: 1000
    } as ConstraintConfig,
    autoCreate: true,
    created: false,
    createdAt: null as Date | null,
    error: null as string | null
  } as ConstraintComponentData,
  
  onAdd: (entity, component) => {
    console.log(`Constraint component added to entity ${entity}`)
    
    if (component.autoCreate) {
      // 延迟创建，等待物理系统初始化
      setTimeout(() => {
        // TODO: 调用约束系统创建约束
        console.log('Auto-creating constraint...')
      }, 100)
    }
  },
  
  onRemove: (entity, component) => {
    console.log(`Constraint component removed from entity ${entity}`)
    
    if (component.constraintId) {
      // TODO: 调用约束系统移除约束
      console.log(`Removing constraint ${component.constraintId}`)
    }
  },
  
  onSet: (entity, component) => {
    // 当组件数据更新时的处理
    if (component.config && !component.created && component.autoCreate) {
      // 尝试创建约束
      console.log('Attempting to create constraint from updated config')
    }
  }
})

/**
 * 约束组件工具函数
 */
export const ConstraintComponentUtils = {
  /**
   * 创建固定约束配置
   */
  createFixedConstraintConfig: (
    entityA: any,
    entityB: any,
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ): ConstraintConfig => ({
    type: ConstraintType.FIXED,
    entityA,
    entityB,
    anchorA,
    anchorB,
    enableCollision: false,
    strength: 1.0,
    breakable: false,
    breakForce: 1000
  }),
  
  /**
   * 创建旋转约束配置
   */
  createRevoluteConstraintConfig: (
    entityA: any,
    entityB: any,
    axis = { x: 0, y: 1, z: 0 },
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ) => ({
    type: ConstraintType.REVOLUTE,
    entityA,
    entityB,
    anchorA,
    anchorB,
    axis,
    enableCollision: false,
    strength: 1.0,
    breakable: false,
    breakForce: 1000
  }),
  
  /**
   * 创建滑动约束配置
   */
  createPrismaticConstraintConfig: (
    entityA: any,
    entityB: any,
    axis = { x: 1, y: 0, z: 0 },
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ) => ({
    type: ConstraintType.PRISMATIC,
    entityA,
    entityB,
    anchorA,
    anchorB,
    axis,
    enableCollision: false,
    strength: 1.0,
    breakable: false,
    breakForce: 1000
  }),
  
  /**
   * 创建球形约束配置
   */
  createSphericalConstraintConfig: (
    entityA: any,
    entityB: any,
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ): ConstraintConfig => ({
    type: ConstraintType.SPHERICAL,
    entityA,
    entityB,
    anchorA,
    anchorB,
    enableCollision: false,
    strength: 1.0,
    breakable: false,
    breakForce: 1000
  }),
  
  /**
   * 创建绳索约束配置
   */
  createRopeConstraintConfig: (
    entityA: any,
    entityB: any,
    length: number,
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ) => ({
    type: ConstraintType.ROPE,
    entityA,
    entityB,
    anchorA,
    anchorB,
    length,
    stiffness: 1.0,
    damping: 0.1,
    enableCollision: false,
    strength: 1.0,
    breakable: true,
    breakForce: 500
  }),
  
  /**
   * 创建弹簧约束配置
   */
  createSpringConstraintConfig: (
    entityA: any,
    entityB: any,
    restLength: number,
    stiffness: number = 100,
    damping: number = 10,
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ) => ({
    type: ConstraintType.SPRING,
    entityA,
    entityB,
    anchorA,
    anchorB,
    restLength,
    stiffness,
    damping,
    maxCompression: 0.5,
    maxExtension: 2.0,
    enableCollision: false,
    strength: 1.0,
    breakable: true,
    breakForce: 800
  }),
  
  /**
   * 创建距离约束配置
   */
  createDistanceConstraintConfig: (
    entityA: any,
    entityB: any,
    distance: number,
    tolerance: number = 0.1,
    anchorA = { x: 0, y: 0, z: 0 },
    anchorB = { x: 0, y: 0, z: 0 }
  ): ConstraintConfig => ({
    type: ConstraintType.DISTANCE,
    entityA,
    entityB,
    anchorA,
    anchorB,
    distance,
    tolerance,
    enableCollision: false,
    strength: 1.0,
    breakable: false,
    breakForce: 1000
  }),
  
  /**
   * 验证约束配置
   */
  validateConstraintConfig: (config: ConstraintConfig): { valid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (!config.entityA || !config.entityB) {
      errors.push('Both entityA and entityB must be specified')
    }
    
    if (config.entityA === config.entityB) {
      errors.push('entityA and entityB cannot be the same entity')
    }
    
    if (config.strength <= 0) {
      errors.push('Constraint strength must be positive')
    }
    
    if (config.breakable && config.breakForce <= 0) {
      errors.push('Break force must be positive for breakable constraints')
    }
    
    // 类型特定验证
    switch (config.type) {
      case ConstraintType.ROPE:
        const ropeConfig = config as any
        if (ropeConfig.length <= 0) {
          errors.push('Rope length must be positive')
        }
        break
        
      case ConstraintType.SPRING:
        const springConfig = config as any
        if (springConfig.restLength <= 0) {
          errors.push('Spring rest length must be positive')
        }
        if (springConfig.stiffness <= 0) {
          errors.push('Spring stiffness must be positive')
        }
        break
        
      case ConstraintType.DISTANCE:
        const distanceConfig = config as any
        if (distanceConfig.distance <= 0) {
          errors.push('Distance must be positive')
        }
        break
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  },
  
  /**
   * 获取约束类型的默认配置
   */
  getDefaultConfigForType: (type: ConstraintType): Partial<ConstraintConfig> => {
    const base = {
      enableCollision: false,
      strength: 1.0,
      breakable: false,
      breakForce: 1000
    }
    
    switch (type) {
      case ConstraintType.REVOLUTE:
        return {
          ...base,
          axis: { x: 0, y: 1, z: 0 }
        }
        
      case ConstraintType.PRISMATIC:
        return {
          ...base,
          axis: { x: 1, y: 0, z: 0 }
        }
        
      case ConstraintType.ROPE:
        return {
          ...base,
          length: 1.0,
          stiffness: 1.0,
          damping: 0.1,
          breakable: true,
          breakForce: 500
        }
        
      case ConstraintType.SPRING:
        return {
          ...base,
          restLength: 1.0,
          stiffness: 100,
          damping: 10,
          maxCompression: 0.5,
          maxExtension: 2.0,
          breakable: true,
          breakForce: 800
        }
        
      case ConstraintType.DISTANCE:
        return {
          ...base,
          distance: 1.0,
          tolerance: 0.1
        }
        
      default:
        return base
    }
  }
}
