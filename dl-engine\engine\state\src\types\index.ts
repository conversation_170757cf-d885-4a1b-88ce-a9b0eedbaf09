/**
 * DL-Engine 状态管理类型定义
 */

import { State } from '@hookstate/core'
import { Identifiable } from '@hookstate/identifiable'

/**
 * 不透明类型工具
 */
export type OpaqueType<K> = K & { readonly __opaque__: unique symbol }

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends (infer U)[]
    ? DeepReadonlyArray<U>
    : T[P] extends object
    ? DeepReadonly<T[P]>
    : T[P]
}

export interface DeepReadonlyArray<T> extends ReadonlyArray<DeepReadonly<T>> {}

/**
 * 用户ID类型
 */
export type UserID = OpaqueType<'UserID'> & string

/**
 * 对等节点ID类型
 */
export type PeerID = OpaqueType<'PeerID'> & string

/**
 * 网络ID类型
 */
export type NetworkID = OpaqueType<'NetworkID'> & string

/**
 * 主题类型
 */
export type Topic = OpaqueType<'Topic'> & string

/**
 * 动作队列句柄
 */
export type ActionQueueHandle = OpaqueType<'ActionQueueHandle'> & string

/**
 * 动作选项
 */
export interface ActionOptions {
  /** 动作UUID */
  $uuid?: string
  
  /** 动作时间戳 */
  $time?: number
  
  /** 动作主题 */
  $topic?: Topic
  
  /** 动作来源 */
  $from?: PeerID
  
  /** 动作目标 */
  $to?: PeerID | PeerID[]
  
  /** 动作缓存 */
  $cache?: boolean
  
  /** 动作延迟 */
  $delay?: number
  
  /** 动作堆栈跟踪 */
  $stack?: string[]
}

/**
 * 基础动作接口
 */
export interface Action {
  /** 动作类型 */
  type: string | string[]
}

/**
 * 已解析的动作类型
 */
export type ResolvedActionType<A extends Action = Action> = A & Required<ActionOptions>

/**
 * 动作形状
 */
export type ActionShape<A extends Action> = {
  [K in keyof A]: any
}

/**
 * 动作接收器
 */
export interface ActionReceptor<A extends Action = Action> {
  (action: ResolvedActionType<A>): void
  matchesAction: any
  validator?: (action: ResolvedActionType<A>) => boolean
}

/**
 * 接收器映射
 */
export type ReceptorMap = Record<string, ActionReceptor<any>>

/**
 * 状态定义
 */
export interface StateDefinition<S, I = {}, E = {}, Receptors extends ReceptorMap = {}> {
  /** 状态名称 */
  name: string
  
  /** 初始状态 */
  initial: (() => S) | S
  
  /** 状态扩展 */
  extension?: any
  
  /** 动作接收器 */
  receptors?: Receptors
  
  /** 接收器动作队列 */
  receptorActionQueue?: ActionQueueHandle
  
  /** 状态反应器 */
  reactor?: any
}

/**
 * 状态存储接口
 */
export interface StateStore {
  /** 状态映射 */
  stateMap: Record<string, State<any, any>>
  
  /** 状态反应器 */
  stateReactors: Record<string, any>
}

/**
 * 动作队列实例
 */
export interface ActionQueueInstance {
  /** 队列句柄 */
  handle: ActionQueueHandle
  
  /** 队列函数 */
  queue: () => ResolvedActionType[]
  
  /** 是否自动清理 */
  autoClear: boolean
}

/**
 * 动作存储
 */
export interface ActionStore {
  /** 所有队列 */
  queues: Map<ActionQueueHandle, ActionQueueInstance>
  
  /** 缓存的动作 */
  cached: Array<ResolvedActionType>
  
  /** 传入的动作 */
  incoming: Array<ResolvedActionType>
  
  /** 动作历史 */
  history: Array<ResolvedActionType>
  
  /** 已知的UUID */
  knownUUIDs: Set<string>
  
  /** 传出的动作 */
  outgoing: Record<Topic, {
    queue: Array<ResolvedActionType>
    history: Array<ResolvedActionType>
    forwardedUUIDs: Set<string>
  }>
}

/**
 * DL-Engine存储接口
 */
export interface DLEngineStore extends StateStore {
  /** 默认主题 */
  defaultTopic: Topic
  
  /** 转发主题 */
  forwardingTopics: Set<Topic>
  
  /** 对等节点ID */
  peerID: PeerID
  
  /** 对等节点索引 */
  peerIndex: number
  
  /** 获取调度时间 */
  getDispatchTime: () => number
  
  /** 获取当前反应器根 */
  getCurrentReactorRoot: () => any
  
  /** 默认调度延迟 */
  defaultDispatchDelay: () => number
  
  /** 获取代理ID */
  getAgentID: () => UserID
  
  /** 动作存储 */
  actions: ActionStore
  
  /** 日志记录器 */
  logger: (name: string) => any
}

/**
 * 网络状态
 */
export interface NetworkState {
  /** 是否已连接 */
  connected: boolean
  
  /** 连接类型 */
  connectionType: 'websocket' | 'webrtc' | 'offline'
  
  /** 延迟 */
  latency: number
  
  /** 带宽 */
  bandwidth: number
  
  /** 对等节点列表 */
  peers: Map<PeerID, PeerInfo>
  
  /** 网络统计 */
  stats: NetworkStats
}

/**
 * 对等节点信息
 */
export interface PeerInfo {
  /** 对等节点ID */
  id: PeerID
  
  /** 用户ID */
  userID: UserID
  
  /** 连接状态 */
  connected: boolean
  
  /** 延迟 */
  latency: number
  
  /** 最后活动时间 */
  lastActivity: number
}

/**
 * 网络统计
 */
export interface NetworkStats {
  /** 发送的消息数 */
  messagesSent: number
  
  /** 接收的消息数 */
  messagesReceived: number
  
  /** 发送的字节数 */
  bytesSent: number
  
  /** 接收的字节数 */
  bytesReceived: number
  
  /** 连接时间 */
  connectionTime: number
}

/**
 * 持久化选项
 */
export interface PersistenceOptions {
  /** 存储键 */
  key: string
  
  /** 存储类型 */
  storage: 'localStorage' | 'sessionStorage' | 'indexedDB'
  
  /** 序列化函数 */
  serialize?: (value: any) => string
  
  /** 反序列化函数 */
  deserialize?: (value: string) => any
  
  /** 要持久化的键 */
  keys?: string[]
  
  /** 过期时间（毫秒） */
  ttl?: number
}

/**
 * 状态同步选项
 */
export interface StateSyncOptions {
  /** 是否启用网络同步 */
  networkSync: boolean
  
  /** 同步间隔（毫秒） */
  syncInterval: number
  
  /** 冲突解决策略 */
  conflictResolution: 'client' | 'server' | 'timestamp' | 'custom'
  
  /** 自定义冲突解决函数 */
  customResolver?: (local: any, remote: any) => any
}

/**
 * 反应器根
 */
export interface ReactorRoot {
  /** 运行反应器 */
  run: () => void
  
  /** 停止反应器 */
  stop: () => void
  
  /** 是否正在运行 */
  isRunning: boolean
}

/**
 * 状态变更事件
 */
export interface StateChangeEvent<T = any> {
  /** 状态名称 */
  stateName: string
  
  /** 变更路径 */
  path: string[]
  
  /** 旧值 */
  oldValue: T
  
  /** 新值 */
  newValue: T
  
  /** 时间戳 */
  timestamp: number
}
