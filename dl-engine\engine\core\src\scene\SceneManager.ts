/**
 * DL-Engine 场景管理器
 * 管理3D场景、场景切换、层级管理和优化
 */

import {
  Scene,
  Object3D,
  Group,
  Camera,
  Light,
  Mesh,
  Material,
  Geometry,
  BufferGeometry,
  Fog,
  FogExp2,
  Color,
  Vector3,
  Box3,
  Sphere
} from 'three'

import { Entity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * 场景层级类型
 */
export type SceneLayerType = 
  | 'background'
  | 'environment'
  | 'static'
  | 'dynamic'
  | 'interactive'
  | 'ui'
  | 'effects'
  | 'debug'

/**
 * 场景层级
 */
export interface SceneLayer {
  /** 层级名称 */
  name: string
  
  /** 层级类型 */
  type: SceneLayerType
  
  /** 层级组 */
  group: Group
  
  /** 是否可见 */
  visible: boolean
  
  /** 渲染顺序 */
  renderOrder: number
  
  /** 是否启用视锥剔除 */
  frustumCulling: boolean
  
  /** 是否启用距离剔除 */
  distanceCulling: boolean
  
  /** 距离剔除阈值 */
  cullingDistance: number
  
  /** 对象计数 */
  objectCount: number
}

/**
 * 场景配置
 */
export interface SceneConfig {
  /** 场景名称 */
  name: string
  
  /** 背景颜色 */
  backgroundColor?: Color | string | number
  
  /** 背景纹理 */
  backgroundTexture?: string
  
  /** 环境贴图 */
  environmentMap?: string
  
  /** 雾效配置 */
  fog?: {
    type: 'linear' | 'exponential'
    color: Color | string | number
    near?: number
    far?: number
    density?: number
  }
  
  /** 环境光配置 */
  ambientLight?: {
    color: Color | string | number
    intensity: number
  }
  
  /** 物理配置 */
  physics?: {
    gravity: Vector3
    enabled: boolean
  }
  
  /** 优化配置 */
  optimization?: {
    enableFrustumCulling: boolean
    enableDistanceCulling: boolean
    maxDrawCalls: number
    maxTriangles: number
    lodEnabled: boolean
  }
}

/**
 * 场景实例
 */
export interface SceneInstance {
  /** 场景ID */
  id: string
  
  /** 场景名称 */
  name: string
  
  /** Three.js场景对象 */
  scene: Scene
  
  /** 场景配置 */
  config: SceneConfig
  
  /** 场景层级 */
  layers: Map<string, SceneLayer>
  
  /** 实体对象映射 */
  entityObjects: Map<Entity, Object3D>
  
  /** 对象实体映射 */
  objectEntities: Map<Object3D, Entity>
  
  /** 是否激活 */
  active: boolean
  
  /** 是否已加载 */
  loaded: boolean
  
  /** 包围盒 */
  boundingBox: Box3
  
  /** 包围球 */
  boundingSphere: Sphere
  
  /** 统计信息 */
  stats: {
    objectCount: number
    triangleCount: number
    drawCalls: number
    memoryUsage: number
  }
  
  /** 创建时间 */
  createdAt: number
  
  /** 最后更新时间 */
  lastUpdated: number
}

/**
 * 场景管理器状态
 */
export interface SceneManagerState {
  /** 场景实例 */
  scenes: Map<string, SceneInstance>
  
  /** 当前激活场景 */
  activeScene: string | null
  
  /** 场景切换状态 */
  transitioning: boolean
  
  /** 全局配置 */
  globalConfig: {
    /** 默认层级配置 */
    defaultLayers: SceneLayerType[]
    
    /** 自动优化 */
    autoOptimization: boolean
    
    /** 统计更新间隔 */
    statsUpdateInterval: number
    
    /** 内存限制 */
    memoryLimit: number
  }
  
  /** 全局统计 */
  globalStats: {
    totalScenes: number
    totalObjects: number
    totalTriangles: number
    totalMemoryUsage: number
  }
}

/**
 * 场景管理器状态定义
 */
export const SceneManagerState = defineState({
  name: 'DLEngine.SceneManager',
  initial: (): SceneManagerState => ({
    scenes: new Map(),
    activeScene: null,
    transitioning: false,
    globalConfig: {
      defaultLayers: ['background', 'environment', 'static', 'dynamic', 'interactive', 'ui', 'effects'],
      autoOptimization: true,
      statsUpdateInterval: 1000,
      memoryLimit: 512 * 1024 * 1024 // 512MB
    },
    globalStats: {
      totalScenes: 0,
      totalObjects: 0,
      totalTriangles: 0,
      totalMemoryUsage: 0
    }
  }),
  
  receptors: {
    /**
     * 添加场景
     */
    addScene: (state, scene: SceneInstance) => {
      state.scenes.get(NO_PROXY).set(scene.id, scene)
      state.globalStats.totalScenes.set(state.globalStats.totalScenes.value + 1)
    },
    
    /**
     * 移除场景
     */
    removeScene: (state, id: string) => {
      state.scenes.get(NO_PROXY).delete(id)
      state.globalStats.totalScenes.set(Math.max(0, state.globalStats.totalScenes.value - 1))
    },
    
    /**
     * 设置激活场景
     */
    setActiveScene: (state, sceneId: string | null) => {
      state.activeScene.set(sceneId)
    },
    
    /**
     * 设置切换状态
     */
    setTransitioning: (state, transitioning: boolean) => {
      state.transitioning.set(transitioning)
    },
    
    /**
     * 更新全局配置
     */
    updateGlobalConfig: (state, config: Partial<SceneManagerState['globalConfig']>) => {
      state.globalConfig.merge(config)
    },
    
    /**
     * 更新全局统计
     */
    updateGlobalStats: (state, stats: Partial<SceneManagerState['globalStats']>) => {
      state.globalStats.merge(stats)
    }
  }
})

// 导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * 场景管理器
 */
export class SceneManager {
  private static instance: SceneManager | null = null
  private statsTimer: number | null = null
  
  /**
   * 获取单例实例
   */
  static getInstance(): SceneManager {
    if (!SceneManager.instance) {
      SceneManager.instance = new SceneManager()
    }
    return SceneManager.instance
  }
  
  constructor() {
    this.startStatsTimer()
  }
  
  /**
   * 创建场景
   */
  createScene(config: SceneConfig): string {
    const sceneId = this.generateSceneId()
    const scene = new Scene()
    
    // 应用场景配置
    this.applySceneConfig(scene, config)
    
    // 创建默认层级
    const layers = this.createDefaultLayers()
    
    // 添加层级到场景
    for (const layer of layers.values()) {
      scene.add(layer.group)
    }
    
    // 创建场景实例
    const sceneInstance: SceneInstance = {
      id: sceneId,
      name: config.name,
      scene,
      config,
      layers,
      entityObjects: new Map(),
      objectEntities: new Map(),
      active: false,
      loaded: true,
      boundingBox: new Box3(),
      boundingSphere: new Sphere(),
      stats: {
        objectCount: 0,
        triangleCount: 0,
        drawCalls: 0,
        memoryUsage: 0
      },
      createdAt: Date.now(),
      lastUpdated: Date.now()
    }
    
    // 添加到状态
    getMutableState(SceneManagerState).addScene(sceneInstance)
    
    console.log(`Scene created: ${sceneId} (${config.name})`)
    return sceneId
  }
  
  /**
   * 应用场景配置
   */
  private applySceneConfig(scene: Scene, config: SceneConfig): void {
    // 设置背景
    if (config.backgroundColor) {
      scene.background = new Color(config.backgroundColor)
    }
    
    // 设置雾效
    if (config.fog) {
      const fogColor = new Color(config.fog.color)
      
      if (config.fog.type === 'linear') {
        scene.fog = new Fog(fogColor, config.fog.near || 1, config.fog.far || 1000)
      } else {
        scene.fog = new FogExp2(fogColor, config.fog.density || 0.00025)
      }
    }
    
    // TODO: 设置环境贴图、背景纹理等
  }
  
  /**
   * 创建默认层级
   */
  private createDefaultLayers(): Map<string, SceneLayer> {
    const layers = new Map<string, SceneLayer>()
    const defaultLayers = getState(SceneManagerState).globalConfig.defaultLayers
    
    defaultLayers.forEach((layerType, index) => {
      const layer: SceneLayer = {
        name: layerType,
        type: layerType,
        group: new Group(),
        visible: true,
        renderOrder: index,
        frustumCulling: true,
        distanceCulling: layerType !== 'ui' && layerType !== 'debug',
        cullingDistance: this.getDefaultCullingDistance(layerType),
        objectCount: 0
      }
      
      layer.group.name = `Layer_${layerType}`
      layer.group.renderOrder = index
      
      layers.set(layerType, layer)
    })
    
    return layers
  }
  
  /**
   * 获取默认剔除距离
   */
  private getDefaultCullingDistance(layerType: SceneLayerType): number {
    switch (layerType) {
      case 'background':
        return Infinity
      case 'environment':
        return 1000
      case 'static':
        return 500
      case 'dynamic':
        return 200
      case 'interactive':
        return 100
      case 'effects':
        return 50
      default:
        return 100
    }
  }
  
  /**
   * 激活场景
   */
  activateScene(sceneId: string): boolean {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      console.warn(`Scene not found: ${sceneId}`)
      return false
    }
    
    // 停用当前场景
    const currentSceneId = getState(SceneManagerState).activeScene
    if (currentSceneId) {
      const currentScene = getState(SceneManagerState).scenes.get(currentSceneId)
      if (currentScene) {
        currentScene.active = false
      }
    }
    
    // 激活新场景
    scene.active = true
    getMutableState(SceneManagerState).setActiveScene(sceneId)
    
    console.log(`Scene activated: ${sceneId}`)
    return true
  }
  
  /**
   * 添加对象到场景
   */
  addObject(sceneId: string, object: Object3D, layerType: SceneLayerType = 'dynamic', entity?: Entity): boolean {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      return false
    }
    
    const layer = scene.layers.get(layerType)
    if (!layer) {
      console.warn(`Layer not found: ${layerType}`)
      return false
    }
    
    // 添加到层级
    layer.group.add(object)
    layer.objectCount++
    
    // 更新映射
    if (entity) {
      scene.entityObjects.set(entity, object)
      scene.objectEntities.set(object, entity)
    }
    
    // 更新统计
    scene.stats.objectCount++
    scene.lastUpdated = Date.now()
    
    // 更新包围盒
    this.updateBounds(scene)
    
    return true
  }
  
  /**
   * 从场景移除对象
   */
  removeObject(sceneId: string, object: Object3D): boolean {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      return false
    }
    
    // 查找对象所在的层级
    let foundLayer: SceneLayer | null = null
    for (const layer of scene.layers.values()) {
      if (layer.group.children.includes(object)) {
        foundLayer = layer
        break
      }
    }
    
    if (!foundLayer) {
      return false
    }
    
    // 从层级移除
    foundLayer.group.remove(object)
    foundLayer.objectCount--
    
    // 更新映射
    const entity = scene.objectEntities.get(object)
    if (entity) {
      scene.entityObjects.delete(entity)
      scene.objectEntities.delete(object)
    }
    
    // 更新统计
    scene.stats.objectCount--
    scene.lastUpdated = Date.now()
    
    return true
  }
  
  /**
   * 获取场景对象
   */
  getSceneObject(sceneId: string, entity: Entity): Object3D | null {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      return null
    }
    
    return scene.entityObjects.get(entity) || null
  }
  
  /**
   * 设置层级可见性
   */
  setLayerVisibility(sceneId: string, layerType: SceneLayerType, visible: boolean): boolean {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      return false
    }
    
    const layer = scene.layers.get(layerType)
    if (!layer) {
      return false
    }
    
    layer.visible = visible
    layer.group.visible = visible
    
    return true
  }
  
  /**
   * 更新场景包围盒
   */
  private updateBounds(scene: SceneInstance): void {
    scene.boundingBox.makeEmpty()
    
    scene.scene.traverse((object) => {
      if (object instanceof Mesh) {
        const geometry = object.geometry
        if (geometry.boundingBox) {
          const box = geometry.boundingBox.clone()
          box.applyMatrix4(object.matrixWorld)
          scene.boundingBox.union(box)
        }
      }
    })
    
    scene.boundingBox.getBoundingSphere(scene.boundingSphere)
  }
  
  /**
   * 优化场景
   */
  optimizeScene(sceneId: string): void {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      return
    }
    
    // 合并几何体
    this.mergeGeometries(scene)
    
    // 实例化重复对象
    this.instanceDuplicates(scene)
    
    // 应用LOD
    this.applyLOD(scene)
    
    console.log(`Scene optimized: ${sceneId}`)
  }
  
  /**
   * 合并几何体
   */
  private mergeGeometries(scene: SceneInstance): void {
    // TODO: 实现几何体合并逻辑
    // 将相同材质的静态几何体合并以减少绘制调用
  }
  
  /**
   * 实例化重复对象
   */
  private instanceDuplicates(scene: SceneInstance): void {
    // TODO: 实现实例化渲染
    // 检测重复的几何体和材质，使用InstancedMesh优化
  }
  
  /**
   * 应用LOD
   */
  private applyLOD(scene: SceneInstance): void {
    // TODO: 实现LOD系统
    // 根据距离自动切换不同细节级别的模型
  }
  
  /**
   * 更新统计信息
   */
  private updateStats(): void {
    const scenes = getState(SceneManagerState).scenes
    let totalObjects = 0
    let totalTriangles = 0
    let totalMemory = 0
    
    for (const scene of scenes.values()) {
      // 更新场景统计
      scene.stats.objectCount = 0
      scene.stats.triangleCount = 0
      scene.stats.drawCalls = 0
      
      scene.scene.traverse((object) => {
        scene.stats.objectCount++
        
        if (object instanceof Mesh) {
          const geometry = object.geometry
          if (geometry instanceof BufferGeometry) {
            const positionAttribute = geometry.getAttribute('position')
            if (positionAttribute) {
              scene.stats.triangleCount += positionAttribute.count / 3
            }
          }
        }
      })
      
      totalObjects += scene.stats.objectCount
      totalTriangles += scene.stats.triangleCount
      totalMemory += scene.stats.memoryUsage
    }
    
    // 更新全局统计
    getMutableState(SceneManagerState).updateGlobalStats({
      totalObjects,
      totalTriangles,
      totalMemoryUsage: totalMemory
    })
  }
  
  /**
   * 开始统计定时器
   */
  private startStatsTimer(): void {
    const interval = getState(SceneManagerState).globalConfig.statsUpdateInterval
    
    this.statsTimer = window.setInterval(() => {
      this.updateStats()
    }, interval)
  }
  
  /**
   * 销毁场景
   */
  destroyScene(sceneId: string): boolean {
    const scene = getState(SceneManagerState).scenes.get(sceneId)
    if (!scene) {
      return false
    }
    
    // 清理场景对象
    scene.scene.traverse((object) => {
      if (object instanceof Mesh) {
        object.geometry.dispose()
        
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose())
        } else {
          object.material.dispose()
        }
      }
    })
    
    // 清理映射
    scene.entityObjects.clear()
    scene.objectEntities.clear()
    
    // 从状态中移除
    getMutableState(SceneManagerState).removeScene(sceneId)
    
    console.log(`Scene destroyed: ${sceneId}`)
    return true
  }
  
  /**
   * 生成场景ID
   */
  private generateSceneId(): string {
    return `scene_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 获取当前激活场景
   */
  getActiveScene(): SceneInstance | null {
    const activeSceneId = getState(SceneManagerState).activeScene
    if (!activeSceneId) {
      return null
    }
    
    return getState(SceneManagerState).scenes.get(activeSceneId) || null
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.statsTimer) {
      clearInterval(this.statsTimer)
      this.statsTimer = null
    }
    
    // 销毁所有场景
    const scenes = getState(SceneManagerState).scenes
    for (const sceneId of scenes.keys()) {
      this.destroyScene(sceneId)
    }
  }
}
