/**
 * DL-Engine 约束系统
 * 实现关节约束、连接器等物理约束功能
 */

import {
  ImpulseJoint,
  JointData,
  JointType,
  RigidBody,
  Vector3 as RapierVector3,
  Rotation as RapierRotation
} from '@dimforge/rapier3d'

import { Entity } from '@dl-engine/engine-ecs'
import { PhysicsVector3, PhysicsQuaternion } from '../types/PhysicsTypes'
import { DLPhysicsWorld } from '../PhysicsWorld'

/**
 * 约束类型枚举
 */
export enum ConstraintType {
  FIXED = 'fixed',
  REVOLUTE = 'revolute',
  PRISMATIC = 'prismatic',
  SPHERICAL = 'spherical',
  ROPE = 'rope',
  SPRING = 'spring',
  DISTANCE = 'distance'
}

/**
 * 约束配置基类
 */
export interface BaseConstraintConfig {
  /** 约束类型 */
  type: ConstraintType
  
  /** 第一个实体 */
  entityA: Entity
  
  /** 第二个实体 */
  entityB: Entity
  
  /** 实体A上的锚点（局部坐标） */
  anchorA: PhysicsVector3
  
  /** 实体B上的锚点（局部坐标） */
  anchorB: PhysicsVector3
  
  /** 是否启用碰撞 */
  enableCollision: boolean
  
  /** 约束强度 */
  strength: number
  
  /** 是否可断裂 */
  breakable: boolean
  
  /** 断裂力阈值 */
  breakForce: number
}

/**
 * 旋转约束配置
 */
export interface RevoluteConstraintConfig extends BaseConstraintConfig {
  type: ConstraintType.REVOLUTE
  
  /** 旋转轴（局部坐标） */
  axis: PhysicsVector3
  
  /** 最小角度限制（弧度） */
  minAngle?: number
  
  /** 最大角度限制（弧度） */
  maxAngle?: number
  
  /** 角速度限制 */
  maxAngularVelocity?: number
  
  /** 马达配置 */
  motor?: {
    enabled: boolean
    targetVelocity: number
    maxTorque: number
  }
}

/**
 * 滑动约束配置
 */
export interface PrismaticConstraintConfig extends BaseConstraintConfig {
  type: ConstraintType.PRISMATIC
  
  /** 滑动轴（局部坐标） */
  axis: PhysicsVector3
  
  /** 最小距离限制 */
  minDistance?: number
  
  /** 最大距离限制 */
  maxDistance?: number
  
  /** 线速度限制 */
  maxLinearVelocity?: number
  
  /** 马达配置 */
  motor?: {
    enabled: boolean
    targetVelocity: number
    maxForce: number
  }
}

/**
 * 球形约束配置
 */
export interface SphericalConstraintConfig extends BaseConstraintConfig {
  type: ConstraintType.SPHERICAL
  
  /** 角度限制 */
  angleLimit?: number
  
  /** 扭转限制 */
  twistLimit?: number
}

/**
 * 绳索约束配置
 */
export interface RopeConstraintConfig extends BaseConstraintConfig {
  type: ConstraintType.ROPE
  
  /** 绳索长度 */
  length: number
  
  /** 绳索刚度 */
  stiffness: number
  
  /** 阻尼 */
  damping: number
}

/**
 * 弹簧约束配置
 */
export interface SpringConstraintConfig extends BaseConstraintConfig {
  type: ConstraintType.SPRING
  
  /** 静止长度 */
  restLength: number
  
  /** 弹簧常数 */
  stiffness: number
  
  /** 阻尼系数 */
  damping: number
  
  /** 最大压缩比 */
  maxCompression: number
  
  /** 最大拉伸比 */
  maxExtension: number
}

/**
 * 距离约束配置
 */
export interface DistanceConstraintConfig extends BaseConstraintConfig {
  type: ConstraintType.DISTANCE
  
  /** 固定距离 */
  distance: number
  
  /** 距离容差 */
  tolerance: number
}

/**
 * 约束配置联合类型
 */
export type ConstraintConfig = 
  | BaseConstraintConfig
  | RevoluteConstraintConfig
  | PrismaticConstraintConfig
  | SphericalConstraintConfig
  | RopeConstraintConfig
  | SpringConstraintConfig
  | DistanceConstraintConfig

/**
 * 约束实例
 */
export interface ConstraintInstance {
  /** 约束ID */
  id: string
  
  /** 约束配置 */
  config: ConstraintConfig
  
  /** Rapier关节实例 */
  joint: ImpulseJoint
  
  /** 创建时间 */
  createdAt: Date
  
  /** 是否激活 */
  active: boolean
  
  /** 当前应力 */
  currentStress: number
  
  /** 是否已断裂 */
  broken: boolean
}

/**
 * 约束系统管理器
 */
export class ConstraintSystemManager {
  private static instance: ConstraintSystemManager | null = null
  private constraints: Map<string, ConstraintInstance> = new Map()
  private nextConstraintId = 1
  
  /**
   * 获取单例实例
   */
  static getInstance(): ConstraintSystemManager {
    if (!ConstraintSystemManager.instance) {
      ConstraintSystemManager.instance = new ConstraintSystemManager()
    }
    return ConstraintSystemManager.instance
  }
  
  /**
   * 创建约束
   */
  createConstraint(
    world: DLPhysicsWorld,
    config: ConstraintConfig
  ): string | null {
    // 获取刚体
    const rigidBodyA = world.rigidBodies.get(config.entityA)
    const rigidBodyB = world.rigidBodies.get(config.entityB)
    
    if (!rigidBodyA || !rigidBodyB) {
      console.warn('Cannot create constraint: one or both entities do not have rigid bodies')
      return null
    }
    
    // 创建关节数据
    const jointData = this.createJointData(config)
    if (!jointData) {
      console.warn('Failed to create joint data for constraint')
      return null
    }
    
    // 创建关节
    const joint = world.createImpulseJoint(jointData, rigidBodyA, rigidBodyB, true)
    
    // 创建约束实例
    const constraintId = `constraint_${this.nextConstraintId++}`
    const constraint: ConstraintInstance = {
      id: constraintId,
      config,
      joint,
      createdAt: new Date(),
      active: true,
      currentStress: 0,
      broken: false
    }
    
    this.constraints.set(constraintId, constraint)
    
    console.log(`Constraint created: ${constraintId} (${config.type})`)
    return constraintId
  }
  
  /**
   * 移除约束
   */
  removeConstraint(world: DLPhysicsWorld, constraintId: string): boolean {
    const constraint = this.constraints.get(constraintId)
    if (!constraint) {
      return false
    }
    
    // 从物理世界移除关节
    world.removeImpulseJoint(constraint.joint, true)
    
    // 从管理器移除
    this.constraints.delete(constraintId)
    
    console.log(`Constraint removed: ${constraintId}`)
    return true
  }
  
  /**
   * 获取约束
   */
  getConstraint(constraintId: string): ConstraintInstance | null {
    return this.constraints.get(constraintId) || null
  }
  
  /**
   * 获取所有约束
   */
  getAllConstraints(): ConstraintInstance[] {
    return Array.from(this.constraints.values())
  }
  
  /**
   * 更新约束系统
   */
  update(deltaTime: number): void {
    for (const constraint of this.constraints.values()) {
      if (!constraint.active || constraint.broken) {
        continue
      }
      
      // 检查约束应力
      this.updateConstraintStress(constraint)
      
      // 检查是否需要断裂
      if (constraint.config.breakable && 
          constraint.currentStress > constraint.config.breakForce) {
        this.breakConstraint(constraint)
      }
    }
  }
  
  /**
   * 创建关节数据
   */
  private createJointData(config: ConstraintConfig): JointData | null {
    const anchorA = new RapierVector3(config.anchorA.x, config.anchorA.y, config.anchorA.z)
    const anchorB = new RapierVector3(config.anchorB.x, config.anchorB.y, config.anchorB.z)
    
    switch (config.type) {
      case ConstraintType.FIXED:
        return JointData.fixed(anchorA, RapierRotation.identity(), anchorB, RapierRotation.identity())
      
      case ConstraintType.REVOLUTE:
        const revoluteConfig = config as RevoluteConstraintConfig
        const axis = new RapierVector3(revoluteConfig.axis.x, revoluteConfig.axis.y, revoluteConfig.axis.z)
        return JointData.revolute(anchorA, anchorB, axis)
      
      case ConstraintType.PRISMATIC:
        const prismaticConfig = config as PrismaticConstraintConfig
        const prismaticAxis = new RapierVector3(prismaticConfig.axis.x, prismaticConfig.axis.y, prismaticConfig.axis.z)
        return JointData.prismatic(anchorA, anchorB, prismaticAxis)
      
      case ConstraintType.SPHERICAL:
        return JointData.spherical(anchorA, anchorB)
      
      case ConstraintType.ROPE:
        const ropeConfig = config as RopeConstraintConfig
        return JointData.rope(ropeConfig.length, anchorA, anchorB)
      
      default:
        console.warn(`Unsupported constraint type: ${config.type}`)
        return null
    }
  }
  
  /**
   * 更新约束应力
   */
  private updateConstraintStress(constraint: ConstraintInstance): void {
    // 这里需要从Rapier获取关节的应力信息
    // 目前使用简化的计算方式
    constraint.currentStress = 0 // TODO: 实现实际的应力计算
  }
  
  /**
   * 断裂约束
   */
  private breakConstraint(constraint: ConstraintInstance): void {
    constraint.broken = true
    constraint.active = false
    
    console.log(`Constraint broken: ${constraint.id} (stress: ${constraint.currentStress})`)
    
    // 触发断裂事件
    // TODO: 实现事件系统
  }
  
  /**
   * 获取约束统计信息
   */
  getStats() {
    const total = this.constraints.size
    const active = Array.from(this.constraints.values()).filter(c => c.active).length
    const broken = Array.from(this.constraints.values()).filter(c => c.broken).length
    
    return {
      total,
      active,
      broken,
      types: this.getConstraintTypeStats()
    }
  }
  
  /**
   * 获取约束类型统计
   */
  private getConstraintTypeStats() {
    const stats: Record<string, number> = {}
    
    for (const constraint of this.constraints.values()) {
      const type = constraint.config.type
      stats[type] = (stats[type] || 0) + 1
    }
    
    return stats
  }
}
