/**
 * DL-Engine 学习进度组件测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  LearningProgressComponent,
  LearningProgressUtils,
  LearningStatus,
  type LearningProgressData,
  type Achievement,
  type LearningObjective
} from '../../education/LearningProgressComponent'
import {
  addComponent,
  removeComponent,
  getComponent,
  hasComponent,
  setComponent
} from '../../ComponentFunctions'
import { createEntity, removeEntity } from '../../EntityFunctions'

describe('LearningProgressComponent', () => {
  let testEntity: number
  
  beforeEach(() => {
    testEntity = createEntity()
  })
  
  afterEach(() => {
    removeEntity(testEntity)
  })
  
  describe('Component Definition', () => {
    it('should define learning progress component with correct schema', () => {
      expect(LearningProgressComponent).toBeDefined()
      expect(typeof LearningProgressComponent).toBe('symbol')
    })
    
    it('should add component with default values', () => {
      const progressData: Partial<LearningProgressData> = {
        learnerId: 'student123',
        courseId: 'course456',
        chapterId: 'chapter789'
      }
      
      addComponent(testEntity, LearningProgressComponent, progressData)
      
      expect(hasComponent(testEntity, LearningProgressComponent)).toBe(true)
      
      const retrieved = getComponent(testEntity, LearningProgressComponent)
      expect(retrieved.learnerId).toBe('student123')
      expect(retrieved.courseId).toBe('course456')
      expect(retrieved.chapterId).toBe('chapter789')
      expect(retrieved.progressPercentage).toBe(0)
      expect(retrieved.status).toBe(LearningStatus.NOT_STARTED)
      expect(retrieved.isCompleted).toBe(false)
    })
  })
  
  describe('Lifecycle Hooks', () => {
    it('should call onAdd hook when component is added', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      const progressData: Partial<LearningProgressData> = {
        learnerId: 'student123',
        courseId: 'course456'
      }
      
      addComponent(testEntity, LearningProgressComponent, progressData)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Learning progress started for learner student123')
      )
      
      consoleSpy.mockRestore()
    })
    
    it('should call onRemove hook when component is removed', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      addComponent(testEntity, LearningProgressComponent, {
        learnerId: 'student123',
        courseId: 'course456'
      })
      
      removeComponent(testEntity, LearningProgressComponent)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Learning progress removed for learner student123')
      )
      
      consoleSpy.mockRestore()
    })
    
    it('should call onSet hook and update progress when component is set', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      addComponent(testEntity, LearningProgressComponent, {
        learnerId: 'student123',
        courseId: 'course456',
        totalTasks: 10,
        completedTasks: 5
      })
      
      const retrieved = getComponent(testEntity, LearningProgressComponent)
      expect(retrieved.progressPercentage).toBe(50)
      
      // 更新为完成状态
      setComponent(testEntity, LearningProgressComponent, {
        ...retrieved,
        completedTasks: 10
      })
      
      const updated = getComponent(testEntity, LearningProgressComponent)
      expect(updated.progressPercentage).toBe(100)
      expect(updated.isCompleted).toBe(true)
      expect(updated.status).toBe(LearningStatus.COMPLETED)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Learning completed for learner student123!')
      )
      
      consoleSpy.mockRestore()
    })
  })
  
  describe('LearningProgressUtils', () => {
    let progressData: LearningProgressData
    
    beforeEach(() => {
      progressData = {
        learnerId: 'student123',
        courseId: 'course456',
        chapterId: 'chapter789',
        progressPercentage: 0,
        completedTasks: 0,
        totalTasks: 10,
        studyTimeMinutes: 0,
        startTime: new Date(),
        lastUpdateTime: new Date(),
        isCompleted: false,
        score: 0,
        status: LearningStatus.NOT_STARTED,
        objectives: [],
        achievements: [],
        notes: [],
        mistakes: [],
        preferences: {
          learningStyle: 'visual',
          preferredLanguage: 'zh-CN',
          difficultyPreference: 'adaptive',
          pace: 'normal',
          hintsEnabled: true,
          soundEnabled: true,
          animationsEnabled: true
        }
      }
    })
    
    describe('updateProgress', () => {
      it('should update progress percentage correctly', () => {
        LearningProgressUtils.updateProgress(progressData, 5)
        
        expect(progressData.completedTasks).toBe(5)
        expect(progressData.progressPercentage).toBe(50)
        expect(progressData.isCompleted).toBe(false)
        expect(progressData.status).toBe(LearningStatus.NOT_STARTED)
      })
      
      it('should mark as completed when progress reaches 100%', () => {
        LearningProgressUtils.updateProgress(progressData, 10)
        
        expect(progressData.completedTasks).toBe(10)
        expect(progressData.progressPercentage).toBe(100)
        expect(progressData.isCompleted).toBe(true)
        expect(progressData.status).toBe(LearningStatus.COMPLETED)
      })
      
      it('should not exceed total tasks', () => {
        LearningProgressUtils.updateProgress(progressData, 15)
        
        expect(progressData.completedTasks).toBe(10)
        expect(progressData.progressPercentage).toBe(100)
      })
    })
    
    describe('addStudyTime', () => {
      it('should add study time correctly', () => {
        const initialTime = progressData.studyTimeMinutes
        
        LearningProgressUtils.addStudyTime(progressData, 30)
        
        expect(progressData.studyTimeMinutes).toBe(initialTime + 30)
        expect(progressData.lastUpdateTime).toBeInstanceOf(Date)
      })
    })
    
    describe('addAchievement', () => {
      it('should add new achievement', () => {
        const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
        
        const achievement: Achievement = {
          id: 'achievement1',
          name: 'First Steps',
          description: 'Complete your first lesson',
          unlockedAt: new Date(),
          points: 10,
          category: 'progress'
        }
        
        LearningProgressUtils.addAchievement(progressData, achievement)
        
        expect(progressData.achievements).toHaveLength(1)
        expect(progressData.achievements[0]).toEqual(achievement)
        expect(consoleSpy).toHaveBeenCalledWith('Achievement unlocked: First Steps')
        
        consoleSpy.mockRestore()
      })
      
      it('should not add duplicate achievement', () => {
        const achievement: Achievement = {
          id: 'achievement1',
          name: 'First Steps',
          description: 'Complete your first lesson',
          unlockedAt: new Date(),
          points: 10,
          category: 'progress'
        }
        
        LearningProgressUtils.addAchievement(progressData, achievement)
        LearningProgressUtils.addAchievement(progressData, achievement)
        
        expect(progressData.achievements).toHaveLength(1)
      })
    })
    
    describe('addObjective and completeObjective', () => {
      it('should add and complete learning objectives', () => {
        const objective: LearningObjective = {
          id: 'obj1',
          title: 'Learn Variables',
          description: 'Understand how to declare and use variables',
          isCompleted: false,
          difficulty: 'easy',
          category: 'programming'
        }
        
        LearningProgressUtils.addObjective(progressData, objective)
        
        expect(progressData.objectives).toHaveLength(1)
        expect(progressData.objectives[0].isCompleted).toBe(false)
        
        LearningProgressUtils.completeObjective(progressData, 'obj1')
        
        expect(progressData.objectives[0].isCompleted).toBe(true)
        expect(progressData.objectives[0].completionTime).toBeInstanceOf(Date)
      })
      
      it('should not complete non-existent objective', () => {
        LearningProgressUtils.completeObjective(progressData, 'nonexistent')
        
        // 应该不会抛出错误
        expect(progressData.objectives).toHaveLength(0)
      })
    })
    
    describe('recordMistake', () => {
      it('should record learning mistakes', () => {
        const mistake = {
          id: 'mistake1',
          questionId: 'q1',
          incorrectAnswer: 'wrong answer',
          correctAnswer: 'correct answer',
          timestamp: new Date(),
          attempts: 1,
          category: 'syntax',
          difficulty: 2
        }
        
        LearningProgressUtils.recordMistake(progressData, mistake)
        
        expect(progressData.mistakes).toHaveLength(1)
        expect(progressData.mistakes[0]).toEqual(mistake)
      })
    })
    
    describe('getStats', () => {
      it('should return correct statistics', () => {
        // 设置测试数据
        progressData.progressPercentage = 75
        progressData.objectives = [
          { id: '1', title: 'Obj 1', description: '', isCompleted: true, difficulty: 'easy', category: 'test' },
          { id: '2', title: 'Obj 2', description: '', isCompleted: false, difficulty: 'medium', category: 'test' }
        ]
        progressData.achievements = [
          { id: '1', name: 'Achievement 1', description: '', unlockedAt: new Date(), points: 10, category: 'progress' }
        ]
        progressData.studyTimeMinutes = 120
        progressData.score = 85
        progressData.mistakes = [
          { id: '1', questionId: 'q1', incorrectAnswer: '', correctAnswer: '', timestamp: new Date(), attempts: 1, category: '', difficulty: 1 }
        ]
        progressData.status = LearningStatus.IN_PROGRESS
        
        const stats = LearningProgressUtils.getStats(progressData)
        
        expect(stats.progressPercentage).toBe(75)
        expect(stats.completedObjectives).toBe(1)
        expect(stats.totalObjectives).toBe(2)
        expect(stats.achievementCount).toBe(1)
        expect(stats.totalStudyTime).toBe(120)
        expect(stats.averageScore).toBe(85)
        expect(stats.mistakeCount).toBe(1)
        expect(stats.status).toBe(LearningStatus.IN_PROGRESS)
      })
    })
  })
  
  describe('Integration Tests', () => {
    it('should handle complete learning workflow', () => {
      // 1. 添加学习进度组件
      addComponent(testEntity, LearningProgressComponent, {
        learnerId: 'student123',
        courseId: 'course456',
        chapterId: 'chapter789',
        totalTasks: 5
      })
      
      let progress = getComponent(testEntity, LearningProgressComponent)
      expect(progress.status).toBe(LearningStatus.NOT_STARTED)
      
      // 2. 开始学习
      progress.status = LearningStatus.IN_PROGRESS
      setComponent(testEntity, LearningProgressComponent, progress)
      
      // 3. 添加学习目标
      const objective: LearningObjective = {
        id: 'obj1',
        title: 'Learn Basics',
        description: 'Understand basic concepts',
        isCompleted: false,
        difficulty: 'easy',
        category: 'fundamentals'
      }
      
      LearningProgressUtils.addObjective(progress, objective)
      
      // 4. 更新进度
      LearningProgressUtils.updateProgress(progress, 3)
      LearningProgressUtils.addStudyTime(progress, 60)
      
      // 5. 完成目标
      LearningProgressUtils.completeObjective(progress, 'obj1')
      
      // 6. 添加成就
      const achievement: Achievement = {
        id: 'ach1',
        name: 'Quick Learner',
        description: 'Complete 3 tasks in 1 hour',
        unlockedAt: new Date(),
        points: 20,
        category: 'speed'
      }
      
      LearningProgressUtils.addAchievement(progress, achievement)
      
      // 7. 完成所有任务
      LearningProgressUtils.updateProgress(progress, 5)
      setComponent(testEntity, LearningProgressComponent, progress)
      
      // 验证最终状态
      const finalProgress = getComponent(testEntity, LearningProgressComponent)
      expect(finalProgress.progressPercentage).toBe(100)
      expect(finalProgress.isCompleted).toBe(true)
      expect(finalProgress.status).toBe(LearningStatus.COMPLETED)
      expect(finalProgress.objectives[0].isCompleted).toBe(true)
      expect(finalProgress.achievements).toHaveLength(1)
      expect(finalProgress.studyTimeMinutes).toBe(60)
    })
  })
})
