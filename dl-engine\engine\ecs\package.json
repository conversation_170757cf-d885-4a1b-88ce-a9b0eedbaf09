{"name": "@dl-engine/engine-ecs", "version": "1.0.0", "description": "DL-Engine ECS系统 - 实体组件系统、网络同步", "main": "index.ts", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "test": "vitest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"bitecs": "^0.3.40", "@dl-engine/engine-state": "workspace:*", "@dl-engine/shared-common": "workspace:*"}, "devDependencies": {"@types/node": "^22.0.0", "typescript": "5.6.3", "vite": "5.4.8", "vitest": "^2.0.0", "eslint": "^9.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0"}, "peerDependencies": {"react": "18.2.0"}, "engines": {"node": ">=22.0.0"}}