/**
 * DL-Engine AI工具函数
 */

/**
 * 计算向量相似度（余弦相似度）
 */
export function cosineSimilarity(vectorA: number[], vectorB: number[]): number {
  if (vectorA.length !== vectorB.length) {
    throw new Error('Vectors must have the same length')
  }
  
  let dotProduct = 0
  let normA = 0
  let normB = 0
  
  for (let i = 0; i < vectorA.length; i++) {
    dotProduct += vectorA[i] * vectorB[i]
    normA += vectorA[i] * vectorA[i]
    normB += vectorB[i] * vectorB[i]
  }
  
  if (normA === 0 || normB === 0) {
    return 0
  }
  
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB))
}

/**
 * 计算欧几里得距离
 */
export function euclideanDistance(vectorA: number[], vectorB: number[]): number {
  if (vectorA.length !== vectorB.length) {
    throw new Error('Vectors must have the same length')
  }
  
  let sum = 0
  for (let i = 0; i < vectorA.length; i++) {
    const diff = vectorA[i] - vectorB[i]
    sum += diff * diff
  }
  
  return Math.sqrt(sum)
}

/**
 * 向量归一化
 */
export function normalizeVector(vector: number[]): number[] {
  const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))
  if (norm === 0) {
    return vector.slice()
  }
  return vector.map(val => val / norm)
}

/**
 * 文本预处理
 */
export function preprocessText(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\u4e00-\u9fff\w\s]/g, '') // 保留中文、字母、数字和空格
    .replace(/\s+/g, ' ') // 合并多个空格
    .trim()
}

/**
 * 提取文本特征
 */
export function extractTextFeatures(text: string): {
  wordCount: number
  sentenceCount: number
  averageWordsPerSentence: number
  averageCharsPerWord: number
  uniqueWordRatio: number
} {
  const words = text.match(/[\u4e00-\u9fff]+|\w+/g) || []
  const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0)
  const uniqueWords = new Set(words.map(w => w.toLowerCase()))
  
  const wordCount = words.length
  const sentenceCount = sentences.length
  const averageWordsPerSentence = wordCount / Math.max(sentenceCount, 1)
  const averageCharsPerWord = words.reduce((sum, word) => sum + word.length, 0) / Math.max(wordCount, 1)
  const uniqueWordRatio = uniqueWords.size / Math.max(wordCount, 1)
  
  return {
    wordCount,
    sentenceCount,
    averageWordsPerSentence,
    averageCharsPerWord,
    uniqueWordRatio
  }
}

/**
 * 计算文本相似度（基于词汇重叠）
 */
export function textSimilarity(textA: string, textB: string): number {
  const wordsA = new Set(preprocessText(textA).split(' '))
  const wordsB = new Set(preprocessText(textB).split(' '))
  
  const intersection = new Set([...wordsA].filter(word => wordsB.has(word)))
  const union = new Set([...wordsA, ...wordsB])
  
  return intersection.size / union.size
}

/**
 * 生成文本摘要（简单版本）
 */
export function extractiveSummary(text: string, maxSentences: number = 3): string {
  const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0)
  
  if (sentences.length <= maxSentences) {
    return text
  }
  
  // 简单的句子评分：基于长度和位置
  const scoredSentences = sentences.map((sentence, index) => {
    const positionScore = index === 0 || index === sentences.length - 1 ? 1.5 : 1.0
    const lengthScore = sentence.length / 100 // 假设理想句子长度为100字符
    return {
      sentence: sentence.trim(),
      score: positionScore * lengthScore,
      index
    }
  })
  
  // 选择得分最高的句子
  const topSentences = scoredSentences
    .sort((a, b) => b.score - a.score)
    .slice(0, maxSentences)
    .sort((a, b) => a.index - b.index)
  
  return topSentences.map(s => s.sentence).join('。') + '。'
}

/**
 * 计算学习难度
 */
export function calculateLearningDifficulty(content: {
  wordCount: number
  vocabularyComplexity: number
  conceptDensity: number
  prerequisiteCount: number
}): number {
  const {
    wordCount,
    vocabularyComplexity,
    conceptDensity,
    prerequisiteCount
  } = content
  
  // 归一化各个因子
  const lengthFactor = Math.min(wordCount / 1000, 1) // 假设1000词为中等长度
  const vocabularyFactor = Math.min(vocabularyComplexity, 1)
  const conceptFactor = Math.min(conceptDensity, 1)
  const prerequisiteFactor = Math.min(prerequisiteCount / 5, 1) // 假设5个前置条件为高难度
  
  // 加权平均
  const difficulty = (
    lengthFactor * 0.2 +
    vocabularyFactor * 0.3 +
    conceptFactor * 0.3 +
    prerequisiteFactor * 0.2
  )
  
  return Math.max(0, Math.min(1, difficulty))
}

/**
 * 生成学习建议
 */
export function generateLearningTips(userProfile: {
  learningStyle: { visual: number; auditory: number; kinesthetic: number; readingWriting: number }
  difficultyPreference: number
  completionRate: number
}): string[] {
  const tips: string[] = []
  const { learningStyle, difficultyPreference, completionRate } = userProfile
  
  // 基于学习风格的建议
  const dominantStyle = Object.entries(learningStyle).reduce((a, b) => 
    learningStyle[a[0] as keyof typeof learningStyle] > learningStyle[b[0] as keyof typeof learningStyle] ? a : b
  )[0]
  
  switch (dominantStyle) {
    case 'visual':
      tips.push('尝试使用图表、图像和视频来辅助学习')
      tips.push('制作思维导图来整理知识点')
      break
    case 'auditory':
      tips.push('可以尝试听播客或音频课程')
      tips.push('与他人讨论学习内容会很有帮助')
      break
    case 'kinesthetic':
      tips.push('通过实践和动手操作来学习效果更好')
      tips.push('尝试在VR/AR环境中进行沉浸式学习')
      break
    case 'readingWriting':
      tips.push('做笔记和总结会帮助你更好地理解')
      tips.push('尝试写作来表达你的理解')
      break
  }
  
  // 基于难度偏好的建议
  if (difficultyPreference < 0.3) {
    tips.push('从基础内容开始，循序渐进地提高难度')
  } else if (difficultyPreference > 0.7) {
    tips.push('可以尝试更有挑战性的内容来保持学习动力')
  }
  
  // 基于完成率的建议
  if (completionRate < 0.5) {
    tips.push('设定小目标，每次完成一小部分内容')
    tips.push('找到适合自己的学习节奏，不要急于求成')
  } else if (completionRate > 0.8) {
    tips.push('你的学习进度很好，可以尝试更多样化的内容')
  }
  
  return tips
}

/**
 * 验证AI响应格式
 */
export function validateAIResponse(response: any, expectedSchema: any): boolean {
  // 简单的JSON schema验证
  try {
    if (typeof response !== 'object' || response === null) {
      return false
    }
    
    for (const key in expectedSchema) {
      if (!(key in response)) {
        return false
      }
      
      const expectedType = expectedSchema[key]
      const actualValue = response[key]
      
      if (typeof expectedType === 'string') {
        if (typeof actualValue !== expectedType) {
          return false
        }
      } else if (Array.isArray(expectedType)) {
        if (!Array.isArray(actualValue)) {
          return false
        }
      }
    }
    
    return true
  } catch (error) {
    return false
  }
}

/**
 * 格式化AI错误信息
 */
export function formatAIError(error: any): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object') {
    return error.message || error.error || JSON.stringify(error)
  }
  
  return 'Unknown AI error occurred'
}

/**
 * 生成唯一ID
 */
export function generateUniqueId(prefix: string = 'ai'): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}_${timestamp}_${random}`
}
