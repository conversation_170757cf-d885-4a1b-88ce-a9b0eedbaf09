/**
 * DL-Engine 物理类型定义
 */

import { Vector3, Quaternion } from 'three'
import { Entity } from '@dl-engine/engine-ecs'
import { 
  RigidBody, 
  Collider, 
  KinematicCharacterController,
  RigidBodyType,
  ShapeType,
  ActiveCollisionTypes,
  InteractionGroups
} from '@dimforge/rapier3d'

/**
 * 物理向量
 */
export interface PhysicsVector3 {
  x: number
  y: number
  z: number
}

/**
 * 物理四元数
 */
export interface PhysicsQuaternion {
  x: number
  y: number
  z: number
  w: number
}

/**
 * 刚体类型
 */
export enum BodyType {
  /** 动态刚体 */
  DYNAMIC = 'dynamic',
  /** 静态刚体 */
  STATIC = 'static',
  /** 运动学刚体 */
  KINEMATIC = 'kinematic'
}

/**
 * 碰撞体形状类型
 */
export enum ColliderShape {
  /** 立方体 */
  BOX = 'box',
  /** 球体 */
  SPHERE = 'sphere',
  /** 胶囊体 */
  CAPSULE = 'capsule',
  /** 圆柱体 */
  CYLINDER = 'cylinder',
  /** 圆锥体 */
  CONE = 'cone',
  /** 三角网格 */
  TRIMESH = 'trimesh',
  /** 凸包 */
  CONVEX_HULL = 'convex_hull',
  /** 高度场 */
  HEIGHTFIELD = 'heightfield'
}

/**
 * 碰撞事件类型
 */
export enum CollisionEventType {
  /** 碰撞开始 */
  COLLISION_START = 'collision_start',
  /** 碰撞持续 */
  COLLISION_PERSIST = 'collision_persist',
  /** 碰撞结束 */
  COLLISION_END = 'collision_end',
  /** 触发器进入 */
  TRIGGER_ENTER = 'trigger_enter',
  /** 触发器停留 */
  TRIGGER_STAY = 'trigger_stay',
  /** 触发器退出 */
  TRIGGER_EXIT = 'trigger_exit'
}

/**
 * 射线投射结果
 */
export interface RaycastHit {
  /** 碰撞点距离 */
  distance: number
  
  /** 碰撞点位置 */
  position: PhysicsVector3
  
  /** 碰撞点法线 */
  normal: PhysicsVector3
  
  /** 碰撞的刚体 */
  rigidBody: RigidBody | null
  
  /** 碰撞的碰撞体 */
  collider: Collider
  
  /** 碰撞的实体 */
  entity: Entity
  
  /** 纹理坐标 */
  uv?: { u: number; v: number }
  
  /** 三角形索引 */
  triangleIndex?: number
}

/**
 * 射线投射查询参数
 */
export interface RaycastQuery {
  /** 射线起点 */
  origin: PhysicsVector3
  
  /** 射线方向 */
  direction: PhysicsVector3
  
  /** 最大距离 */
  maxDistance: number
  
  /** 碰撞组 */
  collisionGroups?: InteractionGroups
  
  /** 查询标志 */
  flags?: number
  
  /** 排除的碰撞体 */
  excludeCollider?: Entity
  
  /** 排除的刚体 */
  excludeRigidBody?: Entity
  
  /** 是否包含传感器 */
  includeSensors?: boolean
}

/**
 * 形状投射查询参数
 */
export interface ShapecastQuery {
  /** 形状类型 */
  shape: ColliderShape
  
  /** 形状参数 */
  shapeParams: any
  
  /** 起始位置 */
  position: PhysicsVector3
  
  /** 起始旋转 */
  rotation: PhysicsQuaternion
  
  /** 移动方向 */
  direction: PhysicsVector3
  
  /** 最大距离 */
  maxDistance: number
  
  /** 碰撞组 */
  collisionGroups?: InteractionGroups
}

/**
 * 重叠查询参数
 */
export interface OverlapQuery {
  /** 形状类型 */
  shape: ColliderShape
  
  /** 形状参数 */
  shapeParams: any
  
  /** 位置 */
  position: PhysicsVector3
  
  /** 旋转 */
  rotation: PhysicsQuaternion
  
  /** 碰撞组 */
  collisionGroups?: InteractionGroups
  
  /** 最大结果数量 */
  maxResults?: number
}

/**
 * 碰撞事件数据
 */
export interface CollisionEvent {
  /** 事件类型 */
  type: CollisionEventType
  
  /** 第一个实体 */
  entityA: Entity
  
  /** 第二个实体 */
  entityB: Entity
  
  /** 第一个碰撞体 */
  colliderA: Collider
  
  /** 第二个碰撞体 */
  colliderB: Collider
  
  /** 碰撞点 */
  contactPoint?: PhysicsVector3
  
  /** 碰撞法线 */
  contactNormal?: PhysicsVector3
  
  /** 碰撞冲量 */
  impulse?: number
  
  /** 时间戳 */
  timestamp: number
}

/**
 * 物理材质属性
 */
export interface PhysicsMaterial {
  /** 摩擦系数 */
  friction: number
  
  /** 恢复系数（弹性） */
  restitution: number
  
  /** 密度 */
  density: number
  
  /** 摩擦结合模式 */
  frictionCombineRule: 'average' | 'min' | 'multiply' | 'max'
  
  /** 恢复结合模式 */
  restitutionCombineRule: 'average' | 'min' | 'multiply' | 'max'
}

/**
 * 约束类型
 */
export enum ConstraintType {
  /** 固定约束 */
  FIXED = 'fixed',
  /** 球形约束 */
  SPHERICAL = 'spherical',
  /** 旋转约束 */
  REVOLUTE = 'revolute',
  /** 棱柱约束 */
  PRISMATIC = 'prismatic',
  /** 通用约束 */
  GENERIC = 'generic',
  /** 弹簧约束 */
  SPRING = 'spring',
  /** 距离约束 */
  DISTANCE = 'distance'
}

/**
 * 约束参数
 */
export interface ConstraintParams {
  /** 约束类型 */
  type: ConstraintType
  
  /** 第一个实体 */
  entityA: Entity
  
  /** 第二个实体 */
  entityB: Entity
  
  /** 第一个锚点（相对于实体A） */
  anchorA: PhysicsVector3
  
  /** 第二个锚点（相对于实体B） */
  anchorB: PhysicsVector3
  
  /** 第一个轴向（相对于实体A） */
  axisA?: PhysicsVector3
  
  /** 第二个轴向（相对于实体B） */
  axisB?: PhysicsVector3
  
  /** 限制参数 */
  limits?: {
    /** 最小值 */
    min?: number
    /** 最大值 */
    max?: number
  }
  
  /** 弹簧参数 */
  spring?: {
    /** 刚度 */
    stiffness: number
    /** 阻尼 */
    damping: number
    /** 自然长度 */
    restLength?: number
  }
  
  /** 马达参数 */
  motor?: {
    /** 目标速度 */
    targetVelocity: number
    /** 最大力 */
    maxForce: number
  }
}

/**
 * 物理世界配置
 */
export interface PhysicsWorldConfig {
  /** 重力 */
  gravity: PhysicsVector3
  
  /** 时间步长细分数 */
  substeps: number
  
  /** 是否启用CCD（连续碰撞检测） */
  enableCCD: boolean
  
  /** 积分参数 */
  integrationParameters?: {
    /** 位置迭代次数 */
    numSolverIterations: number
    /** 速度迭代次数 */
    numAdditionalFrictionIterations: number
    /** 预测距离 */
    predictionDistance: number
  }
}

/**
 * 空间查询类型
 */
export enum SpatialQueryType {
  /** 最近的一个 */
  CLOSEST = 'closest',
  /** 所有结果 */
  ALL = 'all',
  /** 任意一个 */
  ANY = 'any'
}

/**
 * 空间查询结果
 */
export interface SpatialQueryResult {
  /** 实体 */
  entity: Entity
  
  /** 碰撞体 */
  collider: Collider
  
  /** 距离 */
  distance: number
  
  /** 位置 */
  position: PhysicsVector3
  
  /** 法线 */
  normal?: PhysicsVector3
}

/**
 * 物理调试信息
 */
export interface PhysicsDebugInfo {
  /** 刚体数量 */
  rigidBodyCount: number
  
  /** 碰撞体数量 */
  colliderCount: number
  
  /** 约束数量 */
  constraintCount: number
  
  /** 活跃刚体数量 */
  activeRigidBodyCount: number
  
  /** 睡眠刚体数量 */
  sleepingRigidBodyCount: number
  
  /** 碰撞对数量 */
  collisionPairCount: number
  
  /** 物理步进时间（毫秒） */
  stepTime: number
  
  /** 碰撞检测时间（毫秒） */
  collisionDetectionTime: number
  
  /** 约束求解时间（毫秒） */
  constraintSolvingTime: number
}
