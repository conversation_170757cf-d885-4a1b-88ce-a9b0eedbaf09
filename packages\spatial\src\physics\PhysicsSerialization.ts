/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import {
  NetworkObjectSendPeriodicUpdatesTag,
  ViewCursor,
  checkBitflag,
  readUint8,
  readUint16,
  readFloat32,
  readVector3,
  readVector4,
  rewindViewCursor,
  spaceUint8,
  spaceUint16,
  spaceFloat32,
  writeUint16,
  writeFloat32,
  writeVector3,
  writeVector4
} from '@ir-engine/ecs'
import { getOptionalComponent, hasComponent } from '@ir-engine/ecs/src/ComponentFunctions'
import { ECSState } from '@ir-engine/ecs/src/ECSState'
import { Entity } from '@ir-engine/ecs/src/Entity'
import { getState } from '@ir-engine/hyperflux'

import { Physics } from './classes/Physics'
import { RigidBodyComponent, RigidBodyDynamicTagComponent } from './components/RigidBodyComponent'

// Enhanced physics network sync constants
export const PHYSICS_SYNC_VERSION = 2
export const PHYSICS_PREDICTION_BUFFER_SIZE = 60 // 1 second at 60fps
export const PHYSICS_INTERPOLATION_THRESHOLD = 100 // ms
export const PHYSICS_EXTRAPOLATION_THRESHOLD = 200 // ms

// Physics state flags for enhanced sync
export const PhysicsStateFlags = {
  POSITION_CHANGED: 1 << 0,
  ROTATION_CHANGED: 1 << 1,
  LINEAR_VELOCITY_CHANGED: 1 << 2,
  ANGULAR_VELOCITY_CHANGED: 1 << 3,
  SLEEPING_STATE_CHANGED: 1 << 4,
  COLLISION_OCCURRED: 1 << 5,
  FORCE_APPLIED: 1 << 6,
  CONSTRAINT_BROKEN: 1 << 7
} as const

// Enhanced physics state for prediction and rollback
export interface PhysicsStateSnapshot {
  timestamp: number
  position: { x: number; y: number; z: number }
  rotation: { x: number; y: number; z: number; w: number }
  linearVelocity: { x: number; y: number; z: number }
  angularVelocity: { x: number; y: number; z: number }
  isSleeping: boolean
  flags: number
}

export const readBodyPosition = readVector3(RigidBodyComponent.position)
export const readBodyRotation = readVector4(RigidBodyComponent.rotation)
export const readBodyLinearVelocity = readVector3(RigidBodyComponent.linearVelocity)
export const readBodyAngularVelocity = readVector3(RigidBodyComponent.angularVelocity)

// Enhanced physics state history for prediction and rollback
const physicsStateHistory = new Map<Entity, PhysicsStateSnapshot[]>()

export const addPhysicsStateSnapshot = (entity: Entity, snapshot: PhysicsStateSnapshot) => {
  if (!physicsStateHistory.has(entity)) {
    physicsStateHistory.set(entity, [])
  }
  const history = physicsStateHistory.get(entity)!
  history.push(snapshot)

  // Keep only recent snapshots
  if (history.length > PHYSICS_PREDICTION_BUFFER_SIZE) {
    history.shift()
  }
}

export const getPhysicsStateHistory = (entity: Entity): PhysicsStateSnapshot[] => {
  return physicsStateHistory.get(entity) || []
}

export const clearPhysicsStateHistory = (entity: Entity) => {
  physicsStateHistory.delete(entity)
}

// Enhanced rigid body reading with prediction support
export const readRigidBody = (v: ViewCursor, entity: Entity) => {
  // Read version and timestamp for enhanced sync
  const version = readUint16(v)
  if (version !== PHYSICS_SYNC_VERSION) {
    console.warn(`Physics sync version mismatch: expected ${PHYSICS_SYNC_VERSION}, got ${version}`)
    return
  }

  const timestamp = readFloat32(v)
  const changeMask = readUint8(v)
  let b = 0

  const rigidBody = getOptionalComponent(entity, RigidBodyComponent)
  const dynamic = hasComponent(entity, RigidBodyDynamicTagComponent)
  let changed = false

  // Create snapshot for state history
  const snapshot: PhysicsStateSnapshot = {
    timestamp,
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0, w: 1 },
    linearVelocity: { x: 0, y: 0, z: 0 },
    angularVelocity: { x: 0, y: 0, z: 0 },
    isSleeping: false,
    flags: changeMask
  }

  if (checkBitflag(changeMask, PhysicsStateFlags.POSITION_CHANGED)) {
    readBodyPosition(v, entity)
    if (rigidBody) {
      snapshot.position = { x: rigidBody.position.x, y: rigidBody.position.y, z: rigidBody.position.z }
    }
    changed = true
  }

  if (checkBitflag(changeMask, PhysicsStateFlags.ROTATION_CHANGED)) {
    readBodyRotation(v, entity)
    if (rigidBody) {
      snapshot.rotation = { x: rigidBody.rotation.x, y: rigidBody.rotation.y, z: rigidBody.rotation.z, w: rigidBody.rotation.w }
    }
    changed = true
  }

  if (checkBitflag(changeMask, PhysicsStateFlags.LINEAR_VELOCITY_CHANGED)) {
    readBodyLinearVelocity(v, entity)
    if (rigidBody) {
      snapshot.linearVelocity = { x: rigidBody.linearVelocity.x, y: rigidBody.linearVelocity.y, z: rigidBody.linearVelocity.z }
    }
    changed = true
  }

  if (checkBitflag(changeMask, PhysicsStateFlags.ANGULAR_VELOCITY_CHANGED)) {
    readBodyAngularVelocity(v, entity)
    if (rigidBody) {
      snapshot.angularVelocity = { x: rigidBody.angularVelocity.x, y: rigidBody.angularVelocity.y, z: rigidBody.angularVelocity.z }
    }
    changed = true
  }

  // Handle sleeping state
  if (checkBitflag(changeMask, PhysicsStateFlags.SLEEPING_STATE_CHANGED)) {
    snapshot.isSleeping = readUint8(v) === 1
    changed = true
  }

  // Store state snapshot for prediction
  if (changed) {
    addPhysicsStateSnapshot(entity, snapshot)
  }

  if (dynamic && rigidBody && changed) {
    const world = Physics.getWorld(entity)
    if (!world) return

    // Apply interpolation/extrapolation based on timestamp
    const currentTime = getState(ECSState).simulationTime
    const timeDiff = currentTime - timestamp

    if (timeDiff > PHYSICS_EXTRAPOLATION_THRESHOLD) {
      // Extrapolate position based on velocity
      const dt = timeDiff / 1000
      rigidBody.position.x += rigidBody.linearVelocity.x * dt
      rigidBody.position.y += rigidBody.linearVelocity.y * dt
      rigidBody.position.z += rigidBody.linearVelocity.z * dt
    }

    Physics.setRigidbodyPose(
      world,
      entity,
      rigidBody.position,
      rigidBody.rotation,
      rigidBody.linearVelocity,
      rigidBody.angularVelocity
    )
  }

  if (!dynamic && rigidBody) {
    const position = rigidBody.position
    const rotation = rigidBody.rotation
    RigidBodyComponent.targetKinematicPosition.x[entity] = position.x
    RigidBodyComponent.targetKinematicPosition.y[entity] = position.y
    RigidBodyComponent.targetKinematicPosition.z[entity] = position.z
    RigidBodyComponent.targetKinematicRotation.x[entity] = rotation.x
    RigidBodyComponent.targetKinematicRotation.y[entity] = rotation.y
    RigidBodyComponent.targetKinematicRotation.z[entity] = rotation.z
    RigidBodyComponent.targetKinematicRotation.w[entity] = rotation.w
  }
}

export const writeBodyPosition = writeVector3(RigidBodyComponent.position)
export const writeBodyRotation = writeVector4(RigidBodyComponent.rotation)
export const writeBodyLinearVelocity = writeVector3(RigidBodyComponent.linearVelocity)
export const writeBodyAngularVelocity = writeVector3(RigidBodyComponent.angularVelocity)

// Enhanced rigid body writing with prediction support
export const writeRigidBody = (v: ViewCursor, entity: Entity) => {
  if (!hasComponent(entity, RigidBodyComponent)) return

  const rewind = rewindViewCursor(v)
  const rigidBody = getOptionalComponent(entity, RigidBodyComponent)
  if (!rigidBody) return rewind()

  // Write version and timestamp for enhanced sync
  const writeVersion = spaceUint16(v)
  const writeTimestamp = spaceFloat32(v)
  const writeChangeMask = spaceUint8(v)

  let changeMask = 0
  const currentTime = getState(ECSState).simulationTime

  const ignoreHasChanged =
    hasComponent(entity, NetworkObjectSendPeriodicUpdatesTag) &&
    Math.round(currentTime % getState(ECSState).periodicUpdateFrequency) === 0

  // Check for position changes
  if (writeBodyPosition(v, entity, ignoreHasChanged)) {
    changeMask |= PhysicsStateFlags.POSITION_CHANGED
  }

  // Check for rotation changes
  if (writeBodyRotation(v, entity, ignoreHasChanged)) {
    changeMask |= PhysicsStateFlags.ROTATION_CHANGED
  }

  // Check for linear velocity changes
  if (writeBodyLinearVelocity(v, entity, ignoreHasChanged)) {
    changeMask |= PhysicsStateFlags.LINEAR_VELOCITY_CHANGED
  }

  // Check for angular velocity changes
  if (writeBodyAngularVelocity(v, entity, ignoreHasChanged)) {
    changeMask |= PhysicsStateFlags.ANGULAR_VELOCITY_CHANGED
  }

  // Check for sleeping state changes
  const world = Physics.getWorld(entity)
  if (world && world.Rigidbodies.has(entity)) {
    const rapierBody = world.Rigidbodies.get(entity)!
    const isSleeping = rapierBody.isSleeping()

    // Store previous sleeping state for comparison
    const prevSleeping = (rigidBody as any).prevSleeping ?? false
    if (isSleeping !== prevSleeping) {
      changeMask |= PhysicsStateFlags.SLEEPING_STATE_CHANGED
      const writeSleepingState = spaceUint8(v)
      writeSleepingState(isSleeping ? 1 : 0)
      ;(rigidBody as any).prevSleeping = isSleeping
    }
  }

  // Check for collision events
  // This would be handled by a separate collision event system

  if (changeMask > 0) {
    writeVersion(PHYSICS_SYNC_VERSION)
    writeTimestamp(currentTime)
    writeChangeMask(changeMask)

    // Create and store state snapshot for local prediction
    const snapshot: PhysicsStateSnapshot = {
      timestamp: currentTime,
      position: { x: rigidBody.position.x, y: rigidBody.position.y, z: rigidBody.position.z },
      rotation: { x: rigidBody.rotation.x, y: rigidBody.rotation.y, z: rigidBody.rotation.z, w: rigidBody.rotation.w },
      linearVelocity: { x: rigidBody.linearVelocity.x, y: rigidBody.linearVelocity.y, z: rigidBody.linearVelocity.z },
      angularVelocity: { x: rigidBody.angularVelocity.x, y: rigidBody.angularVelocity.y, z: rigidBody.angularVelocity.z },
      isSleeping: (rigidBody as any).prevSleeping ?? false,
      flags: changeMask
    }
    addPhysicsStateSnapshot(entity, snapshot)

    return true
  }

  return rewind()
}

// Enhanced collision event serialization
export const writeCollisionEvent = (v: ViewCursor, entity: Entity, collisionData: any) => {
  const rewind = rewindViewCursor(v)

  // Write collision event header
  const writeEventType = spaceUint8(v)
  const writeEntityA = spaceUint16(v)
  const writeEntityB = spaceUint16(v)
  const writeContactPoint = writeVector3({ x: 0, y: 0, z: 0 })
  const writeContactNormal = writeVector3({ x: 0, y: 0, z: 0 })
  const writeImpulse = spaceFloat32(v)

  // Implementation would depend on collision data structure
  // This is a placeholder for enhanced collision sync

  return rewind() // For now, don't write collision events
}

export const PhysicsSerialization = {
  ID: 'ee.core.physics' as const,
  readRigidBody,
  writeRigidBody,
  writeCollisionEvent,
  addPhysicsStateSnapshot,
  getPhysicsStateHistory,
  clearPhysicsStateHistory,
  PHYSICS_SYNC_VERSION,
  PhysicsStateFlags
}
