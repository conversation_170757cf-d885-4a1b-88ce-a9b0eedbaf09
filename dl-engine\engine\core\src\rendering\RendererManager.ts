/**
 * DL-Engine 渲染器管理器
 * 管理Three.js渲染器的创建、配置和生命周期
 */

import {
  WebGLRenderer,
  Scene,
  PerspectiveCamera,
  OrthographicCamera,
  Camera,
  WebGLRenderTarget,
  Vector2,
  Color,
  PCFSoftShadowMap,
  VSMShadowMap,
  PCFShadowMap,
  BasicShadowMap,
  sRGBEncoding,
  LinearEncoding,
  ACESFilmicToneMapping,
  LinearToneMapping,
  ReinhardToneMapping,
  CineonToneMapping
} from 'three'

import { getState, getMutableState } from '@dl-engine/engine-state'
import { RendererState, RenderQuality, AntiAliasing } from '../RendererState'

/**
 * 渲染器配置选项
 */
export interface RendererConfig {
  /** 画布元素或选择器 */
  canvas?: HTMLCanvasElement | string
  
  /** 画布尺寸 */
  width?: number
  height?: number
  
  /** 像素比 */
  pixelRatio?: number
  
  /** 是否启用抗锯齿 */
  antialias?: boolean
  
  /** 是否启用alpha通道 */
  alpha?: boolean
  
  /** 是否启用深度缓冲 */
  depth?: boolean
  
  /** 是否启用模板缓冲 */
  stencil?: boolean
  
  /** 是否保留绘制缓冲 */
  preserveDrawingBuffer?: boolean
  
  /** 功率偏好 */
  powerPreference?: 'default' | 'high-performance' | 'low-power'
  
  /** 是否启用XR */
  xr?: boolean
}

/**
 * 渲染器管理器类
 */
export class RendererManager {
  private static instance: RendererManager | null = null
  
  private renderer: WebGLRenderer | null = null
  private scene: Scene | null = null
  private camera: Camera | null = null
  private canvas: HTMLCanvasElement | null = null
  
  /**
   * 获取单例实例
   */
  static getInstance(): RendererManager {
    if (!RendererManager.instance) {
      RendererManager.instance = new RendererManager()
    }
    return RendererManager.instance
  }
  
  /**
   * 初始化渲染器
   */
  async initialize(config: RendererConfig = {}): Promise<WebGLRenderer> {
    if (this.renderer) {
      console.warn('Renderer already initialized')
      return this.renderer
    }
    
    // 获取或创建画布
    this.canvas = this.getCanvas(config.canvas)
    
    // 创建渲染器
    this.renderer = new WebGLRenderer({
      canvas: this.canvas,
      antialias: config.antialias ?? true,
      alpha: config.alpha ?? false,
      depth: config.depth ?? true,
      stencil: config.stencil ?? true,
      preserveDrawingBuffer: config.preserveDrawingBuffer ?? false,
      powerPreference: config.powerPreference ?? 'high-performance'
    })
    
    // 配置渲染器
    this.configureRenderer(config)
    
    // 创建默认场景和相机
    this.createDefaultScene()
    this.createDefaultCamera(config.width, config.height)
    
    // 更新状态
    const rendererState = getMutableState(RendererState)
    rendererState.setRenderer(this.renderer)
    rendererState.setScene(this.scene)
    rendererState.setCamera(this.camera)
    
    // 设置画布尺寸
    if (config.width && config.height) {
      this.setSize(config.width, config.height)
    } else {
      this.setSize(window.innerWidth, window.innerHeight)
    }
    
    // 启用XR（如果需要）
    if (config.xr) {
      this.enableXR()
    }
    
    console.log('DL-Engine renderer initialized successfully')
    return this.renderer
  }
  
  /**
   * 配置渲染器
   */
  private configureRenderer(config: RendererConfig): void {
    if (!this.renderer) return
    
    // 设置像素比
    const pixelRatio = config.pixelRatio ?? window.devicePixelRatio
    this.renderer.setPixelRatio(Math.min(pixelRatio, 2))
    
    // 设置输出编码
    this.renderer.outputEncoding = sRGBEncoding
    
    // 设置色调映射
    this.renderer.toneMapping = ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 1.0
    
    // 启用阴影
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = PCFSoftShadowMap
    
    // 设置背景色
    this.renderer.setClearColor(0x000000, 1)
    
    // 启用物理正确的光照
    this.renderer.physicallyCorrectLights = true
    
    // 设置其他属性
    this.renderer.sortObjects = true
    this.renderer.autoClear = true
    this.renderer.autoClearColor = true
    this.renderer.autoClearDepth = true
    this.renderer.autoClearStencil = true
  }
  
  /**
   * 获取或创建画布
   */
  private getCanvas(canvasOrSelector?: HTMLCanvasElement | string): HTMLCanvasElement {
    if (canvasOrSelector instanceof HTMLCanvasElement) {
      return canvasOrSelector
    }
    
    if (typeof canvasOrSelector === 'string') {
      const element = document.querySelector(canvasOrSelector)
      if (element instanceof HTMLCanvasElement) {
        return element
      }
      throw new Error(`Canvas element not found: ${canvasOrSelector}`)
    }
    
    // 创建新画布
    const canvas = document.createElement('canvas')
    canvas.style.display = 'block'
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    
    // 添加到body（如果没有指定容器）
    document.body.appendChild(canvas)
    
    return canvas
  }
  
  /**
   * 创建默认场景
   */
  private createDefaultScene(): void {
    this.scene = new Scene()
    this.scene.background = new Color(0x000000)
  }
  
  /**
   * 创建默认相机
   */
  private createDefaultCamera(width?: number, height?: number): void {
    const w = width ?? window.innerWidth
    const h = height ?? window.innerHeight
    const aspect = w / h
    
    this.camera = new PerspectiveCamera(75, aspect, 0.1, 1000)
    this.camera.position.set(0, 0, 5)
  }
  
  /**
   * 设置渲染器尺寸
   */
  setSize(width: number, height: number): void {
    if (!this.renderer) return
    
    this.renderer.setSize(width, height)
    
    // 更新相机宽高比
    if (this.camera instanceof PerspectiveCamera) {
      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
    } else if (this.camera instanceof OrthographicCamera) {
      const aspect = width / height
      this.camera.left = -aspect
      this.camera.right = aspect
      this.camera.updateProjectionMatrix()
    }
    
    // 更新状态
    getMutableState(RendererState).setCanvasSize(width, height)
  }
  
  /**
   * 设置像素比
   */
  setPixelRatio(ratio: number): void {
    if (!this.renderer) return
    
    this.renderer.setPixelRatio(Math.min(ratio, 2))
    getMutableState(RendererState).setPixelRatio(ratio)
  }
  
  /**
   * 设置渲染质量
   */
  setQuality(quality: RenderQuality): void {
    if (!this.renderer) return
    
    switch (quality) {
      case RenderQuality.LOW:
        this.renderer.shadowMap.type = BasicShadowMap
        this.renderer.toneMapping = LinearToneMapping
        break
      case RenderQuality.MEDIUM:
        this.renderer.shadowMap.type = PCFShadowMap
        this.renderer.toneMapping = ReinhardToneMapping
        break
      case RenderQuality.HIGH:
        this.renderer.shadowMap.type = PCFSoftShadowMap
        this.renderer.toneMapping = ACESFilmicToneMapping
        break
      case RenderQuality.ULTRA:
        this.renderer.shadowMap.type = VSMShadowMap
        this.renderer.toneMapping = ACESFilmicToneMapping
        break
    }
    
    getMutableState(RendererState).setQuality(quality)
  }
  
  /**
   * 启用XR
   */
  enableXR(): void {
    if (!this.renderer) return
    
    this.renderer.xr.enabled = true
    getMutableState(RendererState).setXRMode('vr')
  }
  
  /**
   * 渲染一帧
   */
  render(): void {
    if (!this.renderer || !this.scene || !this.camera) return
    
    const startTime = performance.now()
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera)
    
    // 更新统计信息
    const endTime = performance.now()
    const frameTime = endTime - startTime
    
    const rendererState = getMutableState(RendererState)
    rendererState.updateStats({
      frameCount: rendererState.stats.frameCount.value + 1,
      lastFrameTime: frameTime,
      triangles: this.renderer.info.render.triangles,
      drawCalls: this.renderer.info.render.calls,
      textureMemory: this.renderer.info.memory.textures,
      geometryMemory: this.renderer.info.memory.geometries
    })
  }
  
  /**
   * 销毁渲染器
   */
  dispose(): void {
    if (this.renderer) {
      this.renderer.dispose()
      this.renderer = null
    }
    
    if (this.canvas && this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas)
      this.canvas = null
    }
    
    this.scene = null
    this.camera = null
    
    // 清理状态
    const rendererState = getMutableState(RendererState)
    rendererState.setRenderer(null)
    rendererState.setScene(null)
    rendererState.setCamera(null)
    
    RendererManager.instance = null
    console.log('DL-Engine renderer disposed')
  }
  
  /**
   * 获取渲染器
   */
  getRenderer(): WebGLRenderer | null {
    return this.renderer
  }
  
  /**
   * 获取场景
   */
  getScene(): Scene | null {
    return this.scene
  }
  
  /**
   * 获取相机
   */
  getCamera(): Camera | null {
    return this.camera
  }
  
  /**
   * 获取画布
   */
  getCanvas(): HTMLCanvasElement | null {
    return this.canvas
  }
}
