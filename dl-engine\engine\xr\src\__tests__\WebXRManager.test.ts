/**
 * DL-Engine WebXR管理器测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { WebXRManager } from '../webxr/WebXRManager'

// Mock WebXR API
const mockXRSession = {
  inputSources: [],
  visibilityState: 'visible',
  requestReferenceSpace: vi.fn().mockResolvedValue({}),
  requestAnimationFrame: vi.fn(),
  cancelAnimationFrame: vi.fn(),
  end: vi.fn().mockResolvedValue(undefined),
  updateRenderState: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
}

const mockXRSystem = {
  isSessionSupported: vi.fn(),
  requestSession: vi.fn().mockResolvedValue(mockXRSession)
}

const mockRenderer = {
  xr: {
    setSession: vi.fn().mockResolvedValue(undefined),
    getSession: vi.fn().mockReturnValue(null),
    isPresenting: vi.fn().mockReturnValue(false)
  },
  setSize: vi.fn(),
  render: vi.fn()
}

// Mock navigator.xr
Object.defineProperty(navigator, 'xr', {
  value: mockXRSystem,
  writable: true
})

// Mock Three.js WebGLRenderer
vi.mock('three', () => ({
  WebGLRenderer: vi.fn(() => mockRenderer)
}))

describe('WebXRManager', () => {
  let webXRManager: WebXRManager
  
  beforeEach(() => {
    webXRManager = WebXRManager.getInstance()
    
    // 重置所有mock
    vi.clearAllMocks()
    
    // 设置默认的mock返回值
    mockXRSystem.isSessionSupported.mockResolvedValue(true)
  })
  
  afterEach(() => {
    // 清理
    if (webXRManager.getCurrentSession()) {
      webXRManager.endSession()
    }
  })
  
  describe('Initialization', () => {
    it('should initialize WebXR successfully', async () => {
      await webXRManager.initialize(mockRenderer as any)
      
      expect(mockXRSystem.isSessionSupported).toHaveBeenCalledWith('immersive-vr')
      expect(mockXRSystem.isSessionSupported).toHaveBeenCalledWith('immersive-ar')
      expect(mockXRSystem.isSessionSupported).toHaveBeenCalledWith('inline')
    })
    
    it('should detect VR support', async () => {
      mockXRSystem.isSessionSupported.mockImplementation((mode) => {
        return Promise.resolve(mode === 'immersive-vr')
      })
      
      await webXRManager.initialize(mockRenderer as any)
      
      const capabilities = webXRManager.getCapabilities()
      expect(capabilities.supportsVR).toBe(true)
      expect(capabilities.supportsAR).toBe(false)
    })
    
    it('should detect AR support', async () => {
      mockXRSystem.isSessionSupported.mockImplementation((mode) => {
        return Promise.resolve(mode === 'immersive-ar')
      })
      
      await webXRManager.initialize(mockRenderer as any)
      
      const capabilities = webXRManager.getCapabilities()
      expect(capabilities.supportsVR).toBe(false)
      expect(capabilities.supportsAR).toBe(true)
    })
    
    it('should handle WebXR not supported', async () => {
      // 临时移除navigator.xr
      const originalXR = navigator.xr
      delete (navigator as any).xr
      
      await expect(webXRManager.initialize(mockRenderer as any))
        .rejects.toThrow('WebXR not supported in this browser')
      
      // 恢复navigator.xr
      Object.defineProperty(navigator, 'xr', {
        value: originalXR,
        writable: true
      })
    })
    
    it('should handle initialization errors gracefully', async () => {
      mockXRSystem.isSessionSupported.mockRejectedValue(new Error('XR error'))
      
      await expect(webXRManager.initialize(mockRenderer as any))
        .rejects.toThrow('XR error')
    })
  })
  
  describe('Session Management', () => {
    beforeEach(async () => {
      await webXRManager.initialize(mockRenderer as any)
    })
    
    it('should request VR session successfully', async () => {
      const session = await webXRManager.requestSession('immersive-vr')
      
      expect(mockXRSystem.requestSession).toHaveBeenCalledWith('immersive-vr', undefined)
      expect(mockRenderer.xr.setSession).toHaveBeenCalledWith(mockXRSession)
      expect(mockXRSession.requestReferenceSpace).toHaveBeenCalledWith('local-floor')
      expect(session).toBe(mockXRSession)
    })
    
    it('should request AR session successfully', async () => {
      const session = await webXRManager.requestSession('immersive-ar')
      
      expect(mockXRSystem.requestSession).toHaveBeenCalledWith('immersive-ar', undefined)
      expect(session).toBe(mockXRSession)
    })
    
    it('should request session with options', async () => {
      const options = {
        requiredFeatures: ['local-floor'],
        optionalFeatures: ['hand-tracking']
      }
      
      await webXRManager.requestSession('immersive-vr', options)
      
      expect(mockXRSystem.requestSession).toHaveBeenCalledWith('immersive-vr', options)
    })
    
    it('should handle session request errors', async () => {
      mockXRSystem.requestSession.mockRejectedValue(new Error('Session request failed'))
      
      await expect(webXRManager.requestSession('immersive-vr'))
        .rejects.toThrow('Session request failed')
    })
    
    it('should end session successfully', async () => {
      await webXRManager.requestSession('immersive-vr')
      
      expect(webXRManager.getCurrentSession()).toBe(mockXRSession)
      expect(webXRManager.isPresenting()).toBe(true)
      
      await webXRManager.endSession()
      
      expect(mockXRSession.end).toHaveBeenCalled()
    })
    
    it('should handle ending non-existent session', async () => {
      // 没有活动会话时结束会话不应该抛出错误
      await expect(webXRManager.endSession()).resolves.toBeUndefined()
    })
  })
  
  describe('Session Events', () => {
    beforeEach(async () => {
      await webXRManager.initialize(mockRenderer as any)
      await webXRManager.requestSession('immersive-vr')
    })
    
    it('should setup session event listeners', () => {
      expect(mockXRSession.addEventListener).toHaveBeenCalledWith('end', expect.any(Function))
      expect(mockXRSession.addEventListener).toHaveBeenCalledWith('inputsourceschange', expect.any(Function))
      expect(mockXRSession.addEventListener).toHaveBeenCalledWith('select', expect.any(Function))
      expect(mockXRSession.addEventListener).toHaveBeenCalledWith('selectstart', expect.any(Function))
      expect(mockXRSession.addEventListener).toHaveBeenCalledWith('selectend', expect.any(Function))
    })
    
    it('should handle session end event', () => {
      // 获取end事件监听器
      const endListener = mockXRSession.addEventListener.mock.calls
        .find(call => call[0] === 'end')?.[1]
      
      expect(endListener).toBeDefined()
      
      // 模拟会话结束
      endListener()
      
      expect(webXRManager.getCurrentSession()).toBe(null)
      expect(webXRManager.isPresenting()).toBe(false)
    })
    
    it('should handle input sources change event', () => {
      const inputSourcesChangeListener = mockXRSession.addEventListener.mock.calls
        .find(call => call[0] === 'inputsourceschange')?.[1]
      
      expect(inputSourcesChangeListener).toBeDefined()
      
      const mockEvent = {
        session: mockXRSession,
        added: [{ handedness: 'right', targetRayMode: 'tracked-pointer' }],
        removed: []
      }
      
      // 模拟输入源变化
      expect(() => inputSourcesChangeListener(mockEvent)).not.toThrow()
    })
    
    it('should handle select events', () => {
      const selectListener = mockXRSession.addEventListener.mock.calls
        .find(call => call[0] === 'select')?.[1]
      
      expect(selectListener).toBeDefined()
      
      const mockEvent = {
        frame: {},
        inputSource: { handedness: 'right', targetRayMode: 'tracked-pointer' }
      }
      
      // 模拟选择事件
      expect(() => selectListener(mockEvent)).not.toThrow()
    })
  })
  
  describe('Capabilities Detection', () => {
    it('should return correct capabilities after initialization', async () => {
      mockXRSystem.isSessionSupported.mockImplementation((mode) => {
        return Promise.resolve(['immersive-vr', 'inline'].includes(mode))
      })
      
      await webXRManager.initialize(mockRenderer as any)
      
      const capabilities = webXRManager.getCapabilities()
      
      expect(capabilities.supportsVR).toBe(true)
      expect(capabilities.supportsAR).toBe(false)
      expect(capabilities.supportedModes).toContain('immersive-vr')
      expect(capabilities.supportedModes).toContain('inline')
      expect(capabilities.supportedModes).not.toContain('immersive-ar')
    })
    
    it('should handle capabilities detection errors', async () => {
      mockXRSystem.isSessionSupported.mockImplementation((mode) => {
        if (mode === 'immersive-vr') {
          return Promise.reject(new Error('VR not available'))
        }
        return Promise.resolve(false)
      })
      
      await webXRManager.initialize(mockRenderer as any)
      
      const capabilities = webXRManager.getCapabilities()
      
      // 应该优雅地处理错误，不支持VR
      expect(capabilities.supportsVR).toBe(false)
    })
  })
  
  describe('State Management', () => {
    beforeEach(async () => {
      await webXRManager.initialize(mockRenderer as any)
    })
    
    it('should track session state correctly', async () => {
      expect(webXRManager.isPresenting()).toBe(false)
      expect(webXRManager.getCurrentSession()).toBe(null)
      
      await webXRManager.requestSession('immersive-vr')
      
      expect(webXRManager.isPresenting()).toBe(true)
      expect(webXRManager.getCurrentSession()).toBe(mockXRSession)
      
      await webXRManager.endSession()
      
      expect(webXRManager.isPresenting()).toBe(false)
      expect(webXRManager.getCurrentSession()).toBe(null)
    })
    
    it('should maintain capabilities state', async () => {
      const capabilities = webXRManager.getCapabilities()
      
      expect(capabilities).toHaveProperty('supportsVR')
      expect(capabilities).toHaveProperty('supportsAR')
      expect(capabilities).toHaveProperty('supportedModes')
      expect(Array.isArray(capabilities.supportedModes)).toBe(true)
    })
  })
  
  describe('Error Handling', () => {
    beforeEach(async () => {
      await webXRManager.initialize(mockRenderer as any)
    })
    
    it('should handle renderer not initialized', async () => {
      const newManager = new (WebXRManager as any)()
      
      await expect(newManager.requestSession('immersive-vr'))
        .rejects.toThrow('Renderer not initialized')
    })
    
    it('should handle session setup errors', async () => {
      mockRenderer.xr.setSession.mockRejectedValue(new Error('Renderer setup failed'))
      
      await expect(webXRManager.requestSession('immersive-vr'))
        .rejects.toThrow('Renderer setup failed')
    })
    
    it('should handle reference space errors', async () => {
      mockXRSession.requestReferenceSpace.mockRejectedValue(new Error('Reference space not supported'))
      
      await expect(webXRManager.requestSession('immersive-vr'))
        .rejects.toThrow('Reference space not supported')
    })
  })
  
  describe('Singleton Pattern', () => {
    it('should return same instance', () => {
      const instance1 = WebXRManager.getInstance()
      const instance2 = WebXRManager.getInstance()
      
      expect(instance1).toBe(instance2)
    })
    
    it('should maintain state across getInstance calls', async () => {
      const instance1 = WebXRManager.getInstance()
      await instance1.initialize(mockRenderer as any)
      
      const instance2 = WebXRManager.getInstance()
      const capabilities = instance2.getCapabilities()
      
      expect(capabilities).toBeDefined()
    })
  })
  
  describe('Performance', () => {
    beforeEach(async () => {
      await webXRManager.initialize(mockRenderer as any)
    })
    
    it('should handle rapid session requests', async () => {
      const startTime = performance.now()
      
      // 快速请求多个会话（虽然实际上只能有一个活动会话）
      const promises = []
      for (let i = 0; i < 5; i++) {
        promises.push(webXRManager.requestSession('immersive-vr').catch(() => {}))
      }
      
      await Promise.all(promises)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(1000) // 应该在1秒内完成
    })
    
    it('should handle session state queries efficiently', () => {
      const startTime = performance.now()
      
      // 执行大量状态查询
      for (let i = 0; i < 1000; i++) {
        webXRManager.isPresenting()
        webXRManager.getCurrentSession()
        webXRManager.getCapabilities()
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(10) // 应该非常快
    })
  })
})
