/**
 * DL-Engine 实体树管理
 * 管理实体的层次结构和父子关系
 */

import { defineComponent, getComponent, hasComponent, setComponent, removeComponent } from './ComponentFunctions'
import { Entity, UndefinedEntity } from './Entity'

/**
 * 实体树组件
 * 定义实体的父子关系
 */
export const EntityTreeComponent = defineComponent({
  name: 'EntityTree',
  schema: {
    parentEntity: UndefinedEntity as Entity,
    children: [] as Entity[],
    depth: 0,
    index: 0
  }
})

/**
 * 添加子实体
 */
export function addEntityChild(parent: Entity, child: Entity): void {
  if (parent === UndefinedEntity || child === UndefinedEntity) {
    console.warn('Cannot add child to undefined entity')
    return
  }

  if (parent === child) {
    console.warn('Cannot add entity as child of itself')
    return
  }

  // 检查是否会造成循环引用
  if (isAncestor(child, parent)) {
    console.warn('Cannot add entity as child - would create circular reference')
    return
  }

  // 从当前父实体移除
  removeEntityChild(child)

  // 设置新的父子关系
  const parentTree = getComponent(parent, EntityTreeComponent) || {
    parentEntity: UndefinedEntity,
    children: [],
    depth: 0,
    index: 0
  }

  const childTree = getComponent(child, EntityTreeComponent) || {
    parentEntity: UndefinedEntity,
    children: [],
    depth: 0,
    index: 0
  }

  // 更新父实体
  parentTree.children.push(child)
  setComponent(parent, EntityTreeComponent, parentTree)

  // 更新子实体
  childTree.parentEntity = parent
  childTree.depth = parentTree.depth + 1
  childTree.index = parentTree.children.length - 1
  setComponent(child, EntityTreeComponent, childTree)

  // 递归更新所有子实体的深度
  updateChildrenDepth(child)
}

/**
 * 移除子实体
 */
export function removeEntityChild(child: Entity): void {
  if (child === UndefinedEntity) return

  const childTree = getComponent(child, EntityTreeComponent)
  if (!childTree || childTree.parentEntity === UndefinedEntity) return

  const parent = childTree.parentEntity
  const parentTree = getComponent(parent, EntityTreeComponent)
  
  if (parentTree) {
    // 从父实体的子列表中移除
    const index = parentTree.children.indexOf(child)
    if (index !== -1) {
      parentTree.children.splice(index, 1)
      
      // 更新后续子实体的索引
      for (let i = index; i < parentTree.children.length; i++) {
        const siblingTree = getComponent(parentTree.children[i], EntityTreeComponent)
        if (siblingTree) {
          siblingTree.index = i
          setComponent(parentTree.children[i], EntityTreeComponent, siblingTree)
        }
      }
      
      setComponent(parent, EntityTreeComponent, parentTree)
    }
  }

  // 重置子实体的父关系
  childTree.parentEntity = UndefinedEntity
  childTree.depth = 0
  childTree.index = 0
  setComponent(child, EntityTreeComponent, childTree)

  // 递归更新所有子实体的深度
  updateChildrenDepth(child)
}

/**
 * 获取父实体
 */
export function getEntityParent(entity: Entity): Entity {
  const tree = getComponent(entity, EntityTreeComponent)
  return tree?.parentEntity || UndefinedEntity
}

/**
 * 获取子实体列表
 */
export function getEntityChildren(entity: Entity): Entity[] {
  const tree = getComponent(entity, EntityTreeComponent)
  return tree?.children || []
}

/**
 * 获取实体深度
 */
export function getEntityDepth(entity: Entity): number {
  const tree = getComponent(entity, EntityTreeComponent)
  return tree?.depth || 0
}

/**
 * 检查是否为祖先实体
 */
export function isAncestor(ancestor: Entity, descendant: Entity): boolean {
  let current = getEntityParent(descendant)
  
  while (current !== UndefinedEntity) {
    if (current === ancestor) {
      return true
    }
    current = getEntityParent(current)
  }
  
  return false
}

/**
 * 检查是否为子实体
 */
export function isChild(parent: Entity, child: Entity): boolean {
  return getEntityParent(child) === parent
}

/**
 * 获取根实体
 */
export function getRootEntity(entity: Entity): Entity {
  let current = entity
  let parent = getEntityParent(current)
  
  while (parent !== UndefinedEntity) {
    current = parent
    parent = getEntityParent(current)
  }
  
  return current
}

/**
 * 遍历实体树（深度优先）
 */
export function traverseEntityTree(
  entity: Entity, 
  callback: (entity: Entity, depth: number) => void,
  depth = 0
): void {
  callback(entity, depth)
  
  const children = getEntityChildren(entity)
  for (const child of children) {
    traverseEntityTree(child, callback, depth + 1)
  }
}

/**
 * 遍历实体树（广度优先）
 */
export function traverseEntityTreeBreadthFirst(
  entity: Entity,
  callback: (entity: Entity, depth: number) => void
): void {
  const queue: Array<{ entity: Entity; depth: number }> = [{ entity, depth: 0 }]
  
  while (queue.length > 0) {
    const { entity: current, depth } = queue.shift()!
    callback(current, depth)
    
    const children = getEntityChildren(current)
    for (const child of children) {
      queue.push({ entity: child, depth: depth + 1 })
    }
  }
}

/**
 * 更新子实体深度
 */
function updateChildrenDepth(entity: Entity): void {
  const tree = getComponent(entity, EntityTreeComponent)
  if (!tree) return
  
  const children = tree.children
  for (const child of children) {
    const childTree = getComponent(child, EntityTreeComponent)
    if (childTree) {
      childTree.depth = tree.depth + 1
      setComponent(child, EntityTreeComponent, childTree)
      updateChildrenDepth(child)
    }
  }
}

/**
 * 获取实体树统计信息
 */
export function getEntityTreeStats(entity: Entity) {
  let totalEntities = 0
  let maxDepth = 0
  
  traverseEntityTree(entity, (_, depth) => {
    totalEntities++
    maxDepth = Math.max(maxDepth, depth)
  })
  
  return {
    totalEntities,
    maxDepth,
    children: getEntityChildren(entity).length
  }
}

/**
 * 销毁实体树
 */
export function destroyEntityTree(entity: Entity): void {
  // 递归销毁所有子实体
  const children = getEntityChildren(entity)
  for (const child of children) {
    destroyEntityTree(child)
  }
  
  // 从父实体移除
  removeEntityChild(entity)
  
  // 移除树组件
  if (hasComponent(entity, EntityTreeComponent)) {
    removeComponent(entity, EntityTreeComponent)
  }
}
