/**
 * DL-Engine 动画管理器
 * 管理骨骼动画、变形动画、时间轴控制等功能
 */

import {
  AnimationClip,
  AnimationMixer,
  AnimationAction,
  Object3D,
  LoopOnce,
  LoopRepeat,
  LoopPingPong,
  InterpolateLinear,
  InterpolateSmooth,
  InterpolateDiscrete
} from 'three'

import { Entity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * 动画播放状态
 */
export type AnimationPlayState = 'playing' | 'paused' | 'stopped' | 'finished'

/**
 * 动画循环模式
 */
export type AnimationLoopMode = 'once' | 'repeat' | 'pingpong'

/**
 * 动画插值模式
 */
export type AnimationInterpolation = 'linear' | 'smooth' | 'discrete'

/**
 * 动画实例
 */
export interface AnimationInstance {
  /** 实例ID */
  id: string
  
  /** 关联实体 */
  entity: Entity
  
  /** 动画混合器 */
  mixer: AnimationMixer
  
  /** 当前动作 */
  currentAction: AnimationAction | null
  
  /** 所有可用动作 */
  actions: Map<string, AnimationAction>
  
  /** 播放状态 */
  state: AnimationPlayState
  
  /** 播放速度 */
  timeScale: number
  
  /** 权重 */
  weight: number
  
  /** 是否启用 */
  enabled: boolean
  
  /** 动画事件监听器 */
  eventListeners: Map<string, Set<(event: AnimationEvent) => void>>
}

/**
 * 动画事件
 */
export interface AnimationEvent {
  /** 事件类型 */
  type: 'start' | 'finish' | 'loop' | 'interrupt'
  
  /** 动画实例ID */
  instanceId: string
  
  /** 动作名称 */
  actionName: string
  
  /** 时间戳 */
  timestamp: number
  
  /** 额外数据 */
  data?: any
}

/**
 * 动画配置
 */
export interface AnimationConfig {
  /** 循环模式 */
  loop: AnimationLoopMode
  
  /** 播放速度 */
  timeScale: number
  
  /** 权重 */
  weight: number
  
  /** 淡入时间 */
  fadeInDuration: number
  
  /** 淡出时间 */
  fadeOutDuration: number
  
  /** 插值模式 */
  interpolation: AnimationInterpolation
  
  /** 是否钳制 */
  clampWhenFinished: boolean
  
  /** 开始时间 */
  startTime: number
  
  /** 结束时间 */
  endTime: number
}

/**
 * 默认动画配置
 */
const defaultAnimationConfig: AnimationConfig = {
  loop: 'repeat',
  timeScale: 1.0,
  weight: 1.0,
  fadeInDuration: 0.2,
  fadeOutDuration: 0.2,
  interpolation: 'linear',
  clampWhenFinished: false,
  startTime: 0,
  endTime: -1
}

/**
 * 动画管理器状态
 */
export interface AnimationManagerState {
  /** 动画实例映射 */
  instances: Map<Entity, AnimationInstance>
  
  /** 全局时间缩放 */
  globalTimeScale: number
  
  /** 是否启用动画 */
  enabled: boolean
  
  /** 统计信息 */
  stats: {
    activeAnimations: number
    totalAnimations: number
    averageFrameTime: number
    memoryUsage: number
  }
}

/**
 * 动画管理器状态定义
 */
export const AnimationManagerState = defineState({
  name: 'DLEngine.AnimationManager',
  initial: (): AnimationManagerState => ({
    instances: new Map(),
    globalTimeScale: 1.0,
    enabled: true,
    stats: {
      activeAnimations: 0,
      totalAnimations: 0,
      averageFrameTime: 0,
      memoryUsage: 0
    }
  }),
  
  receptors: {
    /**
     * 添加动画实例
     */
    addInstance: (state, entity: Entity, instance: AnimationInstance) => {
      state.instances.get(NO_PROXY).set(entity, instance)
      state.stats.totalAnimations.set(state.stats.totalAnimations.value + 1)
    },
    
    /**
     * 移除动画实例
     */
    removeInstance: (state, entity: Entity) => {
      state.instances.get(NO_PROXY).delete(entity)
      state.stats.totalAnimations.set(Math.max(0, state.stats.totalAnimations.value - 1))
    },
    
    /**
     * 设置全局时间缩放
     */
    setGlobalTimeScale: (state, timeScale: number) => {
      state.globalTimeScale.set(timeScale)
    },
    
    /**
     * 设置启用状态
     */
    setEnabled: (state, enabled: boolean) => {
      state.enabled.set(enabled)
    },
    
    /**
     * 更新统计信息
     */
    updateStats: (state, stats: Partial<AnimationManagerState['stats']>) => {
      state.stats.merge(stats)
    }
  }
})

// 导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * 动画管理器
 */
export class AnimationManager {
  private static instance: AnimationManager | null = null
  
  /**
   * 获取单例实例
   */
  static getInstance(): AnimationManager {
    if (!AnimationManager.instance) {
      AnimationManager.instance = new AnimationManager()
    }
    return AnimationManager.instance
  }
  
  /**
   * 创建动画实例
   */
  createInstance(entity: Entity, object: Object3D, clips: AnimationClip[]): AnimationInstance {
    // 创建动画混合器
    const mixer = new AnimationMixer(object)
    
    // 创建动作映射
    const actions = new Map<string, AnimationAction>()
    clips.forEach(clip => {
      const action = mixer.clipAction(clip)
      actions.set(clip.name, action)
    })
    
    // 创建动画实例
    const instance: AnimationInstance = {
      id: this.generateInstanceId(),
      entity,
      mixer,
      currentAction: null,
      actions,
      state: 'stopped',
      timeScale: 1.0,
      weight: 1.0,
      enabled: true,
      eventListeners: new Map()
    }
    
    // 设置混合器事件监听
    this.setupMixerEvents(instance)
    
    // 添加到状态
    getMutableState(AnimationManagerState).addInstance(entity, instance)
    
    console.log(`Animation instance created for entity ${entity}`)
    return instance
  }
  
  /**
   * 播放动画
   */
  play(entity: Entity, actionName: string, config?: Partial<AnimationConfig>): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance || !instance.enabled) {
      return false
    }
    
    const action = instance.actions.get(actionName)
    if (!action) {
      console.warn(`Animation action '${actionName}' not found for entity ${entity}`)
      return false
    }
    
    // 应用配置
    const finalConfig = { ...defaultAnimationConfig, ...config }
    this.applyActionConfig(action, finalConfig)
    
    // 停止当前动画
    if (instance.currentAction && instance.currentAction !== action) {
      instance.currentAction.fadeOut(finalConfig.fadeOutDuration)
    }
    
    // 播放新动画
    action.reset()
    action.fadeIn(finalConfig.fadeInDuration)
    action.play()
    
    // 更新实例状态
    instance.currentAction = action
    instance.state = 'playing'
    
    // 触发事件
    this.dispatchEvent(instance, {
      type: 'start',
      instanceId: instance.id,
      actionName,
      timestamp: performance.now()
    })
    
    return true
  }
  
  /**
   * 暂停动画
   */
  pause(entity: Entity): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance || !instance.currentAction) {
      return false
    }
    
    instance.currentAction.paused = true
    instance.state = 'paused'
    
    return true
  }
  
  /**
   * 恢复动画
   */
  resume(entity: Entity): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance || !instance.currentAction) {
      return false
    }
    
    instance.currentAction.paused = false
    instance.state = 'playing'
    
    return true
  }
  
  /**
   * 停止动画
   */
  stop(entity: Entity, fadeOutDuration: number = 0.2): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance || !instance.currentAction) {
      return false
    }
    
    if (fadeOutDuration > 0) {
      instance.currentAction.fadeOut(fadeOutDuration)
    } else {
      instance.currentAction.stop()
    }
    
    instance.state = 'stopped'
    instance.currentAction = null
    
    return true
  }
  
  /**
   * 设置动画权重
   */
  setWeight(entity: Entity, actionName: string, weight: number): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return false
    }
    
    const action = instance.actions.get(actionName)
    if (!action) {
      return false
    }
    
    action.setEffectiveWeight(weight)
    return true
  }
  
  /**
   * 设置动画时间缩放
   */
  setTimeScale(entity: Entity, timeScale: number): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return false
    }
    
    instance.timeScale = timeScale
    instance.mixer.timeScale = timeScale
    
    return true
  }
  
  /**
   * 交叉淡化到新动画
   */
  crossFade(entity: Entity, fromAction: string, toAction: string, duration: number = 0.5): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return false
    }
    
    const from = instance.actions.get(fromAction)
    const to = instance.actions.get(toAction)
    
    if (!from || !to) {
      return false
    }
    
    // 执行交叉淡化
    from.fadeOut(duration)
    to.reset().fadeIn(duration).play()
    
    instance.currentAction = to
    instance.state = 'playing'
    
    return true
  }
  
  /**
   * 更新动画
   */
  update(deltaTime: number): void {
    const state = getState(AnimationManagerState)
    
    if (!state.enabled) {
      return
    }
    
    const adjustedDeltaTime = deltaTime * state.globalTimeScale
    let activeCount = 0
    
    // 更新所有动画实例
    for (const instance of state.instances.values()) {
      if (instance.enabled && instance.mixer) {
        instance.mixer.update(adjustedDeltaTime)
        
        if (instance.state === 'playing') {
          activeCount++
        }
      }
    }
    
    // 更新统计信息
    getMutableState(AnimationManagerState).updateStats({
      activeAnimations: activeCount
    })
  }
  
  /**
   * 应用动作配置
   */
  private applyActionConfig(action: AnimationAction, config: AnimationConfig): void {
    // 设置循环模式
    switch (config.loop) {
      case 'once':
        action.setLoop(LoopOnce, 1)
        break
      case 'repeat':
        action.setLoop(LoopRepeat, Infinity)
        break
      case 'pingpong':
        action.setLoop(LoopPingPong, Infinity)
        break
    }
    
    // 设置时间缩放
    action.setEffectiveTimeScale(config.timeScale)
    
    // 设置权重
    action.setEffectiveWeight(config.weight)
    
    // 设置钳制
    action.clampWhenFinished = config.clampWhenFinished
    
    // 设置时间范围
    if (config.endTime > config.startTime) {
      action.time = config.startTime
      // TODO: 实现结束时间限制
    }
  }
  
  /**
   * 设置混合器事件
   */
  private setupMixerEvents(instance: AnimationInstance): void {
    instance.mixer.addEventListener('finished', (event) => {
      const action = event.action as AnimationAction
      const actionName = this.getActionName(instance, action)
      
      if (actionName) {
        instance.state = 'finished'
        
        this.dispatchEvent(instance, {
          type: 'finish',
          instanceId: instance.id,
          actionName,
          timestamp: performance.now()
        })
      }
    })
    
    instance.mixer.addEventListener('loop', (event) => {
      const action = event.action as AnimationAction
      const actionName = this.getActionName(instance, action)
      
      if (actionName) {
        this.dispatchEvent(instance, {
          type: 'loop',
          instanceId: instance.id,
          actionName,
          timestamp: performance.now()
        })
      }
    })
  }
  
  /**
   * 获取动作名称
   */
  private getActionName(instance: AnimationInstance, action: AnimationAction): string | null {
    for (const [name, instanceAction] of instance.actions) {
      if (instanceAction === action) {
        return name
      }
    }
    return null
  }
  
  /**
   * 分发事件
   */
  private dispatchEvent(instance: AnimationInstance, event: AnimationEvent): void {
    const listeners = instance.eventListeners.get(event.type)
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(event)
        } catch (error) {
          console.error('Error in animation event listener:', error)
        }
      }
    }
  }
  
  /**
   * 添加事件监听器
   */
  addEventListener(entity: Entity, eventType: string, listener: (event: AnimationEvent) => void): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return false
    }
    
    if (!instance.eventListeners.has(eventType)) {
      instance.eventListeners.set(eventType, new Set())
    }
    
    instance.eventListeners.get(eventType)!.add(listener)
    return true
  }
  
  /**
   * 移除事件监听器
   */
  removeEventListener(entity: Entity, eventType: string, listener: (event: AnimationEvent) => void): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return false
    }
    
    const listeners = instance.eventListeners.get(eventType)
    if (listeners) {
      return listeners.delete(listener)
    }
    
    return false
  }
  
  /**
   * 销毁动画实例
   */
  destroyInstance(entity: Entity): boolean {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return false
    }
    
    // 停止所有动画
    instance.mixer.stopAllAction()
    
    // 清理事件监听器
    instance.eventListeners.clear()
    
    // 从状态中移除
    getMutableState(AnimationManagerState).removeInstance(entity)
    
    console.log(`Animation instance destroyed for entity ${entity}`)
    return true
  }
  
  /**
   * 生成实例ID
   */
  private generateInstanceId(): string {
    return `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 获取动画信息
   */
  getAnimationInfo(entity: Entity): {
    currentAction: string | null
    state: AnimationPlayState
    timeScale: number
    weight: number
    availableActions: string[]
  } | null {
    const instance = getState(AnimationManagerState).instances.get(entity)
    if (!instance) {
      return null
    }
    
    const currentActionName = instance.currentAction ? 
      this.getActionName(instance, instance.currentAction) : null
    
    return {
      currentAction: currentActionName,
      state: instance.state,
      timeScale: instance.timeScale,
      weight: instance.weight,
      availableActions: Array.from(instance.actions.keys())
    }
  }
}
