{"name": "@dl-engine/root", "version": "1.0.0", "description": "Digital Learning Engine - 数字化学习引擎根包", "private": true, "workspaces": ["engine/*", "editor/*", "server/*", "client/*", "shared/*"], "scripts": {"build": "tsx scripts/build.ts", "build:dev": "tsx scripts/build.ts --dev", "build:prod": "tsx scripts/build.ts", "build:analyze": "tsx scripts/build.ts --analyze", "build:watch": "tsx scripts/build.ts --watch", "build:clean": "tsx scripts/build.ts --clean", "build:all": "pnpm -r build", "build:all:clean": "pnpm -r clean && pnpm -r build", "dev": "vite --mode development", "dev:all": "pnpm -r dev", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest watch", "test:coverage": "tsx scripts/test-coverage.ts", "test:all": "pnpm -r test", "test:all:coverage": "pnpm -r test:coverage", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:all": "pnpm -r lint", "lint:all:fix": "pnpm -r lint:fix", "type-check": "tsc --noEmit", "type-check:build": "tsc --build tsconfig.build.json", "type-check:all": "pnpm -r type-check", "validate": "tsx scripts/validate-config.ts", "validate:all": "pnpm -r validate", "clean": "rimraf dist coverage .vite", "clean:all": "pnpm -r clean", "install:all": "pnpm install", "prepare": "husky install", "release": "tsx scripts/release.ts", "docs:build": "typedoc", "docs:serve": "serve docs"}, "devDependencies": {"@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitest/coverage-v8": "^2.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.0.0", "jsdom": "^25.0.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "rimraf": "^6.0.0", "rollup-plugin-visualizer": "^5.12.0", "serve": "^14.0.0", "terser": "^5.31.0", "tsx": "^4.0.0", "typedoc": "^0.26.0", "typescript": "5.6.3", "vite": "5.4.8", "vite-plugin-dts": "^4.0.0", "vitest": "^2.0.0"}, "engines": {"node": ">=22.0.0", "pnpm": ">=9.0.0"}}