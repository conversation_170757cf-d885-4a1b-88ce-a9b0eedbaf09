/**
 * DL-Engine WebXR管理器
 * 管理WebXR会话、设备检测和XR模式切换
 */

import { WebGLRenderer, Group, Camera, Scene } from 'three'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'
import { Entity, UndefinedEntity } from '@dl-engine/engine-ecs'

/**
 * XR会话类型
 */
export type XRSessionMode = 'immersive-vr' | 'immersive-ar' | 'inline'

/**
 * XR设备能力
 */
export interface XRCapabilities {
  /** 是否支持VR */
  supportsVR: boolean
  
  /** 是否支持AR */
  supportsAR: boolean
  
  /** 是否支持手部追踪 */
  supportsHandTracking: boolean
  
  /** 是否支持眼部追踪 */
  supportsEyeTracking: boolean
  
  /** 支持的会话模式 */
  supportedModes: XRSessionMode[]
  
  /** 设备名称 */
  deviceName?: string
}

/**
 * XR会话状态
 */
export interface XRSessionState {
  /** 当前会话 */
  session: XRSession | null
  
  /** 会话模式 */
  mode: XRSessionMode | null
  
  /** 是否处于XR模式 */
  isPresenting: boolean
  
  /** 参考空间 */
  referenceSpace: XRReferenceSpace | null
  
  /** 控制器 */
  controllers: XRInputSource[]
  
  /** 手部追踪数据 */
  hands: XRHand[]
}

/**
 * WebXR状态定义
 */
export const WebXRState = defineState({
  name: 'DLEngine.WebXR',
  initial: () => ({
    /** XR设备能力 */
    capabilities: {
      supportsVR: false,
      supportsAR: false,
      supportsHandTracking: false,
      supportsEyeTracking: false,
      supportedModes: [],
      deviceName: undefined
    } as XRCapabilities,
    
    /** 当前XR会话状态 */
    session: {
      session: null,
      mode: null,
      isPresenting: false,
      referenceSpace: null,
      controllers: [],
      hands: []
    } as XRSessionState,
    
    /** 是否已初始化 */
    initialized: false,
    
    /** 错误信息 */
    error: null as string | null
  }),
  
  receptors: {
    /**
     * 设置初始化状态
     */
    setInitialized: (state, initialized: boolean) => {
      state.initialized.set(initialized)
    },
    
    /**
     * 设置设备能力
     */
    setCapabilities: (state, capabilities: Partial<XRCapabilities>) => {
      state.capabilities.merge(capabilities)
    },
    
    /**
     * 设置会话状态
     */
    setSessionState: (state, sessionState: Partial<XRSessionState>) => {
      state.session.merge(sessionState)
    },
    
    /**
     * 设置错误信息
     */
    setError: (state, error: string | null) => {
      state.error.set(error)
    }
  }
})

/**
 * WebXR管理器
 */
export class WebXRManager {
  private static instance: WebXRManager | null = null
  private renderer: WebGLRenderer | null = null
  private currentSession: XRSession | null = null
  
  /**
   * 获取单例实例
   */
  static getInstance(): WebXRManager {
    if (!WebXRManager.instance) {
      WebXRManager.instance = new WebXRManager()
    }
    return WebXRManager.instance
  }
  
  /**
   * 初始化WebXR
   */
  async initialize(renderer: WebGLRenderer): Promise<void> {
    this.renderer = renderer
    
    try {
      // 检测WebXR支持
      if (!navigator.xr) {
        throw new Error('WebXR not supported in this browser')
      }
      
      // 检测设备能力
      const capabilities = await this.detectCapabilities()
      
      // 更新状态
      const state = getMutableState(WebXRState)
      state.setCapabilities(capabilities)
      state.setInitialized(true)
      
      console.log('WebXR initialized successfully', capabilities)
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown WebXR error'
      getMutableState(WebXRState).setError(errorMessage)
      console.error('Failed to initialize WebXR:', error)
      throw error
    }
  }
  
  /**
   * 检测XR设备能力
   */
  private async detectCapabilities(): Promise<XRCapabilities> {
    const capabilities: XRCapabilities = {
      supportsVR: false,
      supportsAR: false,
      supportsHandTracking: false,
      supportsEyeTracking: false,
      supportedModes: []
    }
    
    try {
      // 检测VR支持
      if (await navigator.xr.isSessionSupported('immersive-vr')) {
        capabilities.supportsVR = true
        capabilities.supportedModes.push('immersive-vr')
      }
      
      // 检测AR支持
      if (await navigator.xr.isSessionSupported('immersive-ar')) {
        capabilities.supportsAR = true
        capabilities.supportedModes.push('immersive-ar')
      }
      
      // 检测内联模式支持
      if (await navigator.xr.isSessionSupported('inline')) {
        capabilities.supportedModes.push('inline')
      }
      
      // TODO: 检测手部追踪和眼部追踪支持
      // 这些功能需要在会话中检测
      
    } catch (error) {
      console.warn('Error detecting XR capabilities:', error)
    }
    
    return capabilities
  }
  
  /**
   * 请求XR会话
   */
  async requestSession(mode: XRSessionMode, options?: XRSessionInit): Promise<XRSession> {
    if (!navigator.xr) {
      throw new Error('WebXR not supported')
    }
    
    if (!this.renderer) {
      throw new Error('Renderer not initialized')
    }
    
    try {
      // 请求会话
      const session = await navigator.xr.requestSession(mode, options)
      
      // 配置渲染器
      await this.renderer.xr.setSession(session)
      
      // 获取参考空间
      const referenceSpace = await session.requestReferenceSpace('local-floor')
      
      // 设置会话事件监听器
      this.setupSessionEventListeners(session)
      
      // 更新状态
      const state = getMutableState(WebXRState)
      state.setSessionState({
        session,
        mode,
        isPresenting: true,
        referenceSpace,
        controllers: [],
        hands: []
      })
      
      this.currentSession = session
      
      console.log(`XR session started: ${mode}`)
      return session
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start XR session'
      getMutableState(WebXRState).setError(errorMessage)
      console.error('Failed to request XR session:', error)
      throw error
    }
  }
  
  /**
   * 结束XR会话
   */
  async endSession(): Promise<void> {
    if (!this.currentSession) {
      return
    }
    
    try {
      await this.currentSession.end()
      
    } catch (error) {
      console.error('Error ending XR session:', error)
    }
  }
  
  /**
   * 设置会话事件监听器
   */
  private setupSessionEventListeners(session: XRSession): void {
    session.addEventListener('end', () => {
      this.onSessionEnd()
    })
    
    session.addEventListener('inputsourceschange', (event) => {
      this.onInputSourcesChange(event)
    })
    
    session.addEventListener('select', (event) => {
      this.onSelect(event)
    })
    
    session.addEventListener('selectstart', (event) => {
      this.onSelectStart(event)
    })
    
    session.addEventListener('selectend', (event) => {
      this.onSelectEnd(event)
    })
  }
  
  /**
   * 会话结束处理
   */
  private onSessionEnd(): void {
    const state = getMutableState(WebXRState)
    state.setSessionState({
      session: null,
      mode: null,
      isPresenting: false,
      referenceSpace: null,
      controllers: [],
      hands: []
    })
    
    this.currentSession = null
    console.log('XR session ended')
  }
  
  /**
   * 输入源变化处理
   */
  private onInputSourcesChange(event: XRInputSourceChangeEvent): void {
    const state = getMutableState(WebXRState)
    const controllers = Array.from(event.session.inputSources)
    state.setSessionState({ controllers })
  }
  
  /**
   * 选择事件处理
   */
  private onSelect(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource
    const frame = event.frame

    // 获取输入源的姿态
    if (inputSource.gripSpace) {
      const gripPose = frame.getPose(inputSource.gripSpace, this.getReferenceSpace())
      if (gripPose) {
        this.handleControllerSelect(inputSource, gripPose)
      }
    }

    // 处理手部追踪选择
    if (inputSource.hand) {
      this.handleHandSelect(inputSource, frame)
    }

    console.log('XR select event:', event)
  }

  /**
   * 选择开始事件处理
   */
  private onSelectStart(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource
    const frame = event.frame

    // 开始选择交互
    if (inputSource.gripSpace) {
      const gripPose = frame.getPose(inputSource.gripSpace, this.getReferenceSpace())
      if (gripPose) {
        this.handleControllerSelectStart(inputSource, gripPose)
      }
    }

    console.log('XR select start event:', event)
  }

  /**
   * 选择结束事件处理
   */
  private onSelectEnd(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource
    const frame = event.frame

    // 结束选择交互
    if (inputSource.gripSpace) {
      const gripPose = frame.getPose(inputSource.gripSpace, this.getReferenceSpace())
      if (gripPose) {
        this.handleControllerSelectEnd(inputSource, gripPose)
      }
    }

    console.log('XR select end event:', event)
  }
  
  /**
   * 获取当前会话
   */
  getCurrentSession(): XRSession | null {
    return this.currentSession
  }
  
  /**
   * 检查是否处于XR模式
   */
  isPresenting(): boolean {
    return getState(WebXRState).session.isPresenting
  }
  
  /**
   * 获取XR设备能力
   */
  getCapabilities(): XRCapabilities {
    return getState(WebXRState).capabilities
  }

  /**
   * 获取参考空间
   */
  private getReferenceSpace(): XRReferenceSpace | null {
    return getState(WebXRState).session.referenceSpace
  }

  /**
   * 处理控制器选择
   */
  private handleControllerSelect(inputSource: XRInputSource, pose: XRPose): void {
    const position = pose.transform.position
    const orientation = pose.transform.orientation

    // 发射射线进行交互检测
    this.performRaycastInteraction(
      { x: position.x, y: position.y, z: position.z },
      { x: orientation.x, y: orientation.y, z: orientation.z, w: orientation.w }
    )
  }

  /**
   * 处理控制器选择开始
   */
  private handleControllerSelectStart(inputSource: XRInputSource, pose: XRPose): void {
    // 开始拖拽或抓取操作
    console.log('Controller select start at:', pose.transform.position)
  }

  /**
   * 处理控制器选择结束
   */
  private handleControllerSelectEnd(inputSource: XRInputSource, pose: XRPose): void {
    // 结束拖拽或抓取操作
    console.log('Controller select end at:', pose.transform.position)
  }

  /**
   * 处理手部选择
   */
  private handleHandSelect(inputSource: XRInputSource, frame: XRFrame): void {
    if (!inputSource.hand) return

    // 获取手部关节信息
    const indexTip = inputSource.hand.get('index-finger-tip')
    const thumbTip = inputSource.hand.get('thumb-tip')

    if (indexTip && thumbTip) {
      const indexPose = frame.getJointPose(indexTip, this.getReferenceSpace())
      const thumbPose = frame.getJointPose(thumbTip, this.getReferenceSpace())

      if (indexPose && thumbPose) {
        // 检测捏合手势
        const distance = this.calculateDistance(
          indexPose.transform.position,
          thumbPose.transform.position
        )

        if (distance < 0.02) { // 2cm 阈值
          this.handlePinchGesture(indexPose.transform.position)
        }
      }
    }
  }

  /**
   * 执行射线投射交互
   */
  private performRaycastInteraction(
    origin: { x: number; y: number; z: number },
    orientation: { x: number; y: number; z: number; w: number }
  ): void {
    // 计算射线方向
    const direction = this.quaternionToDirection(orientation)

    // TODO: 与物理引擎集成，执行射线投射
    console.log('Raycast interaction:', { origin, direction })
  }

  /**
   * 处理捏合手势
   */
  private handlePinchGesture(position: DOMPointReadOnly): void {
    console.log('Pinch gesture detected at:', position)
    // TODO: 触发捏合交互事件
  }

  /**
   * 计算两点间距离
   */
  private calculateDistance(a: DOMPointReadOnly, b: DOMPointReadOnly): number {
    return Math.sqrt(
      Math.pow(b.x - a.x, 2) +
      Math.pow(b.y - a.y, 2) +
      Math.pow(b.z - a.z, 2)
    )
  }

  /**
   * 四元数转方向向量
   */
  private quaternionToDirection(q: { x: number; y: number; z: number; w: number }): { x: number; y: number; z: number } {
    // 计算前向向量 (0, 0, -1) 经过四元数旋转后的结果
    return {
      x: 2 * (q.x * q.z + q.w * q.y),
      y: 2 * (q.y * q.z - q.w * q.x),
      z: -(1 - 2 * (q.x * q.x + q.y * q.y))
    }
  }

  /**
   * 更新XR帧
   */
  updateFrame(frame: XRFrame): void {
    const session = this.getCurrentSession()
    if (!session) return

    const referenceSpace = this.getReferenceSpace()
    if (!referenceSpace) return

    // 更新控制器状态
    this.updateControllers(frame, referenceSpace)

    // 更新手部追踪
    this.updateHandTracking(frame, referenceSpace)
  }

  /**
   * 更新控制器状态
   */
  private updateControllers(frame: XRFrame, referenceSpace: XRReferenceSpace): void {
    const session = this.getCurrentSession()
    if (!session) return

    const state = getMutableState(WebXRState)
    const controllers: any[] = []

    for (const inputSource of session.inputSources) {
      if (inputSource.gripSpace) {
        const gripPose = frame.getPose(inputSource.gripSpace, referenceSpace)
        if (gripPose) {
          controllers.push({
            inputSource,
            gripPose,
            connected: true
          })
        }
      }
    }

    state.setSessionState({ controllers })
  }

  /**
   * 更新手部追踪
   */
  private updateHandTracking(frame: XRFrame, referenceSpace: XRReferenceSpace): void {
    const session = this.getCurrentSession()
    if (!session) return

    const state = getMutableState(WebXRState)
    const hands: any[] = []

    for (const inputSource of session.inputSources) {
      if (inputSource.hand) {
        const handData = this.getHandData(inputSource.hand, frame, referenceSpace)
        if (handData) {
          hands.push({
            inputSource,
            handData,
            handedness: inputSource.handedness
          })
        }
      }
    }

    state.setSessionState({ hands })
  }

  /**
   * 获取手部数据
   */
  private getHandData(hand: XRHand, frame: XRFrame, referenceSpace: XRReferenceSpace): any {
    const joints: Record<string, any> = {}

    for (const [jointName, joint] of hand) {
      const pose = frame.getJointPose(joint, referenceSpace)
      if (pose) {
        joints[jointName] = {
          position: pose.transform.position,
          orientation: pose.transform.orientation,
          radius: pose.radius
        }
      }
    }

    return { joints }
  }
}
