/**
 * DL-Engine 渲染管理器测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { RendererManager } from '../rendering/RendererManager'

// Mock Three.js
const mockRenderer = {
  setSize: vi.fn(),
  setPixelRatio: vi.fn(),
  setClearColor: vi.fn(),
  render: vi.fn(),
  dispose: vi.fn(),
  domElement: document.createElement('canvas'),
  getSize: vi.fn().mockReturnValue({ width: 1920, height: 1080 }),
  getPixelRatio: vi.fn().mockReturnValue(1),
  shadowMap: {
    enabled: false,
    type: 'PCFSoftShadowMap'
  },
  outputColorSpace: 'srgb',
  toneMapping: 'ACESFilmicToneMapping',
  toneMappingExposure: 1.0
}

const mockScene = {
  add: vi.fn(),
  remove: vi.fn(),
  traverse: vi.fn(),
  children: []
}

const mockCamera = {
  aspect: 16/9,
  updateProjectionMatrix: vi.fn(),
  position: { set: vi.fn() },
  lookAt: vi.fn()
}

// Mock Three.js module
vi.mock('three', () => ({
  WebGLRenderer: vi.fn(() => mockRenderer),
  Scene: vi.fn(() => mockScene),
  PerspectiveCamera: vi.fn(() => mockCamera),
  Color: vi.fn(),
  PCFSoftShadowMap: 'PCFSoftShadowMap',
  SRGBColorSpace: 'srgb',
  ACESFilmicToneMapping: 'ACESFilmicToneMapping'
}))

describe('RendererManager', () => {
  let rendererManager: RendererManager
  let container: HTMLElement
  
  beforeEach(() => {
    // 创建测试容器
    container = document.createElement('div')
    container.style.width = '1920px'
    container.style.height = '1080px'
    document.body.appendChild(container)
    
    rendererManager = RendererManager.getInstance()
    
    // 重置mock
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    rendererManager.destroy()
    document.body.removeChild(container)
  })
  
  describe('Initialization', () => {
    it('should initialize renderer with default config', async () => {
      await rendererManager.initialize(container)
      
      expect(mockRenderer.setSize).toHaveBeenCalledWith(1920, 1080, false)
      expect(mockRenderer.setPixelRatio).toHaveBeenCalledWith(
        Math.min(window.devicePixelRatio, 2)
      )
      expect(container.appendChild).toHaveBeenCalledWith(mockRenderer.domElement)
    })
    
    it('should initialize renderer with custom config', async () => {
      const config = {
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance' as const,
        shadowMapEnabled: true,
        shadowMapType: 'PCFSoftShadowMap' as const,
        outputColorSpace: 'srgb' as const,
        toneMapping: 'ACESFilmicToneMapping' as const,
        toneMappingExposure: 1.5,
        physicallyCorrectLights: true
      }
      
      await rendererManager.initialize(container, config)
      
      expect(mockRenderer.shadowMap.enabled).toBe(true)
      expect(mockRenderer.toneMappingExposure).toBe(1.5)
    })
    
    it('should handle initialization errors gracefully', async () => {
      // Mock renderer creation failure
      const { WebGLRenderer } = await import('three')
      vi.mocked(WebGLRenderer).mockImplementationOnce(() => {
        throw new Error('WebGL not supported')
      })
      
      await expect(rendererManager.initialize(container)).rejects.toThrow('WebGL not supported')
    })
  })
  
  describe('Rendering', () => {
    beforeEach(async () => {
      await rendererManager.initialize(container)
    })
    
    it('should render scene with camera', () => {
      rendererManager.render(mockScene as any, mockCamera as any)
      
      expect(mockRenderer.render).toHaveBeenCalledWith(mockScene, mockCamera)
    })
    
    it('should handle render errors gracefully', () => {
      mockRenderer.render.mockImplementationOnce(() => {
        throw new Error('Render error')
      })
      
      expect(() => {
        rendererManager.render(mockScene as any, mockCamera as any)
      }).not.toThrow()
    })
    
    it('should update render statistics', () => {
      const initialStats = rendererManager.getStats()
      
      rendererManager.render(mockScene as any, mockCamera as any)
      
      const updatedStats = rendererManager.getStats()
      expect(updatedStats.frameCount).toBe(initialStats.frameCount + 1)
    })
  })
  
  describe('Resize Handling', () => {
    beforeEach(async () => {
      await rendererManager.initialize(container)
    })
    
    it('should resize renderer and update camera', () => {
      rendererManager.resize(1280, 720)
      
      expect(mockRenderer.setSize).toHaveBeenCalledWith(1280, 720, false)
    })
    
    it('should handle automatic resize', () => {
      // 模拟容器尺寸变化
      Object.defineProperty(container, 'clientWidth', { value: 800 })
      Object.defineProperty(container, 'clientHeight', { value: 600 })
      
      rendererManager.handleResize()
      
      expect(mockRenderer.setSize).toHaveBeenCalledWith(800, 600, false)
    })
  })
  
  describe('Configuration Updates', () => {
    beforeEach(async () => {
      await rendererManager.initialize(container)
    })
    
    it('should update pixel ratio', () => {
      rendererManager.setPixelRatio(2.0)
      
      expect(mockRenderer.setPixelRatio).toHaveBeenCalledWith(2.0)
    })
    
    it('should update clear color', () => {
      rendererManager.setClearColor('#ff0000', 0.5)
      
      expect(mockRenderer.setClearColor).toHaveBeenCalledWith('#ff0000', 0.5)
    })
    
    it('should toggle shadow mapping', () => {
      rendererManager.setShadowMapEnabled(true)
      
      expect(mockRenderer.shadowMap.enabled).toBe(true)
    })
  })
  
  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await rendererManager.initialize(container)
    })
    
    it('should track render statistics', () => {
      const stats = rendererManager.getStats()
      
      expect(stats).toHaveProperty('frameCount')
      expect(stats).toHaveProperty('renderTime')
      expect(stats).toHaveProperty('fps')
      expect(stats).toHaveProperty('memoryUsage')
    })
    
    it('should calculate FPS correctly', () => {
      // 模拟多帧渲染
      for (let i = 0; i < 60; i++) {
        rendererManager.render(mockScene as any, mockCamera as any)
      }
      
      const stats = rendererManager.getStats()
      expect(stats.frameCount).toBe(60)
      expect(stats.fps).toBeGreaterThan(0)
    })
  })
  
  describe('Error Recovery', () => {
    beforeEach(async () => {
      await rendererManager.initialize(container)
    })
    
    it('should handle context loss', () => {
      const canvas = mockRenderer.domElement
      const lostEvent = new Event('webglcontextlost')
      const restoredEvent = new Event('webglcontextrestored')
      
      // 模拟上下文丢失
      canvas.dispatchEvent(lostEvent)
      
      // 验证状态
      expect(rendererManager.isContextLost()).toBe(true)
      
      // 模拟上下文恢复
      canvas.dispatchEvent(restoredEvent)
      
      // 验证恢复
      expect(rendererManager.isContextLost()).toBe(false)
    })
    
    it('should attempt context recovery', async () => {
      const canvas = mockRenderer.domElement
      const lostEvent = new Event('webglcontextlost')
      
      canvas.dispatchEvent(lostEvent)
      
      // 尝试恢复上下文
      const recovered = await rendererManager.recoverContext()
      
      // 在测试环境中，恢复可能失败，但不应该抛出错误
      expect(typeof recovered).toBe('boolean')
    })
  })
  
  describe('Memory Management', () => {
    beforeEach(async () => {
      await rendererManager.initialize(container)
    })
    
    it('should dispose resources properly', () => {
      rendererManager.destroy()
      
      expect(mockRenderer.dispose).toHaveBeenCalled()
    })
    
    it('should track memory usage', () => {
      const stats = rendererManager.getStats()
      
      expect(stats.memoryUsage).toBeGreaterThanOrEqual(0)
      expect(stats.memoryUsage.geometries).toBeGreaterThanOrEqual(0)
      expect(stats.memoryUsage.textures).toBeGreaterThanOrEqual(0)
    })
  })
  
  describe('Singleton Pattern', () => {
    it('should return same instance', () => {
      const instance1 = RendererManager.getInstance()
      const instance2 = RendererManager.getInstance()
      
      expect(instance1).toBe(instance2)
    })
    
    it('should maintain state across getInstance calls', async () => {
      const instance1 = RendererManager.getInstance()
      await instance1.initialize(container)
      
      const instance2 = RendererManager.getInstance()
      expect(instance2.isInitialized()).toBe(true)
    })
  })
})
