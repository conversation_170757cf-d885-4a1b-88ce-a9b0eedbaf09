/**
 * DL-Engine 测试覆盖率检查脚本
 * 运行所有测试并生成覆盖率报告
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync, writeFileSync } from 'fs'
import { join } from 'path'

interface CoverageReport {
  module: string
  statements: number
  branches: number
  functions: number
  lines: number
  uncoveredLines: string[]
}

interface TestSuite {
  name: string
  path: string
  testFiles: string[]
  coverageThreshold: number
}

/**
 * 测试套件配置
 */
const testSuites: TestSuite[] = [
  {
    name: 'Core Engine',
    path: 'engine/core',
    testFiles: ['src/__tests__/**/*.test.ts'],
    coverageThreshold: 80
  },
  {
    name: 'ECS System',
    path: 'engine/ecs',
    testFiles: ['src/__tests__/**/*.test.ts'],
    coverageThreshold: 85
  },
  {
    name: 'Physics Engine',
    path: 'engine/physics',
    testFiles: ['src/__tests__/**/*.test.ts'],
    coverageThreshold: 75
  },
  {
    name: 'State Management',
    path: 'engine/state',
    testFiles: ['src/__tests__/**/*.test.ts'],
    coverageThreshold: 90
  },
  {
    name: 'XR Module',
    path: 'engine/xr',
    testFiles: ['src/__tests__/**/*.test.ts'],
    coverageThreshold: 70
  },
  {
    name: 'AI Module',
    path: 'engine/ai',
    testFiles: ['src/__tests__/**/*.test.ts'],
    coverageThreshold: 75
  }
]

/**
 * 运行单个模块的测试
 */
function runModuleTests(suite: TestSuite): CoverageReport {
  const modulePath = join(process.cwd(), suite.path)
  
  console.log(`\n🧪 Running tests for ${suite.name}...`)
  
  try {
    // 检查是否存在测试文件
    const hasTests = suite.testFiles.some(pattern => {
      const testPath = join(modulePath, pattern.replace('**/*', ''))
      return existsSync(testPath)
    })
    
    if (!hasTests) {
      console.log(`⚠️  No test files found for ${suite.name}`)
      return {
        module: suite.name,
        statements: 0,
        branches: 0,
        functions: 0,
        lines: 0,
        uncoveredLines: []
      }
    }
    
    // 运行测试并生成覆盖率报告
    const command = `cd ${modulePath} && npm run test:coverage`
    
    try {
      execSync(command, { stdio: 'pipe' })
    } catch (error) {
      // 如果没有test:coverage脚本，尝试使用vitest
      try {
        execSync(`cd ${modulePath} && npx vitest run --coverage`, { stdio: 'pipe' })
      } catch (vitestError) {
        console.log(`⚠️  Could not run tests for ${suite.name}: No test runner configured`)
        return {
          module: suite.name,
          statements: 0,
          branches: 0,
          functions: 0,
          lines: 0,
          uncoveredLines: []
        }
      }
    }
    
    // 解析覆盖率报告
    const coverageReport = parseCoverageReport(modulePath)
    
    console.log(`✅ Tests completed for ${suite.name}`)
    console.log(`   Lines: ${coverageReport.lines}%`)
    console.log(`   Statements: ${coverageReport.statements}%`)
    console.log(`   Functions: ${coverageReport.functions}%`)
    console.log(`   Branches: ${coverageReport.branches}%`)
    
    return coverageReport
    
  } catch (error) {
    console.error(`❌ Error running tests for ${suite.name}:`, error)
    return {
      module: suite.name,
      statements: 0,
      branches: 0,
      functions: 0,
      lines: 0,
      uncoveredLines: []
    }
  }
}

/**
 * 解析覆盖率报告
 */
function parseCoverageReport(modulePath: string): CoverageReport {
  const coverageFile = join(modulePath, 'coverage/coverage-summary.json')
  
  if (!existsSync(coverageFile)) {
    // 如果没有覆盖率文件，返回默认值
    return {
      module: modulePath,
      statements: 0,
      branches: 0,
      functions: 0,
      lines: 0,
      uncoveredLines: []
    }
  }
  
  try {
    const coverageData = JSON.parse(readFileSync(coverageFile, 'utf-8'))
    const total = coverageData.total
    
    return {
      module: modulePath,
      statements: total.statements.pct || 0,
      branches: total.branches.pct || 0,
      functions: total.functions.pct || 0,
      lines: total.lines.pct || 0,
      uncoveredLines: extractUncoveredLines(modulePath)
    }
  } catch (error) {
    console.warn(`Warning: Could not parse coverage report for ${modulePath}`)
    return {
      module: modulePath,
      statements: 0,
      branches: 0,
      functions: 0,
      lines: 0,
      uncoveredLines: []
    }
  }
}

/**
 * 提取未覆盖的代码行
 */
function extractUncoveredLines(modulePath: string): string[] {
  const lcovFile = join(modulePath, 'coverage/lcov.info')
  
  if (!existsSync(lcovFile)) {
    return []
  }
  
  try {
    const lcovContent = readFileSync(lcovFile, 'utf-8')
    const uncoveredLines: string[] = []
    
    // 解析LCOV格式，提取未覆盖的行
    const lines = lcovContent.split('\n')
    let currentFile = ''
    
    for (const line of lines) {
      if (line.startsWith('SF:')) {
        currentFile = line.substring(3)
      } else if (line.startsWith('DA:')) {
        const [lineNum, hitCount] = line.substring(3).split(',')
        if (hitCount === '0') {
          uncoveredLines.push(`${currentFile}:${lineNum}`)
        }
      }
    }
    
    return uncoveredLines.slice(0, 10)  // 只返回前10个未覆盖的行
  } catch (error) {
    return []
  }
}

/**
 * 生成覆盖率报告
 */
function generateCoverageReport(reports: CoverageReport[]): void {
  console.log('\n📊 Coverage Report Summary')
  console.log('=' .repeat(80))
  
  let totalStatements = 0
  let totalBranches = 0
  let totalFunctions = 0
  let totalLines = 0
  let moduleCount = 0
  
  const reportLines: string[] = []
  
  for (const report of reports) {
    if (report.lines > 0) {  // 只统计有测试的模块
      totalStatements += report.statements
      totalBranches += report.branches
      totalFunctions += report.functions
      totalLines += report.lines
      moduleCount++
    }
    
    const status = report.lines >= 80 ? '✅' : report.lines >= 60 ? '⚠️' : '❌'
    const line = `${status} ${report.module.padEnd(20)} Lines: ${report.lines.toFixed(1)}% | Statements: ${report.statements.toFixed(1)}% | Functions: ${report.functions.toFixed(1)}% | Branches: ${report.branches.toFixed(1)}%`
    
    reportLines.push(line)
    console.log(line)
  }
  
  console.log('=' .repeat(80))
  
  if (moduleCount > 0) {
    const avgStatements = totalStatements / moduleCount
    const avgBranches = totalBranches / moduleCount
    const avgFunctions = totalFunctions / moduleCount
    const avgLines = totalLines / moduleCount
    
    console.log(`📈 Average Coverage:`)
    console.log(`   Lines: ${avgLines.toFixed(1)}%`)
    console.log(`   Statements: ${avgStatements.toFixed(1)}%`)
    console.log(`   Functions: ${avgFunctions.toFixed(1)}%`)
    console.log(`   Branches: ${avgBranches.toFixed(1)}%`)
    
    // 评估整体覆盖率
    if (avgLines >= 80) {
      console.log('\n🎉 Excellent test coverage! Target achieved.')
    } else if (avgLines >= 60) {
      console.log('\n👍 Good test coverage, but there\'s room for improvement.')
    } else {
      console.log('\n⚠️  Test coverage needs improvement. Consider adding more tests.')
    }
  } else {
    console.log('\n⚠️  No test coverage data available.')
  }
  
  // 生成详细报告文件
  const detailedReport = {
    timestamp: new Date().toISOString(),
    summary: {
      totalModules: reports.length,
      testedModules: moduleCount,
      averageCoverage: moduleCount > 0 ? {
        lines: totalLines / moduleCount,
        statements: totalStatements / moduleCount,
        functions: totalFunctions / moduleCount,
        branches: totalBranches / moduleCount
      } : null
    },
    modules: reports
  }
  
  writeFileSync(
    join(process.cwd(), 'coverage-report.json'),
    JSON.stringify(detailedReport, null, 2)
  )
  
  console.log('\n📄 Detailed report saved to coverage-report.json')
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  console.log('🚀 Starting DL-Engine Test Coverage Analysis...')
  
  const reports: CoverageReport[] = []
  
  // 运行所有测试套件
  for (const suite of testSuites) {
    const report = runModuleTests(suite)
    reports.push(report)
  }
  
  // 生成总体报告
  generateCoverageReport(reports)
  
  console.log('\n✨ Test coverage analysis completed!')
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Error running test coverage analysis:', error)
    process.exit(1)
  })
}

export { main as runCoverageAnalysis }
