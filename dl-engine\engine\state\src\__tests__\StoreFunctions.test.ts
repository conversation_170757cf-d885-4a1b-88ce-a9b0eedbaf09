/**
 * DL-Engine 状态管理测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { 
  defineState, 
  getMutableState, 
  getState, 
  useState,
  dispatchAction,
  addActionReceptor,
  removeActionReceptor
} from '../StoreFunctions'

// 测试状态定义
interface TestState {
  count: number
  name: string
  items: string[]
  nested: {
    value: number
    active: boolean
  }
}

const TestStateDefinition = defineState({
  name: 'TestState',
  initial: (): TestState => ({
    count: 0,
    name: 'test',
    items: [],
    nested: {
      value: 42,
      active: true
    }
  }),
  
  receptors: {
    increment: (state) => {
      state.count.set(state.count.value + 1)
    },
    
    decrement: (state) => {
      state.count.set(state.count.value - 1)
    },
    
    setName: (state, name: string) => {
      state.name.set(name)
    },
    
    addItem: (state, item: string) => {
      state.items.get(NO_PROXY).push(item)
    },
    
    removeItem: (state, index: number) => {
      state.items.get(NO_PROXY).splice(index, 1)
    },
    
    setNestedValue: (state, value: number) => {
      state.nested.value.set(value)
    },
    
    toggleActive: (state) => {
      state.nested.active.set(!state.nested.active.value)
    },
    
    reset: (state) => {
      state.count.set(0)
      state.name.set('test')
      state.items.set([])
      state.nested.value.set(42)
      state.nested.active.set(true)
    }
  }
})

// 导入NO_PROXY
import { NO_PROXY } from '../StoreFunctions'

describe('StoreFunctions', () => {
  beforeEach(() => {
    // 重置状态
    const mutableState = getMutableState(TestStateDefinition)
    mutableState.reset()
  })
  
  describe('State Definition', () => {
    it('should define state with initial values', () => {
      const state = getState(TestStateDefinition)
      
      expect(state.count).toBe(0)
      expect(state.name).toBe('test')
      expect(state.items).toEqual([])
      expect(state.nested.value).toBe(42)
      expect(state.nested.active).toBe(true)
    })
    
    it('should create unique state instances', () => {
      const AnotherState = defineState({
        name: 'AnotherState',
        initial: () => ({ value: 100 }),
        receptors: {}
      })
      
      const testState = getState(TestStateDefinition)
      const anotherState = getState(AnotherState)
      
      expect(testState).not.toBe(anotherState)
      expect(testState.count).toBe(0)
      expect(anotherState.value).toBe(100)
    })
  })
  
  describe('State Access', () => {
    it('should get immutable state', () => {
      const state = getState(TestStateDefinition)
      
      expect(state.count).toBe(0)
      expect(state.name).toBe('test')
      
      // 尝试修改应该不会影响状态
      expect(() => {
        (state as any).count = 999
      }).not.toThrow()
      
      // 状态应该保持不变
      const stateAfter = getState(TestStateDefinition)
      expect(stateAfter.count).toBe(0)
    })
    
    it('should get mutable state for modifications', () => {
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.count.set(10)
      mutableState.name.set('modified')
      
      const state = getState(TestStateDefinition)
      expect(state.count).toBe(10)
      expect(state.name).toBe('modified')
    })
  })
  
  describe('State Mutations', () => {
    it('should update primitive values', () => {
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.increment()
      expect(getState(TestStateDefinition).count).toBe(1)
      
      mutableState.increment()
      expect(getState(TestStateDefinition).count).toBe(2)
      
      mutableState.decrement()
      expect(getState(TestStateDefinition).count).toBe(1)
    })
    
    it('should update string values', () => {
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.setName('new name')
      expect(getState(TestStateDefinition).name).toBe('new name')
    })
    
    it('should update array values', () => {
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.addItem('item1')
      mutableState.addItem('item2')
      
      const state = getState(TestStateDefinition)
      expect(state.items).toEqual(['item1', 'item2'])
      
      mutableState.removeItem(0)
      expect(getState(TestStateDefinition).items).toEqual(['item2'])
    })
    
    it('should update nested object values', () => {
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.setNestedValue(100)
      expect(getState(TestStateDefinition).nested.value).toBe(100)
      
      mutableState.toggleActive()
      expect(getState(TestStateDefinition).nested.active).toBe(false)
      
      mutableState.toggleActive()
      expect(getState(TestStateDefinition).nested.active).toBe(true)
    })
  })
  
  describe('State Reactivity', () => {
    it('should trigger reactions on state changes', () => {
      let reactionCount = 0
      let lastValue = 0
      
      const unsubscribe = useState(TestStateDefinition).count.subscribe((value) => {
        reactionCount++
        lastValue = value
      })
      
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.increment()
      expect(reactionCount).toBe(1)
      expect(lastValue).toBe(1)
      
      mutableState.increment()
      expect(reactionCount).toBe(2)
      expect(lastValue).toBe(2)
      
      unsubscribe()
      
      mutableState.increment()
      expect(reactionCount).toBe(2) // 不应该再触发
      expect(lastValue).toBe(2)
    })
    
    it('should handle multiple subscribers', () => {
      let reaction1Count = 0
      let reaction2Count = 0
      
      const unsubscribe1 = useState(TestStateDefinition).count.subscribe(() => {
        reaction1Count++
      })
      
      const unsubscribe2 = useState(TestStateDefinition).count.subscribe(() => {
        reaction2Count++
      })
      
      const mutableState = getMutableState(TestStateDefinition)
      mutableState.increment()
      
      expect(reaction1Count).toBe(1)
      expect(reaction2Count).toBe(1)
      
      unsubscribe1()
      mutableState.increment()
      
      expect(reaction1Count).toBe(1) // 不应该再触发
      expect(reaction2Count).toBe(2)
      
      unsubscribe2()
    })
    
    it('should handle nested property subscriptions', () => {
      let nestedReactionCount = 0
      let lastNestedValue = 0
      
      const unsubscribe = useState(TestStateDefinition).nested.value.subscribe((value) => {
        nestedReactionCount++
        lastNestedValue = value
      })
      
      const mutableState = getMutableState(TestStateDefinition)
      
      mutableState.setNestedValue(200)
      expect(nestedReactionCount).toBe(1)
      expect(lastNestedValue).toBe(200)
      
      unsubscribe()
    })
  })
  
  describe('Action System', () => {
    it('should dispatch actions through receptors', () => {
      dispatchAction(TestStateDefinition.increment())
      expect(getState(TestStateDefinition).count).toBe(1)
      
      dispatchAction(TestStateDefinition.setName('action test'))
      expect(getState(TestStateDefinition).name).toBe('action test')
    })
    
    it('should handle action parameters', () => {
      dispatchAction(TestStateDefinition.addItem('action item'))
      expect(getState(TestStateDefinition).items).toContain('action item')
      
      dispatchAction(TestStateDefinition.setNestedValue(999))
      expect(getState(TestStateDefinition).nested.value).toBe(999)
    })
    
    it('should support action interceptors', () => {
      const interceptedActions: any[] = []
      
      const removeInterceptor = addActionReceptor((action) => {
        interceptedActions.push(action)
      })
      
      dispatchAction(TestStateDefinition.increment())
      dispatchAction(TestStateDefinition.setName('intercepted'))
      
      expect(interceptedActions).toHaveLength(2)
      expect(interceptedActions[0].type).toBe('TestState.increment')
      expect(interceptedActions[1].type).toBe('TestState.setName')
      
      removeInterceptor()
      
      dispatchAction(TestStateDefinition.increment())
      expect(interceptedActions).toHaveLength(2) // 不应该再增加
    })
  })
  
  describe('Performance', () => {
    it('should handle many state updates efficiently', () => {
      const startTime = performance.now()
      
      const mutableState = getMutableState(TestStateDefinition)
      
      // 执行大量状态更新
      for (let i = 0; i < 1000; i++) {
        mutableState.increment()
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(100) // 应该在100ms内完成
      expect(getState(TestStateDefinition).count).toBe(1000)
    })
    
    it('should handle many subscribers efficiently', () => {
      const subscribers: (() => void)[] = []
      let totalReactions = 0
      
      // 创建大量订阅者
      for (let i = 0; i < 100; i++) {
        const unsubscribe = useState(TestStateDefinition).count.subscribe(() => {
          totalReactions++
        })
        subscribers.push(unsubscribe)
      }
      
      const startTime = performance.now()
      
      const mutableState = getMutableState(TestStateDefinition)
      mutableState.increment()
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(50) // 应该在50ms内完成
      expect(totalReactions).toBe(100)
      
      // 清理订阅者
      subscribers.forEach(unsubscribe => unsubscribe())
    })
  })
  
  describe('Memory Management', () => {
    it('should clean up subscriptions properly', () => {
      const subscriptions: (() => void)[] = []
      
      // 创建订阅
      for (let i = 0; i < 10; i++) {
        const unsubscribe = useState(TestStateDefinition).count.subscribe(() => {})
        subscriptions.push(unsubscribe)
      }
      
      // 取消订阅
      subscriptions.forEach(unsubscribe => unsubscribe())
      
      // 状态更新不应该触发已取消的订阅
      const mutableState = getMutableState(TestStateDefinition)
      expect(() => {
        mutableState.increment()
      }).not.toThrow()
    })
    
    it('should handle state cleanup', () => {
      // 创建临时状态
      const TempState = defineState({
        name: 'TempState',
        initial: () => ({ value: 0 }),
        receptors: {
          setValue: (state, value: number) => {
            state.value.set(value)
          }
        }
      })
      
      const mutableState = getMutableState(TempState)
      mutableState.setValue(42)
      
      expect(getState(TempState).value).toBe(42)
      
      // 状态应该可以被垃圾回收（在实际实现中）
      // 这里只是确保没有内存泄漏的迹象
    })
  })
  
  describe('Error Handling', () => {
    it('should handle receptor errors gracefully', () => {
      const ErrorState = defineState({
        name: 'ErrorState',
        initial: () => ({ value: 0 }),
        receptors: {
          throwError: () => {
            throw new Error('Test error')
          },
          setValue: (state, value: number) => {
            state.value.set(value)
          }
        }
      })
      
      // 错误的receptor不应该崩溃系统
      expect(() => {
        dispatchAction(ErrorState.throwError())
      }).not.toThrow()
      
      // 其他receptor应该仍然工作
      dispatchAction(ErrorState.setValue(42))
      expect(getState(ErrorState).value).toBe(42)
    })
    
    it('should handle subscription errors gracefully', () => {
      const errorSubscription = useState(TestStateDefinition).count.subscribe(() => {
        throw new Error('Subscription error')
      })
      
      const normalSubscription = useState(TestStateDefinition).count.subscribe(() => {
        // 正常订阅
      })
      
      // 订阅错误不应该影响状态更新
      expect(() => {
        const mutableState = getMutableState(TestStateDefinition)
        mutableState.increment()
      }).not.toThrow()
      
      errorSubscription()
      normalSubscription()
    })
  })
})
