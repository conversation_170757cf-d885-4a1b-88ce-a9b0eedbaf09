{"extends": "../../tsconfig.json", "compilerOptions": {"composite": true, "noEmit": false, "allowImportingTsExtensions": false, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@dl-engine/engine-ecs": ["../ecs/src"], "@dl-engine/engine-state": ["../state/src"], "@dl-engine/shared-common": ["../../shared/common/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}