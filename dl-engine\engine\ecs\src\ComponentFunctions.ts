/**
 * DL-Engine 组件系统核心功能
 * 基于bitECS的组件定义、创建、管理功能
 */

import * as bitECS from 'bitecs'
import { useHookstate, State, none } from '@dl-engine/engine-state'
import { generateUUID } from '@dl-engine/shared-common'
import { Entity, UndefinedEntity } from './Entity'
import { Engine } from './Engine'

/**
 * 组件类型定义
 */
export type ComponentType<T = any> = {
  name: string
  schema: T
  map: Map<Entity, T>
  exists: Uint8Array
  storageSize: number
  isTag: boolean
  onAdd?: (entity: Entity, component: T) => void
  onRemove?: (entity: Entity, component: T) => void
  onSet?: (entity: Entity, component: T) => void
}

/**
 * 组件定义选项
 */
export interface ComponentDefinition<T = any> {
  name: string
  schema?: T
  onAdd?: (entity: Entity, component: T) => void
  onRemove?: (entity: Entity, component: T) => void
  onSet?: (entity: Entity, component: T) => void
}

/**
 * 组件映射表
 */
export const ComponentMap = new Map<string, ComponentType>()

/**
 * 已移除组件标记
 */
export const $RemovedComponent = {
  exists: new Uint8Array(100000),
  storageSize: 100000
}

/**
 * 定义组件
 * @param definition 组件定义
 * @returns 组件类型
 */
export function defineComponent<T = any>(definition: ComponentDefinition<T>): ComponentType<T> {
  if (ComponentMap.has(definition.name)) {
    throw new Error(`Component ${definition.name} already exists`)
  }

  const isTag = !definition.schema || Object.keys(definition.schema).length === 0

  const component: ComponentType<T> = {
    name: definition.name,
    schema: definition.schema || ({} as T),
    map: new Map<Entity, T>(),
    exists: new Uint8Array(100000),
    storageSize: 100000,
    isTag,
    onAdd: definition.onAdd,
    onRemove: definition.onRemove,
    onSet: definition.onSet
  }

  ComponentMap.set(definition.name, component)
  return component
}

/**
 * 创建实体
 * @param layerID 层级ID（可选）
 * @returns 新创建的实体
 */
export function createEntity(layerID?: string): Entity {
  if (!Engine.instance?.store) {
    throw new Error('Engine not initialized')
  }

  const entity = bitECS.addEntity(Engine.instance.store) as Entity
  
  // 确保存储空间足够
  if ($RemovedComponent.exists.length <= entity) {
    const nextSize = nextPowerOf2(entity + 1)
    if ($RemovedComponent.storageSize < nextSize) {
      resizeRemovedComponent(nextSize)
    }
  }
  
  $RemovedComponent.exists[entity] = 1

  // 如果指定了层级，设置层级组件
  if (layerID) {
    // 这里需要LayerComponent的实现
    // setComponent(entity, LayerComponent, layerID)
  }

  return entity
}

/**
 * 移除实体
 * @param entity 要移除的实体
 */
export function removeEntity(entity: Entity): void {
  if (!entityExists(entity)) {
    return
  }

  // 移除所有组件
  for (const [, component] of ComponentMap) {
    if (hasComponent(entity, component)) {
      removeComponent(entity, component)
    }
  }

  // 标记为已移除
  $RemovedComponent.exists[entity] = 0

  // 从bitECS中移除
  if (Engine.instance?.store) {
    bitECS.removeEntity(Engine.instance.store, entity)
  }
}

/**
 * 检查实体是否存在
 * @param entity 实体
 * @returns 是否存在
 */
export function entityExists(entity: Entity): boolean {
  if (entity === UndefinedEntity) return false
  return $RemovedComponent.exists[entity] === 1
}

/**
 * 设置组件
 * @param entity 实体
 * @param component 组件类型
 * @param data 组件数据（可选）
 */
export function setComponent<T>(
  entity: Entity,
  component: ComponentType<T>,
  data?: Partial<T>
): void {
  if (!entityExists(entity)) {
    throw new Error(`Entity ${entity} does not exist`)
  }

  // 确保组件存储空间足够
  if (component.exists.length <= entity) {
    const nextSize = nextPowerOf2(entity + 1)
    if (component.storageSize < nextSize) {
      resizeComponent(component, nextSize)
    }
  }

  const hadComponent = hasComponent(entity, component)
  
  // 创建组件数据
  let componentData: T
  if (component.isTag) {
    componentData = {} as T
  } else {
    componentData = { ...component.schema, ...data } as T
  }

  // 设置组件
  component.map.set(entity, componentData)
  component.exists[entity] = 1

  // 调用回调
  if (!hadComponent && component.onAdd) {
    component.onAdd(entity, componentData)
  }
  if (component.onSet) {
    component.onSet(entity, componentData)
  }
}

/**
 * 获取组件
 * @param entity 实体
 * @param component 组件类型
 * @returns 组件数据
 */
export function getComponent<T>(entity: Entity, component: ComponentType<T>): T {
  if (!hasComponent(entity, component)) {
    throw new Error(`Entity ${entity} does not have component ${component.name}`)
  }
  return component.map.get(entity)!
}

/**
 * 获取可选组件
 * @param entity 实体
 * @param component 组件类型
 * @returns 组件数据或undefined
 */
export function getOptionalComponent<T>(
  entity: Entity,
  component: ComponentType<T>
): T | undefined {
  return component.map.get(entity)
}

/**
 * 获取可变组件
 * @param entity 实体
 * @param component 组件类型
 * @returns 可变组件数据
 */
export function getMutableComponent<T>(entity: Entity, component: ComponentType<T>): T {
  return getComponent(entity, component)
}

/**
 * 检查实体是否有组件
 * @param entity 实体
 * @param component 组件类型
 * @returns 是否有组件
 */
export function hasComponent<T>(entity: Entity, component: ComponentType<T>): boolean {
  if (!entityExists(entity)) return false
  return component.exists[entity] === 1
}

/**
 * 检查实体是否有多个组件
 * @param entity 实体
 * @param components 组件类型数组
 * @returns 是否有所有组件
 */
export function hasComponents(entity: Entity, ...components: ComponentType[]): boolean {
  return components.every(component => hasComponent(entity, component))
}

/**
 * 移除组件
 * @param entity 实体
 * @param component 组件类型
 */
export function removeComponent<T>(entity: Entity, component: ComponentType<T>): void {
  if (!hasComponent(entity, component)) {
    return
  }

  const componentData = component.map.get(entity)!
  
  // 调用移除回调
  if (component.onRemove) {
    component.onRemove(entity, componentData)
  }

  // 移除组件
  component.map.delete(entity)
  component.exists[entity] = 0
}

/**
 * 获取实体的所有组件
 * @param entity 实体
 * @returns 组件名称数组
 */
export function getAllComponents(entity: Entity): string[] {
  const components: string[] = []
  for (const [name, component] of ComponentMap) {
    if (hasComponent(entity, component)) {
      components.push(name)
    }
  }
  return components
}

/**
 * React Hook: 使用组件
 * @param entity 实体
 * @param component 组件类型
 * @returns 组件状态
 */
export function useComponent<T>(entity: Entity, component: ComponentType<T>): State<T> {
  const state = useHookstate(() => {
    if (hasComponent(entity, component)) {
      return getComponent(entity, component)
    }
    return none
  })

  // 监听组件变化
  // 这里需要实现组件变化监听机制

  return state
}

/**
 * React Hook: 使用可选组件
 * @param entity 实体
 * @param component 组件类型
 * @returns 可选组件状态
 */
export function useOptionalComponent<T>(
  entity: Entity,
  component: ComponentType<T>
): State<T | undefined> {
  const state = useHookstate(() => getOptionalComponent(entity, component))
  return state
}

// 工具函数

/**
 * 计算下一个2的幂
 */
function nextPowerOf2(n: number): number {
  return Math.pow(2, Math.ceil(Math.log2(n)))
}

/**
 * 调整已移除组件存储大小
 */
function resizeRemovedComponent(newSize: number): void {
  const newExists = new Uint8Array(newSize)
  newExists.set($RemovedComponent.exists)
  $RemovedComponent.exists = newExists
  $RemovedComponent.storageSize = newSize
}

/**
 * 调整组件存储大小
 */
function resizeComponent<T>(component: ComponentType<T>, newSize: number): void {
  const newExists = new Uint8Array(newSize)
  newExists.set(component.exists)
  component.exists = newExists
  component.storageSize = newSize
}
