/**
 * Digital Learning Engine (DL-Engine)
 * 面向教育场景的3D/VR/AR应用开发平台
 * 
 * 基于现有的 Infinite Reality Engine 项目重构
 * 采用现代化的分布式微服务架构
 * 专注于数字化学习和教育场景
 */

// 导出引擎核心模块
export * from './engine/core'
export * from './engine/ecs'
export * from './engine/physics'
export * from './engine/state'

// 导出共享模块
export * from './shared/common'

// 版本信息
export const DL_ENGINE_VERSION = '1.0.0'
export const DL_ENGINE_NAME = 'Digital Learning Engine'

// 引擎初始化函数
export { initializeDLEngine } from './engine/core/src/initialization'

// 类型定义
export type * from './shared/common/src/types'
