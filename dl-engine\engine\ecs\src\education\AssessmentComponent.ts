/**
 * DL-Engine 评估组件
 * 处理教育场景中的评估和测验
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 评估类型枚举
 */
export enum AssessmentType {
  QUIZ = 'quiz',
  EXAM = 'exam',
  ASSIGNMENT = 'assignment',
  PROJECT = 'project',
  PRACTICAL = 'practical',
  PEER_REVIEW = 'peer_review',
  SELF_ASSESSMENT = 'self_assessment'
}

/**
 * 问题类型枚举
 */
export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  FILL_BLANK = 'fill_blank',
  MATCHING = 'matching',
  ORDERING = 'ordering',
  INTERACTIVE_3D = 'interactive_3d'
}

/**
 * 评估状态枚举
 */
export enum AssessmentStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  SUBMITTED = 'submitted',
  GRADED = 'graded',
  REVIEWED = 'reviewed',
  EXPIRED = 'expired'
}

/**
 * 问题选项
 */
export interface QuestionOption {
  id: string
  text: string
  isCorrect: boolean
  explanation?: string
  points: number
}

/**
 * 评估问题
 */
export interface AssessmentQuestion {
  id: string
  type: QuestionType
  title: string
  content: string
  options: QuestionOption[]
  correctAnswer: string | string[]
  points: number
  timeLimit: number // 秒
  difficulty: number // 1-5
  category: string
  tags: string[]
  hints: string[]
  explanation: string
  mediaUrl?: string
  interactiveData?: any // 3D互动数据
}

/**
 * 学生答案
 */
export interface StudentAnswer {
  questionId: string
  answer: string | string[]
  isCorrect: boolean
  pointsEarned: number
  timeSpent: number
  attempts: number
  submittedAt: Date
  feedback?: string
}

/**
 * 评估结果
 */
export interface AssessmentResult {
  totalQuestions: number
  answeredQuestions: number
  correctAnswers: number
  totalPoints: number
  earnedPoints: number
  percentage: number
  grade: string
  timeSpent: number
  startTime: Date
  endTime: Date
  passed: boolean
  feedback: string
}

/**
 * 评估配置
 */
export interface AssessmentConfig {
  /** 时间限制（分钟） */
  timeLimit: number
  
  /** 最大尝试次数 */
  maxAttempts: number
  
  /** 通过分数 */
  passingScore: number
  
  /** 是否允许回看 */
  allowReview: boolean
  
  /** 是否随机题目顺序 */
  randomizeQuestions: boolean
  
  /** 是否随机选项顺序 */
  randomizeOptions: boolean
  
  /** 是否显示即时反馈 */
  showImmediateFeedback: boolean
  
  /** 是否允许跳过 */
  allowSkip: boolean
  
  /** 自动提交 */
  autoSubmit: boolean
}

/**
 * 评估数据
 */
export interface AssessmentData {
  /** 评估ID */
  id: string
  
  /** 评估标题 */
  title: string
  
  /** 评估描述 */
  description: string
  
  /** 评估类型 */
  type: AssessmentType
  
  /** 当前状态 */
  status: AssessmentStatus
  
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 问题列表 */
  questions: AssessmentQuestion[]
  
  /** 学生答案 */
  answers: StudentAnswer[]
  
  /** 评估结果 */
  result: AssessmentResult | null
  
  /** 评估配置 */
  config: AssessmentConfig
  
  /** 当前问题索引 */
  currentQuestionIndex: number
  
  /** 开始时间 */
  startTime: Date | null
  
  /** 结束时间 */
  endTime: Date | null
  
  /** 剩余时间（秒） */
  remainingTime: number
  
  /** 尝试次数 */
  attemptCount: number
  
  /** 是否已提交 */
  isSubmitted: boolean
  
  /** 评分者ID */
  graderId?: string
  
  /** 评分时间 */
  gradedAt?: Date
  
  /** 评分备注 */
  graderNotes?: string
}

/**
 * 评估组件
 */
export const AssessmentComponent = defineComponent({
  name: 'Assessment',
  schema: {
    id: '',
    title: '',
    description: '',
    type: AssessmentType.QUIZ,
    status: AssessmentStatus.NOT_STARTED,
    learnerId: '',
    courseId: '',
    questions: [] as AssessmentQuestion[],
    answers: [] as StudentAnswer[],
    result: null as AssessmentResult | null,
    config: {
      timeLimit: 60,
      maxAttempts: 3,
      passingScore: 70,
      allowReview: true,
      randomizeQuestions: false,
      randomizeOptions: false,
      showImmediateFeedback: false,
      allowSkip: false,
      autoSubmit: true
    } as AssessmentConfig,
    currentQuestionIndex: 0,
    startTime: null as Date | null,
    endTime: null as Date | null,
    remainingTime: 0,
    attemptCount: 0,
    isSubmitted: false,
    graderId: undefined as string | undefined,
    gradedAt: undefined as Date | undefined,
    graderNotes: undefined as string | undefined
  } as AssessmentData,
  
  onAdd: (entity, component) => {
    console.log(`Assessment component added: ${component.title}`)
    component.remainingTime = component.config.timeLimit * 60 // 转换为秒
  },
  
  onRemove: (entity, component) => {
    console.log(`Assessment component removed: ${component.title}`)
  }
})

/**
 * 评估工具函数
 */
export const AssessmentUtils = {
  /**
   * 开始评估
   */
  startAssessment: (assessment: AssessmentData): boolean => {
    if (assessment.status !== AssessmentStatus.NOT_STARTED) {
      return false
    }
    
    if (assessment.attemptCount >= assessment.config.maxAttempts) {
      return false
    }
    
    assessment.status = AssessmentStatus.IN_PROGRESS
    assessment.startTime = new Date()
    assessment.currentQuestionIndex = 0
    assessment.attemptCount++
    assessment.remainingTime = assessment.config.timeLimit * 60
    
    // 随机化问题顺序
    if (assessment.config.randomizeQuestions) {
      assessment.questions = [...assessment.questions].sort(() => Math.random() - 0.5)
    }
    
    // 随机化选项顺序
    if (assessment.config.randomizeOptions) {
      assessment.questions.forEach(question => {
        if (question.options.length > 0) {
          question.options = [...question.options].sort(() => Math.random() - 0.5)
        }
      })
    }
    
    console.log(`Assessment started: ${assessment.title}`)
    return true
  },
  
  /**
   * 提交答案
   */
  submitAnswer: (assessment: AssessmentData, questionId: string, answer: string | string[]): boolean => {
    if (assessment.status !== AssessmentStatus.IN_PROGRESS) {
      return false
    }
    
    const question = assessment.questions.find(q => q.id === questionId)
    if (!question) {
      return false
    }
    
    // 检查是否已经回答过
    const existingAnswerIndex = assessment.answers.findIndex(a => a.questionId === questionId)
    
    // 计算分数
    const isCorrect = AssessmentUtils.checkAnswer(question, answer)
    const pointsEarned = isCorrect ? question.points : 0
    
    const studentAnswer: StudentAnswer = {
      questionId,
      answer,
      isCorrect,
      pointsEarned,
      timeSpent: 0, // 需要从外部计算
      attempts: 1,
      submittedAt: new Date()
    }
    
    if (existingAnswerIndex >= 0) {
      // 更新现有答案
      assessment.answers[existingAnswerIndex] = studentAnswer
      assessment.answers[existingAnswerIndex].attempts++
    } else {
      // 添加新答案
      assessment.answers.push(studentAnswer)
    }
    
    console.log(`Answer submitted for question ${questionId}: ${isCorrect ? 'Correct' : 'Incorrect'}`)
    return true
  },
  
  /**
   * 检查答案是否正确
   */
  checkAnswer: (question: AssessmentQuestion, answer: string | string[]): boolean => {
    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
      case QuestionType.TRUE_FALSE:
      case QuestionType.SHORT_ANSWER:
        return question.correctAnswer === answer
      
      case QuestionType.FILL_BLANK:
      case QuestionType.MATCHING:
        if (Array.isArray(question.correctAnswer) && Array.isArray(answer)) {
          return JSON.stringify(question.correctAnswer.sort()) === JSON.stringify(answer.sort())
        }
        return false
      
      default:
        return false
    }
  },
  
  /**
   * 提交评估
   */
  submitAssessment: (assessment: AssessmentData): boolean => {
    if (assessment.status !== AssessmentStatus.IN_PROGRESS) {
      return false
    }
    
    assessment.status = AssessmentStatus.SUBMITTED
    assessment.endTime = new Date()
    assessment.isSubmitted = true
    
    // 计算结果
    assessment.result = AssessmentUtils.calculateResult(assessment)
    
    console.log(`Assessment submitted: ${assessment.title}`)
    return true
  },
  
  /**
   * 计算评估结果
   */
  calculateResult: (assessment: AssessmentData): AssessmentResult => {
    const totalQuestions = assessment.questions.length
    const answeredQuestions = assessment.answers.length
    const correctAnswers = assessment.answers.filter(a => a.isCorrect).length
    const totalPoints = assessment.questions.reduce((sum, q) => sum + q.points, 0)
    const earnedPoints = assessment.answers.reduce((sum, a) => sum + a.pointsEarned, 0)
    const percentage = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0
    const passed = percentage >= assessment.config.passingScore
    
    let grade = 'F'
    if (percentage >= 90) grade = 'A'
    else if (percentage >= 80) grade = 'B'
    else if (percentage >= 70) grade = 'C'
    else if (percentage >= 60) grade = 'D'
    
    const timeSpent = assessment.startTime && assessment.endTime 
      ? Math.round((assessment.endTime.getTime() - assessment.startTime.getTime()) / 1000)
      : 0
    
    return {
      totalQuestions,
      answeredQuestions,
      correctAnswers,
      totalPoints,
      earnedPoints,
      percentage,
      grade,
      timeSpent,
      startTime: assessment.startTime!,
      endTime: assessment.endTime!,
      passed,
      feedback: passed ? '恭喜通过评估！' : '需要继续努力，建议复习相关内容。'
    }
  },
  
  /**
   * 更新剩余时间
   */
  updateRemainingTime: (assessment: AssessmentData, deltaSeconds: number): void => {
    if (assessment.status === AssessmentStatus.IN_PROGRESS) {
      assessment.remainingTime = Math.max(0, assessment.remainingTime - deltaSeconds)
      
      // 时间到自动提交
      if (assessment.remainingTime <= 0 && assessment.config.autoSubmit) {
        AssessmentUtils.submitAssessment(assessment)
      }
    }
  },
  
  /**
   * 获取当前问题
   */
  getCurrentQuestion: (assessment: AssessmentData): AssessmentQuestion | null => {
    if (assessment.currentQuestionIndex >= 0 && assessment.currentQuestionIndex < assessment.questions.length) {
      return assessment.questions[assessment.currentQuestionIndex]
    }
    return null
  },
  
  /**
   * 下一题
   */
  nextQuestion: (assessment: AssessmentData): boolean => {
    if (assessment.currentQuestionIndex < assessment.questions.length - 1) {
      assessment.currentQuestionIndex++
      return true
    }
    return false
  },
  
  /**
   * 上一题
   */
  previousQuestion: (assessment: AssessmentData): boolean => {
    if (assessment.currentQuestionIndex > 0) {
      assessment.currentQuestionIndex--
      return true
    }
    return false
  }
}
