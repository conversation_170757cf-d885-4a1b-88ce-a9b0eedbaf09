{"name": "@dl-engine/engine-state", "version": "1.0.0", "description": "DL-Engine 状态管理系统 - 响应式状态、动作系统、网络同步", "main": "index.ts", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "test": "vitest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"@hookstate/core": "4.0.1", "@hookstate/identifiable": "^4.0.0", "react": "18.2.0", "react-reconciler": "0.29.0", "ts-matches": "5.3.0", "ts-toolbelt": "^9.6.0", "uuid": "9.0.0", "@dl-engine/shared-common": "workspace:*"}, "devDependencies": {"@types/node": "^22.0.0", "@types/uuid": "^10.0.0", "typescript": "5.6.3", "vite": "5.4.8", "vitest": "^2.0.0", "eslint": "^9.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "engines": {"node": ">=22.0.0"}}