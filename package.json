{"name": "dl-engine", "description": "Digital Learning Engine (DL-Engine) - 面向教育场景的3D/VR/AR应用开发平台", "version": "1.0.0", "homepage": "dl-engine.org", "private": true, "bin": {"dl-engine": "npx/cli.js"}, "workspaces": ["packages/*", "packages/projects/projects/**", "dl-engine/*", "dl-engine/engine/*", "dl-engine/editor/*", "dl-engine/server/*", "dl-engine/client/*", "dl-engine/shared/*"], "keywords": ["three", "three.js", "ecs", "webgl", "education", "learning", "digital learning", "game engine", "webrtc", "productivity", "xr", "vr", "ar", "教育", "学习平台"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "entrypoint": {}, "contributors": [], "bugs": {}, "directories": {"lib": "packages/server-core/src/user", "config": "config/"}, "engines": {"node": ">= 22.11.0"}, "npmClient": "npm", "scripts": {"build-client": "cd packages/client && npm run build", "check": "npm run lint && npm run check-errors && npm run check-eslint && npm run test && npm run build-client", "check-errors": "lerna run --scope '@ir-engine/*' check-errors && lerna run --ignore '@ir-engine/*' check-errors", "check-eslint": "eslint --quiet .", "checkout-dev": "lerna exec 'git checkout dev' --parallel --no-bail", "clean-node-modules": "npx rimraf node_modules && npx rimraf package-lock.json && npx lerna exec npx rimraf node_modules && npx lerna exec npx rimraf package-lock.json", "clone-project": "ts-node --swc scripts/clone-project.ts", "create-root-package-json": "ts-node --swc scripts/create-root-package-json", "create-project": "ts-node --swc scripts/create-project", "depcheck": "lerna exec --no-bail --stream -- depcheck", "dev": "P2P_INSTANCE_ENABLED=\"$(node scripts/get_env.js P2P_INSTANCE_ENABLED true)\"; npm run dev-docker && if test $P2P_INSTANCE_ENABLED = \"true\" ; then concurrently -n server,client -c '#FF9800,#7E2D40' 'cd packages/server && npm run dev-api-server' npm:dev-client ; else concurrently -n agones,server,client -c '#CEB793,#FF9800,#7E2D40' npm:dev-agones-silent npm:dev-server npm:dev-client ; fi", "dev-noclient": "npm run dev-docker && concurrently -n agones,server,taskserver npm:dev-agones-silent npm:dev-server npm:dev-taskserver", "dev-agones": "cd scripts && ./start-agones.sh", "dev-agones-silent": "npm run dev-agones &> /dev/null", "dev-client": "cd packages/client && npm run dev", "dev-taskserver": "cd packages/taskserver && npm run dev", "dev-docker": "cd scripts && ./start-containers.sh", "dev-docker-windows": "cd scripts && docker-compose up -d && docker-compose up -d -f docker-compose-minio.yml", "dev-tabs": "npm run dev-docker && cd scripts && ./dev-tabs.sh", "fetch-projects": "lerna exec 'git fetch -p && git rebase' --parallel --no-bail", "dev-reinit": "./scripts/checkenv.sh && npm run dev-docker && cd packages/server && npm run dev-reinit-db && cd .. && npm run precommit-hooks", "dev-server": "cd packages/server && npm run dev", "dev-windows": "npm run dev-docker-windows && concurrently -n agones,server,client npm:dev-agones-silent npm:dev-server npm:dev-client", "diff": "lerna diff", "format": "prettier --write \"packages/**/*.{ts,tsx}\" \"scripts/*.ts\"", "format-scss": "stylelint \"packages/**/*.scss\" --fix", "format-staged": "lint-staged", "gltf-texture-converter": "node scripts/gltf-texture-converter.js", "init-db-production": "cross-env APP_ENV=production FORCE_DB_REFRESH=true EXIT_ON_DB_INIT=true ts-node --swc packages/server/src/index.ts", "install-manifest": "ts-node --swc scripts/install-manifest.ts", "lint": "prettier --check \"packages/**/*.{ts,tsx}\" \"scripts/*.ts\"", "local": "P2P_INSTANCE_ENABLED=\"$(node scripts/get_env.js P2P_INSTANCE_ENABLED true)\"; npm run dev-docker && if test $P2P_INSTANCE_ENABLED = \"true\" ; then npm run local-p2p ; else npm run local-server ; fi", "local-p2p": "cross-env VITE_LOCAL_BUILD=true LOCAL=true concurrently -n server,client \"cd packages/server && npm run start\" \"cd packages/client && npm run local\"", "local-server": "cross-env VITE_LOCAL_BUILD=true LOCAL=true concurrently -n agones,server,worldserver,mediaserver,client npm:dev-agones-silent \"cd packages/server && npm run start\" \"cd packages/instanceserver && npm run start\" \"cd packages/instanceserver && npm run start-channel\" \"cd packages/client && npm run local\"", "make-user-admin": "ts-node --swc scripts/make-user-admin.ts", "migrate": "cd packages/server-core && npm run migrate", "migrate:rollback": "cd packages/server-core && npm run migrate:rollback", "migrate:unlock": "cd packages/server-core && npm run migrate:unlock", "postinstall": "patch-package", "precommit-hooks": "./scripts/setup-shared-hooks.sh", "prepare-database": "cross-env APP_ENV=production PREPARE_DATABASE=true EXIT_ON_DB_INIT=true ts-node --swc packages/server/src/index.ts", "publish": "lerna publish from-package --yes --registry https://registry.npmjs.org", "publish-npm": "lerna publish from-package --yes --no-verify-access --ignore-scripts --registry https://registry.npmjs.org", "publish-github": "lerna publish from-package --yes --no-verify-access --ignore-scripts --registry https://npm.pkg.github.com", "test": "lerna exec --scope '@ir-engine/*' --ignore '@ir-engine/server-core' 'npm run test' && lerna exec --scope '@ir-engine/server-core' 'npm run test' && lerna exec --ignore '@ir-engine/*' 'npm run test'", "test-e2e": "ts-node --swc scripts/run_e2e_tests.ts", "test:ci": "cpy --no-overwrite --rename=.env.local '.env.local.default' . && cross-env CI=true npm run test", "validate": "npm run lint && lerna run validate", "version-increment": "lerna version --conventional-commits --yes", "version-increment-no-tag": "lerna version --conventional-commits --yes --no-git-tag-version", "create-build-status": "ts-node --swc scripts/create-build-status.ts", "record-build-error": "ts-node --swc scripts/record-build-error.ts", "record-build-success": "ts-node --swc scripts/record-build-success.ts", "add-license-headers": "ts-node --swc scripts/add-license-headers.ts", "storybook": "cd packages/ui && npm run storybook", "build:storybook": "cd packages/ui && npm run build:storybook"}, "types": "lib/", "pre-commit": ["add-license-headers", "format-staged"], "lint-staged": {"*.{ts,tsx}": ["prettier --write"], "*.scss": ["stylelint \"**/*.scss\" --fix"]}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.26.0", "@ianvs/prettier-plugin-sort-imports": "4.1.0", "@rollup/plugin-dynamic-import-vars": "2.1.2", "@testing-library/react": "15.0.4", "@types/app-root-path": "1.2.4", "@types/cli": "0.11.21", "@types/primus": "^7.3.6", "@types/three": "0.176.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "4.3.1", "@vitest/coverage-istanbul": "2.1.1", "@vitest/ui": "2.1.1", "chromatic": "^11.28.0", "concurrently": "7.6.0", "deep-object-diff": "^1.1.9", "depcheck": "1.4.3", "eslint": "9.5.0", "globals": "15.6.0", "kill-port": "2.0.1", "lerna": "6.5.1", "lint-staged": "13.2.0", "package-json-type": "1.0.3", "pre-commit": "1.2.2", "prettier": "3.0.2", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "react-dnd": "16.0.1", "react-router-dom": "6.9.0", "rimraf": "4.4.0", "stylelint": "^15.3.0", "stylelint-config-standard-scss": "^7.0.1", "stylelint-scss": "^4.5.0", "supertest": "6.3.3", "vitest": "2.1.1"}, "dependencies": {"@aws-sdk/client-ecr": "3.726.1", "@aws-sdk/client-s3": "3.726.1", "@feathersjs/errors": "5.0.5", "@feathersjs/feathers": "5.0.5", "@feathersjs/schema": "5.0.5", "@feathersjs/typebox": "5.0.5", "@google-cloud/artifact-registry": "^3.5.0", "@google-cloud/compute": "^4.11.0", "@google-cloud/networkservices": "^0.7.0", "@google-cloud/storage": "^7.15.0", "@hookstate/core": "4.0.1", "@swc/core": "1.7.35", "app-root-path": "3.1.0", "class-variance-authority": "0.7.1", "cli": "1.0.1", "cycle-import-check": "^1.4.0", "dexie": "^4.0.11", "dotenv": "16.0.3", "history": "^5.3.0", "jsdom": "25.0.1", "jsdom-global": "3.0.2", "minio": "7.1.3", "motion": "12.14.0", "ollama": "^0.5.16", "patch-package": "^8.0.0", "ts-node": "10.9.2", "typescript": "5.6.3"}}