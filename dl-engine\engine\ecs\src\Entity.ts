/**
 * DL-Engine 实体类型定义
 * 定义实体、UUID、源ID等核心类型
 */

import { matches, Validator } from 'ts-matches'
import { OpaqueType } from '@dl-engine/shared-common'

/**
 * 实体类型 - 基于bitECS的数字实体ID
 */
export type Entity = OpaqueType<'entity'> & number

/**
 * 实体UUID - 全局唯一标识符
 */
export type EntityUUID = OpaqueType<'EntityUUID'> & string

/**
 * 源ID - 标识实体来源
 */
export type SourceID = OpaqueType<'SourceID'> & string

/**
 * 实体ID - 在特定源内的唯一标识
 */
export type EntityID = OpaqueType<'EntityID'> & string

/**
 * 实体UUID对 - 包含源ID和实体ID的组合
 */
export type EntityUUIDPair = {
  entitySourceID: SourceID
  entityID: EntityID
}

/**
 * 未定义实体常量 - 表示无效或未初始化的实体
 */
export const UndefinedEntity = 0 as Entity

/**
 * 实体类型验证器
 */
export const matchesEntity = matches.number as Validator<unknown, Entity>

/**
 * 实体UUID类型验证器
 */
export const matchesEntityUUID = matches.string as Validator<unknown, EntityUUID>

/**
 * 实体UUID对类型验证器
 */
export const matchesEntityUUIDPair = matches.object as Validator<unknown, EntityUUIDPair>

/**
 * 实体ID类型验证器
 */
export const matchesEntityID = matches.string as Validator<unknown, EntityID>

/**
 * 源ID类型验证器
 */
export const matchesEntitySourceID = matches.string as Validator<unknown, SourceID>

/**
 * 实体工具函数
 */
export const EntityUtils = {
  /**
   * 检查是否为有效实体
   */
  isValidEntity: (entity: Entity): boolean => {
    return entity !== UndefinedEntity && entity > 0
  },

  /**
   * 创建实体UUID对
   */
  createUUIDPair: (sourceID: SourceID, entityID: EntityID): EntityUUIDPair => ({
    entitySourceID: sourceID,
    entityID
  }),

  /**
   * 从UUID对生成字符串
   */
  uuidPairToString: (pair: EntityUUIDPair): string => {
    return `${pair.entitySourceID}:${pair.entityID}`
  },

  /**
   * 从字符串解析UUID对
   */
  stringToUUIDPair: (str: string): EntityUUIDPair | null => {
    const parts = str.split(':')
    if (parts.length !== 2) return null
    
    return {
      entitySourceID: parts[0] as SourceID,
      entityID: parts[1] as EntityID
    }
  },

  /**
   * 生成新的实体ID
   */
  generateEntityID: (): EntityID => {
    return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` as EntityID
  },

  /**
   * 生成新的源ID
   */
  generateSourceID: (): SourceID => {
    return `source_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` as SourceID
  }
}

/**
 * 实体常量
 */
export const EntityConstants = {
  /** 最大实体数量 */
  MAX_ENTITIES: 100000,
  
  /** 默认源ID */
  DEFAULT_SOURCE_ID: 'default' as SourceID,
  
  /** 引擎源ID */
  ENGINE_SOURCE_ID: 'engine' as SourceID,
  
  /** 用户源ID前缀 */
  USER_SOURCE_PREFIX: 'user_',
  
  /** 场景源ID前缀 */
  SCENE_SOURCE_PREFIX: 'scene_'
} as const

/**
 * 实体事件类型
 */
export enum EntityEventType {
  CREATED = 'entity:created',
  DESTROYED = 'entity:destroyed',
  COMPONENT_ADDED = 'entity:component:added',
  COMPONENT_REMOVED = 'entity:component:removed',
  COMPONENT_CHANGED = 'entity:component:changed'
}

/**
 * 实体事件数据
 */
export interface EntityEvent {
  type: EntityEventType
  entity: Entity
  component?: string
  data?: any
  timestamp: number
}

/**
 * 实体统计信息
 */
export interface EntityStats {
  totalEntities: number
  activeEntities: number
  removedEntities: number
  entitiesPerSource: Record<string, number>
  componentsPerEntity: Record<Entity, number>
}

/**
 * 实体查询选项
 */
export interface EntityQueryOptions {
  includeRemoved?: boolean
  sourceFilter?: SourceID[]
  componentFilter?: string[]
  limit?: number
  offset?: number
}
