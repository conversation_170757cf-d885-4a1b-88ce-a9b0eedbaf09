import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    target: 'es2022',
    lib: {
      entry: resolve(__dirname, 'index.ts'),
      name: 'DLEngineAI',
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        'axios',
        '@dl-engine/engine-ecs',
        '@dl-engine/engine-state',
        '@dl-engine/shared-common'
      ]
    },
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@dl-engine/engine-ecs': resolve(__dirname, '../ecs/src'),
      '@dl-engine/engine-state': resolve(__dirname, '../state/src'),
      '@dl-engine/shared-common': resolve(__dirname, '../../shared/common/src')
    }
  },
  test: {
    environment: 'node',
    globals: true
  }
})
