/**
 * DL-Engine 3D Web图层
 * 在3D空间中渲染HTML/React内容
 */

import {
  Mesh,
  PlaneGeometry,
  MeshBasicMaterial,
  Texture,
  CanvasTexture,
  Vector3,
  Quaternion,
  Matrix4,
  DoubleSide
} from 'three'

import { Entity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState } from '@dl-engine/engine-state'

/**
 * WebLayer配置
 */
export interface WebLayerConfig {
  /** 图层宽度（米） */
  width: number
  
  /** 图层高度（米） */
  height: number
  
  /** 像素密度 */
  pixelDensity: number
  
  /** 是否启用交互 */
  interactive: boolean
  
  /** 是否自动更新 */
  autoUpdate: boolean
  
  /** 更新频率（FPS） */
  updateRate: number
  
  /** 背景透明 */
  transparent: boolean
}

/**
 * 默认WebLayer配置
 */
const defaultWebLayerConfig: WebLayerConfig = {
  width: 1,
  height: 0.6,
  pixelDensity: 1,
  interactive: true,
  autoUpdate: true,
  updateRate: 30,
  transparent: true
}

/**
 * WebLayer状态
 */
export interface WebLayerState {
  /** 图层映射 */
  layers: Map<Entity, WebLayer3D>
  
  /** 全局配置 */
  globalConfig: Partial<WebLayerConfig>
}

/**
 * WebLayer状态定义
 */
export const WebLayerState = defineState({
  name: 'DLEngine.WebLayer',
  initial: (): WebLayerState => ({
    layers: new Map(),
    globalConfig: {}
  }),
  
  receptors: {
    /**
     * 添加图层
     */
    addLayer: (state, entity: Entity, layer: WebLayer3D) => {
      state.layers.get(NO_PROXY).set(entity, layer)
    },
    
    /**
     * 移除图层
     */
    removeLayer: (state, entity: Entity) => {
      state.layers.get(NO_PROXY).delete(entity)
    },
    
    /**
     * 更新全局配置
     */
    updateGlobalConfig: (state, config: Partial<WebLayerConfig>) => {
      state.globalConfig.merge(config)
    }
  }
})

// 导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * 3D Web图层类
 */
export class WebLayer3D extends Mesh {
  /** 关联的实体 */
  entity: Entity
  
  /** 图层配置 */
  config: WebLayerConfig
  
  /** HTML元素 */
  element: HTMLElement | null = null
  
  /** 画布元素 */
  canvas: HTMLCanvasElement
  
  /** 画布上下文 */
  context: CanvasRenderingContext2D
  
  /** 纹理 */
  texture: CanvasTexture
  
  /** 材质 */
  material: MeshBasicMaterial
  
  /** 几何体 */
  geometry: PlaneGeometry
  
  /** 是否需要更新 */
  needsUpdate: boolean = true
  
  /** 最后更新时间 */
  lastUpdateTime: number = 0
  
  /** 更新间隔 */
  updateInterval: number
  
  constructor(entity: Entity, config: Partial<WebLayerConfig> = {}) {
    // 合并配置
    const finalConfig = { ...defaultWebLayerConfig, ...config }
    
    // 创建几何体
    const geometry = new PlaneGeometry(finalConfig.width, finalConfig.height)
    
    // 创建画布
    const canvas = document.createElement('canvas')
    const pixelWidth = Math.floor(finalConfig.width * 1000 * finalConfig.pixelDensity)
    const pixelHeight = Math.floor(finalConfig.height * 1000 * finalConfig.pixelDensity)
    
    canvas.width = pixelWidth
    canvas.height = pixelHeight
    canvas.style.width = `${pixelWidth}px`
    canvas.style.height = `${pixelHeight}px`
    
    // 获取上下文
    const context = canvas.getContext('2d')!
    context.imageSmoothingEnabled = true
    context.imageSmoothingQuality = 'high'
    
    // 创建纹理
    const texture = new CanvasTexture(canvas)
    texture.flipY = false
    
    // 创建材质
    const material = new MeshBasicMaterial({
      map: texture,
      transparent: finalConfig.transparent,
      side: DoubleSide,
      alphaTest: finalConfig.transparent ? 0.01 : 0
    })
    
    // 调用父类构造函数
    super(geometry, material)
    
    // 设置属性
    this.entity = entity
    this.config = finalConfig
    this.canvas = canvas
    this.context = context
    this.texture = texture
    this.material = material
    this.geometry = geometry
    this.updateInterval = 1000 / finalConfig.updateRate
    
    // 设置名称
    this.name = `WebLayer3D_${entity}`
    
    // 添加到状态
    getMutableState(WebLayerState).addLayer(entity, this)
    
    console.log(`WebLayer3D created for entity ${entity}`)
  }
  
  /**
   * 设置HTML元素
   */
  setElement(element: HTMLElement): void {
    this.element = element
    this.needsUpdate = true
  }
  
  /**
   * 更新图层内容
   */
  update(force: boolean = false): void {
    const now = performance.now()
    
    // 检查是否需要更新
    if (!force && !this.needsUpdate && !this.config.autoUpdate) {
      return
    }
    
    // 检查更新频率
    if (!force && (now - this.lastUpdateTime) < this.updateInterval) {
      return
    }
    
    if (this.element) {
      this.renderElementToCanvas()
    } else {
      this.renderDefaultContent()
    }
    
    // 更新纹理
    this.texture.needsUpdate = true
    this.needsUpdate = false
    this.lastUpdateTime = now
  }
  
  /**
   * 渲染HTML元素到画布
   */
  private renderElementToCanvas(): void {
    if (!this.element) return
    
    const { canvas, context } = this
    
    // 清空画布
    context.clearRect(0, 0, canvas.width, canvas.height)
    
    // 设置背景
    if (!this.config.transparent) {
      context.fillStyle = '#ffffff'
      context.fillRect(0, 0, canvas.width, canvas.height)
    }
    
    // 使用html2canvas或类似库渲染HTML
    // 这里简化实现，实际项目中需要集成html2canvas
    this.renderElementSimple()
  }
  
  /**
   * 简化的元素渲染
   */
  private renderElementSimple(): void {
    const { context, canvas } = this
    
    // 获取元素样式
    const computedStyle = window.getComputedStyle(this.element!)
    const backgroundColor = computedStyle.backgroundColor
    const color = computedStyle.color
    const fontSize = computedStyle.fontSize
    const fontFamily = computedStyle.fontFamily
    
    // 设置字体
    context.font = `${fontSize} ${fontFamily}`
    context.fillStyle = color
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    
    // 绘制背景
    if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
      context.fillStyle = backgroundColor
      context.fillRect(0, 0, canvas.width, canvas.height)
      context.fillStyle = color
    }
    
    // 绘制文本
    const text = this.element!.textContent || ''
    const x = canvas.width / 2
    const y = canvas.height / 2
    
    // 处理多行文本
    const lines = this.wrapText(text, canvas.width - 40)
    const lineHeight = parseInt(fontSize) * 1.2
    const startY = y - (lines.length - 1) * lineHeight / 2
    
    lines.forEach((line, index) => {
      context.fillText(line, x, startY + index * lineHeight)
    })
  }
  
  /**
   * 文本换行
   */
  private wrapText(text: string, maxWidth: number): string[] {
    const words = text.split(' ')
    const lines: string[] = []
    let currentLine = ''
    
    for (const word of words) {
      const testLine = currentLine + (currentLine ? ' ' : '') + word
      const metrics = this.context.measureText(testLine)
      
      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine)
        currentLine = word
      } else {
        currentLine = testLine
      }
    }
    
    if (currentLine) {
      lines.push(currentLine)
    }
    
    return lines
  }
  
  /**
   * 渲染默认内容
   */
  private renderDefaultContent(): void {
    const { context, canvas } = this
    
    // 清空画布
    context.clearRect(0, 0, canvas.width, canvas.height)
    
    // 绘制默认背景
    context.fillStyle = '#f0f0f0'
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    // 绘制边框
    context.strokeStyle = '#cccccc'
    context.lineWidth = 2
    context.strokeRect(1, 1, canvas.width - 2, canvas.height - 2)
    
    // 绘制默认文本
    context.fillStyle = '#666666'
    context.font = '24px Arial'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText('DL-Engine WebLayer3D', canvas.width / 2, canvas.height / 2)
  }
  
  /**
   * 设置位置
   */
  setPosition(x: number, y: number, z: number): void {
    this.position.set(x, y, z)
  }
  
  /**
   * 设置旋转
   */
  setRotation(x: number, y: number, z: number): void {
    this.rotation.set(x, y, z)
  }
  
  /**
   * 设置缩放
   */
  setScale(x: number, y: number = x, z: number = 1): void {
    this.scale.set(x, y, z)
  }
  
  /**
   * 更新配置
   */
  updateConfig(config: Partial<WebLayerConfig>): void {
    this.config = { ...this.config, ...config }
    
    // 更新几何体尺寸
    if (config.width !== undefined || config.height !== undefined) {
      this.geometry.dispose()
      this.geometry = new PlaneGeometry(this.config.width, this.config.height)
      this.geometry = this.geometry
    }
    
    // 更新画布尺寸
    if (config.width !== undefined || config.height !== undefined || config.pixelDensity !== undefined) {
      const pixelWidth = Math.floor(this.config.width * 1000 * this.config.pixelDensity)
      const pixelHeight = Math.floor(this.config.height * 1000 * this.config.pixelDensity)
      
      this.canvas.width = pixelWidth
      this.canvas.height = pixelHeight
      this.canvas.style.width = `${pixelWidth}px`
      this.canvas.style.height = `${pixelHeight}px`
    }
    
    // 更新更新间隔
    if (config.updateRate !== undefined) {
      this.updateInterval = 1000 / this.config.updateRate
    }
    
    this.needsUpdate = true
  }
  
  /**
   * 销毁图层
   */
  dispose(): void {
    // 清理几何体
    this.geometry.dispose()

    // 清理材质
    this.material.dispose()

    // 清理纹理
    this.texture.dispose()

    // 从状态中移除
    getMutableState(WebLayerState).removeLayer(this.entity)

    console.log(`WebLayer3D disposed for entity ${this.entity}`)
  }
}

/**
 * 3D UI面板管理器
 */
export class UI3DPanelManager {
  private static instance: UI3DPanelManager | null = null
  private panels: Map<string, WebLayer3D> = new Map()

  /**
   * 获取单例实例
   */
  static getInstance(): UI3DPanelManager {
    if (!UI3DPanelManager.instance) {
      UI3DPanelManager.instance = new UI3DPanelManager()
    }
    return UI3DPanelManager.instance
  }

  /**
   * 创建教育UI面板
   */
  createEducationPanel(
    entity: Entity,
    content: string,
    position: Vector3,
    config: Partial<WebLayerConfig> = {}
  ): WebLayer3D {
    const panelConfig: WebLayerConfig = {
      width: 2.0,
      height: 1.5,
      pixelDensity: 2,
      interactive: true,
      autoUpdate: true,
      updateRate: 30,
      transparent: true,
      ...config
    }

    const panel = new WebLayer3D(entity, panelConfig)

    // 设置教育面板的HTML内容
    const educationHTML = this.generateEducationHTML(content)
    panel.setContent(educationHTML)

    // 设置位置
    panel.position.copy(position)

    // 面向用户
    panel.lookAt(new Vector3(0, position.y, 0))

    this.panels.set(`education_${entity}`, panel)

    console.log(`Education panel created for entity ${entity}`)
    return panel
  }

  /**
   * 创建交互式菜单
   */
  createInteractiveMenu(
    entity: Entity,
    menuItems: Array<{ label: string; action: string; icon?: string }>,
    position: Vector3
  ): WebLayer3D {
    const menuConfig: WebLayerConfig = {
      width: 1.5,
      height: 2.0,
      pixelDensity: 2,
      interactive: true,
      autoUpdate: true,
      updateRate: 60,
      transparent: true
    }

    const menu = new WebLayer3D(entity, menuConfig)

    // 生成菜单HTML
    const menuHTML = this.generateMenuHTML(menuItems)
    menu.setContent(menuHTML)

    // 设置位置和旋转
    menu.position.copy(position)
    menu.lookAt(new Vector3(0, position.y, 0))

    this.panels.set(`menu_${entity}`, menu)

    console.log(`Interactive menu created for entity ${entity}`)
    return menu
  }

  /**
   * 创建信息提示面板
   */
  createInfoTooltip(
    entity: Entity,
    title: string,
    description: string,
    position: Vector3
  ): WebLayer3D {
    const tooltipConfig: WebLayerConfig = {
      width: 1.0,
      height: 0.6,
      pixelDensity: 2,
      interactive: false,
      autoUpdate: false,
      updateRate: 10,
      transparent: true
    }

    const tooltip = new WebLayer3D(entity, tooltipConfig)

    // 生成提示HTML
    const tooltipHTML = this.generateTooltipHTML(title, description)
    tooltip.setContent(tooltipHTML)

    // 设置位置，稍微偏上
    tooltip.position.copy(position)
    tooltip.position.y += 0.5
    tooltip.lookAt(new Vector3(0, position.y, 0))

    this.panels.set(`tooltip_${entity}`, tooltip)

    console.log(`Info tooltip created for entity ${entity}`)
    return tooltip
  }

  /**
   * 生成教育面板HTML
   */
  private generateEducationHTML(content: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
          }
          .header {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
          }
          .content {
            font-size: 16px;
            line-height: 1.6;
            text-align: left;
          }
          .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 15px 0;
            overflow: hidden;
          }
          .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 60%;
            border-radius: 4px;
          }
        </style>
      </head>
      <body>
        <div class="header">📚 学习内容</div>
        <div class="content">${content}</div>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </body>
      </html>
    `
  }

  /**
   * 生成菜单HTML
   */
  private generateMenuHTML(menuItems: Array<{ label: string; action: string; icon?: string }>): string {
    const itemsHTML = menuItems.map((item, index) => `
      <div class="menu-item" data-action="${item.action}">
        ${item.icon ? `<span class="icon">${item.icon}</span>` : ''}
        <span class="label">${item.label}</span>
      </div>
    `).join('')

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body {
            margin: 0;
            padding: 15px;
            font-family: 'Arial', sans-serif;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 10px;
            backdrop-filter: blur(10px);
          }
          .menu-item {
            padding: 12px 15px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
          }
          .menu-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
          }
          .icon {
            margin-right: 10px;
            font-size: 18px;
          }
          .label {
            font-size: 16px;
          }
        </style>
      </head>
      <body>
        ${itemsHTML}
      </body>
      </html>
    `
  }

  /**
   * 生成提示HTML
   */
  private generateTooltipHTML(title: string, description: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body {
            margin: 0;
            padding: 15px;
            font-family: 'Arial', sans-serif;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border-radius: 8px;
            border: 2px solid #4CAF50;
          }
          .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #4CAF50;
          }
          .description {
            font-size: 14px;
            line-height: 1.4;
          }
        </style>
      </head>
      <body>
        <div class="title">${title}</div>
        <div class="description">${description}</div>
      </body>
      </html>
    `
  }

  /**
   * 获取面板
   */
  getPanel(id: string): WebLayer3D | null {
    return this.panels.get(id) || null
  }

  /**
   * 移除面板
   */
  removePanel(id: string): boolean {
    const panel = this.panels.get(id)
    if (panel) {
      panel.dispose()
      this.panels.delete(id)
      return true
    }
    return false
  }

  /**
   * 获取所有面板
   */
  getAllPanels(): WebLayer3D[] {
    return Array.from(this.panels.values())
  }

  /**
   * 清理所有面板
   */
  dispose(): void {
    for (const panel of this.panels.values()) {
      panel.dispose()
    }
    this.panels.clear()
  }
}
