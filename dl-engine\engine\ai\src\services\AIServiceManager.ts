/**
 * DL-Engine AI服务管理器
 * 统一管理各种AI服务和功能
 */

import { OllamaClient, EducationAIAssistant } from '../ollama/OllamaClient'
import { LearningAnalytics } from '../analytics/LearningAnalytics'
import { TextProcessor } from '../nlp/TextProcessor'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * AI服务类型
 */
export enum AIServiceType {
  OLLAMA = 'ollama',
  EDUCATION_ASSISTANT = 'education_assistant',
  LEARNING_ANALYTICS = 'learning_analytics',
  TEXT_PROCESSOR = 'text_processor'
}

/**
 * AI服务状态
 */
export interface AIServiceStatus {
  type: AIServiceType
  name: string
  enabled: boolean
  connected: boolean
  lastError?: string
  performance: {
    averageResponseTime: number
    successRate: number
    totalRequests: number
  }
}

/**
 * AI服务配置
 */
export interface AIServiceConfig {
  /** 启用的服务列表 */
  enabledServices: AIServiceType[]
  
  /** 默认语言 */
  defaultLanguage: string
  
  /** 默认AI模型 */
  defaultModel: string
  
  /** 响应超时时间（毫秒） */
  responseTimeout: number
  
  /** 是否启用缓存 */
  enableCache: boolean
  
  /** 缓存过期时间（分钟） */
  cacheExpiration: number
  
  /** 最大并发请求数 */
  maxConcurrentRequests: number
}

/**
 * AI服务管理器状态
 */
export const AIServiceManagerState = defineState({
  name: 'AIServiceManager',
  initial: {
    config: {
      enabledServices: [
        AIServiceType.OLLAMA,
        AIServiceType.EDUCATION_ASSISTANT,
        AIServiceType.LEARNING_ANALYTICS,
        AIServiceType.TEXT_PROCESSOR
      ] as AIServiceType[],
      defaultLanguage: 'zh-CN',
      defaultModel: 'llama3.2:3b',
      responseTimeout: 30000,
      enableCache: true,
      cacheExpiration: 60,
      maxConcurrentRequests: 5
    } as AIServiceConfig,
    
    services: new Map<AIServiceType, AIServiceStatus>(),
    
    initialized: false,
    
    activeRequests: 0,
    
    cache: new Map<string, { data: any; timestamp: number }>()
  },
  
  actions: {
    /**
     * 更新配置
     */
    updateConfig: (state, config: Partial<AIServiceConfig>) => {
      state.config.merge(config)
    },
    
    /**
     * 设置服务状态
     */
    setServiceStatus: (state, type: AIServiceType, status: Partial<AIServiceStatus>) => {
      const currentStatus = state.services.get(type) || {
        type,
        name: type,
        enabled: false,
        connected: false,
        performance: {
          averageResponseTime: 0,
          successRate: 0,
          totalRequests: 0
        }
      }
      
      state.services.set(type, { ...currentStatus, ...status })
    },
    
    /**
     * 设置初始化状态
     */
    setInitialized: (state, initialized: boolean) => {
      state.initialized.set(initialized)
    },
    
    /**
     * 增加活跃请求数
     */
    incrementActiveRequests: (state) => {
      state.activeRequests.set(state.activeRequests.value + 1)
    },
    
    /**
     * 减少活跃请求数
     */
    decrementActiveRequests: (state) => {
      state.activeRequests.set(Math.max(0, state.activeRequests.value - 1))
    },
    
    /**
     * 设置缓存
     */
    setCache: (state, key: string, data: any) => {
      state.cache.get(NO_PROXY).set(key, {
        data,
        timestamp: Date.now()
      })
    },
    
    /**
     * 清理过期缓存
     */
    cleanExpiredCache: (state) => {
      const now = Date.now()
      const expiration = state.config.cacheExpiration.value * 60 * 1000
      const cache = state.cache.get(NO_PROXY)
      
      for (const [key, value] of cache) {
        if (now - value.timestamp > expiration) {
          cache.delete(key)
        }
      }
    }
  }
})

// 导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * AI服务管理器
 */
export class AIServiceManager {
  private static instance: AIServiceManager | null = null
  private services: Map<AIServiceType, any> = new Map()
  
  /**
   * 获取单例实例
   */
  static getInstance(): AIServiceManager {
    if (!AIServiceManager.instance) {
      AIServiceManager.instance = new AIServiceManager()
    }
    return AIServiceManager.instance
  }
  
  /**
   * 初始化AI服务
   */
  async initialize(): Promise<void> {
    console.log('🤖 Initializing AI Service Manager...')
    
    try {
      const config = getState(AIServiceManagerState).config
      
      // 初始化各种AI服务
      await this.initializeServices(config.enabledServices)
      
      // 设置定期清理缓存
      if (config.enableCache) {
        setInterval(() => {
          getMutableState(AIServiceManagerState).cleanExpiredCache()
        }, 5 * 60 * 1000) // 每5分钟清理一次
      }
      
      getMutableState(AIServiceManagerState).setInitialized(true)
      console.log('✅ AI Service Manager initialized successfully')
      
    } catch (error) {
      console.error('❌ Failed to initialize AI Service Manager:', error)
      throw error
    }
  }
  
  /**
   * 初始化服务
   */
  private async initializeServices(enabledServices: AIServiceType[]): Promise<void> {
    for (const serviceType of enabledServices) {
      try {
        await this.initializeService(serviceType)
      } catch (error) {
        console.error(`Failed to initialize service ${serviceType}:`, error)
        
        // 更新服务状态
        getMutableState(AIServiceManagerState).setServiceStatus(serviceType, {
          enabled: false,
          connected: false,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  }
  
  /**
   * 初始化单个服务
   */
  private async initializeService(serviceType: AIServiceType): Promise<void> {
    let service: any
    
    switch (serviceType) {
      case AIServiceType.OLLAMA:
        service = OllamaClient.getInstance()
        await this.testOllamaConnection(service)
        break
        
      case AIServiceType.EDUCATION_ASSISTANT:
        service = EducationAIAssistant.getInstance()
        break
        
      case AIServiceType.LEARNING_ANALYTICS:
        service = LearningAnalytics.getInstance()
        break
        
      case AIServiceType.TEXT_PROCESSOR:
        service = TextProcessor.getInstance()
        break
        
      default:
        throw new Error(`Unknown service type: ${serviceType}`)
    }
    
    this.services.set(serviceType, service)
    
    // 更新服务状态
    getMutableState(AIServiceManagerState).setServiceStatus(serviceType, {
      enabled: true,
      connected: true,
      name: this.getServiceName(serviceType)
    })
    
    console.log(`✅ Service ${serviceType} initialized`)
  }
  
  /**
   * 测试Ollama连接
   */
  private async testOllamaConnection(ollamaClient: OllamaClient): Promise<void> {
    try {
      await ollamaClient.listModels()
    } catch (error) {
      throw new Error('Failed to connect to Ollama service')
    }
  }
  
  /**
   * 获取服务
   */
  getService<T>(serviceType: AIServiceType): T | null {
    return this.services.get(serviceType) || null
  }
  
  /**
   * 获取教育AI助手
   */
  getEducationAssistant(): EducationAIAssistant | null {
    return this.getService<EducationAIAssistant>(AIServiceType.EDUCATION_ASSISTANT)
  }
  
  /**
   * 获取Ollama客户端
   */
  getOllamaClient(): OllamaClient | null {
    return this.getService<OllamaClient>(AIServiceType.OLLAMA)
  }
  
  /**
   * 获取学习分析服务
   */
  getLearningAnalytics(): LearningAnalytics | null {
    return this.getService<LearningAnalytics>(AIServiceType.LEARNING_ANALYTICS)
  }
  
  /**
   * 获取文本处理器
   */
  getTextProcessor(): TextProcessor | null {
    return this.getService<TextProcessor>(AIServiceType.TEXT_PROCESSOR)
  }
  
  /**
   * 检查服务是否可用
   */
  isServiceAvailable(serviceType: AIServiceType): boolean {
    const state = getState(AIServiceManagerState)
    const serviceStatus = state.services.get(serviceType)
    return serviceStatus?.enabled && serviceStatus?.connected || false
  }
  
  /**
   * 获取所有服务状态
   */
  getAllServiceStatus(): AIServiceStatus[] {
    const state = getState(AIServiceManagerState)
    return Array.from(state.services.values())
  }
  
  /**
   * 更新配置
   */
  updateConfig(config: Partial<AIServiceConfig>): void {
    getMutableState(AIServiceManagerState).updateConfig(config)
  }
  
  /**
   * 获取服务名称
   */
  private getServiceName(serviceType: AIServiceType): string {
    const nameMap = {
      [AIServiceType.OLLAMA]: 'Ollama本地AI',
      [AIServiceType.EDUCATION_ASSISTANT]: '教育AI助手',
      [AIServiceType.LEARNING_ANALYTICS]: '学习分析',
      [AIServiceType.TEXT_PROCESSOR]: '文本处理器'
    }
    return nameMap[serviceType] || serviceType
  }
  
  /**
   * 获取缓存
   */
  getCache(key: string): any | null {
    const state = getState(AIServiceManagerState)
    const cached = state.cache.get(key)
    
    if (!cached) return null
    
    const now = Date.now()
    const expiration = state.config.cacheExpiration * 60 * 1000
    
    if (now - cached.timestamp > expiration) {
      // 缓存已过期
      return null
    }
    
    return cached.data
  }
  
  /**
   * 设置缓存
   */
  setCache(key: string, data: any): void {
    const state = getState(AIServiceManagerState)
    if (state.config.enableCache) {
      getMutableState(AIServiceManagerState).setCache(key, data)
    }
  }
}
