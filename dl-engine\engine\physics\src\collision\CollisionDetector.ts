/**
 * DL-Engine 碰撞检测器
 * 高性能碰撞检测和查询系统
 */

import { 
  Ray, 
  RayIntersection,
  Collider,
  QueryFilterFlags,
  InteractionGroups
} from '@dimforge/rapier3d'

import { Entity } from '@dl-engine/engine-ecs'
import { 
  DLPhysicsWorld,
  PhysicsWorldManager 
} from '../PhysicsWorld'
import { 
  RaycastQuery, 
  RaycastHit, 
  ShapecastQuery,
  OverlapQuery,
  SpatialQueryType,
  SpatialQueryResult,
  PhysicsVector3,
  PhysicsQuaternion,
  ColliderShape
} from '../types/PhysicsTypes'

/**
 * 碰撞检测器类
 */
export class CollisionDetector {
  private worldManager: PhysicsWorldManager
  
  constructor() {
    this.worldManager = PhysicsWorldManager.getInstance()
  }
  
  /**
   * 射线投射 - 单个结果
   */
  raycast(query: RaycastQuery, worldId?: Entity): RaycastHit | null {
    const world = this.worldManager.getWorld(worldId)
    if (!world) {
      console.warn('Physics world not found for raycast')
      return null
    }
    
    const ray = new Ray(
      { x: query.origin.x, y: query.origin.y, z: query.origin.z },
      { x: query.direction.x, y: query.direction.y, z: query.direction.z }
    )
    
    const maxToi = query.maxDistance
    const solid = true
    const groups = query.collisionGroups
    const flags = query.flags || QueryFilterFlags.EXCLUDE_SENSORS
    
    // 获取排除的碰撞体
    const excludeCollider = query.excludeCollider ? 
      world.colliders.get(query.excludeCollider) : undefined
    const excludeRigidBody = query.excludeRigidBody ? 
      world.rigidBodies.get(query.excludeRigidBody) : undefined
    
    // 执行射线投射
    const hit = world.castRayAndGetNormal(
      ray,
      maxToi,
      solid,
      flags,
      groups,
      excludeCollider,
      excludeRigidBody
    )
    
    if (!hit) {
      return null
    }
    
    // 转换结果
    return this.convertRaycastHit(hit, ray)
  }
  
  /**
   * 射线投射 - 多个结果
   */
  raycastAll(query: RaycastQuery, worldId?: Entity): RaycastHit[] {
    const world = this.worldManager.getWorld(worldId)
    if (!world) {
      console.warn('Physics world not found for raycast')
      return []
    }
    
    const ray = new Ray(
      { x: query.origin.x, y: query.origin.y, z: query.origin.z },
      { x: query.direction.x, y: query.direction.y, z: query.direction.z }
    )
    
    const maxToi = query.maxDistance
    const solid = true
    const groups = query.collisionGroups
    const flags = query.flags || QueryFilterFlags.EXCLUDE_SENSORS
    
    const hits: RaycastHit[] = []
    
    // 执行射线投射，收集所有交点
    world.intersectionsWithRay(
      ray,
      maxToi,
      solid,
      (intersection: RayIntersection) => {
        const hit = this.convertRaycastHit(intersection, ray)
        if (hit) {
          hits.push(hit)
        }
        return true // 继续收集
      },
      flags,
      groups
    )
    
    // 按距离排序
    hits.sort((a, b) => a.distance - b.distance)
    
    return hits
  }
  
  /**
   * 形状投射
   */
  shapecast(query: ShapecastQuery, worldId?: Entity): RaycastHit | null {
    const world = this.worldManager.getWorld(worldId)
    if (!world) {
      console.warn('Physics world not found for shapecast')
      return null
    }
    
    // 创建形状描述符
    const shapeDesc = this.createShapeDescriptor(query.shape, query.shapeParams)
    if (!shapeDesc) {
      return null
    }
    
    const shapePos = { 
      x: query.position.x, 
      y: query.position.y, 
      z: query.position.z 
    }
    const shapeRot = { 
      x: query.rotation.x, 
      y: query.rotation.y, 
      z: query.rotation.z, 
      w: query.rotation.w 
    }
    const shapeVel = { 
      x: query.direction.x, 
      y: query.direction.y, 
      z: query.direction.z 
    }
    
    // 执行形状投射
    const hit = world.castShape(
      shapePos,
      shapeRot,
      shapeVel,
      shapeDesc,
      query.maxDistance,
      true, // stop_at_penetration
      query.collisionGroups
    )
    
    if (!hit) {
      return null
    }
    
    // 转换结果
    return {
      distance: hit.toi,
      position: {
        x: query.position.x + query.direction.x * hit.toi,
        y: query.position.y + query.direction.y * hit.toi,
        z: query.position.z + query.direction.z * hit.toi
      },
      normal: hit.normal1,
      rigidBody: hit.collider.parent(),
      collider: hit.collider,
      entity: (hit.collider as any).entity || 0
    }
  }
  
  /**
   * 重叠查询
   */
  overlap(query: OverlapQuery, worldId?: Entity): SpatialQueryResult[] {
    const world = this.worldManager.getWorld(worldId)
    if (!world) {
      console.warn('Physics world not found for overlap query')
      return []
    }
    
    // 创建形状描述符
    const shapeDesc = this.createShapeDescriptor(query.shape, query.shapeParams)
    if (!shapeDesc) {
      return []
    }
    
    const shapePos = { 
      x: query.position.x, 
      y: query.position.y, 
      z: query.position.z 
    }
    const shapeRot = { 
      x: query.rotation.x, 
      y: query.rotation.y, 
      z: query.rotation.z, 
      w: query.rotation.w 
    }
    
    const results: SpatialQueryResult[] = []
    const maxResults = query.maxResults || 100
    
    // 执行重叠查询
    world.intersectionsWithShape(
      shapePos,
      shapeRot,
      shapeDesc,
      (collider: Collider) => {
        if (results.length >= maxResults) {
          return false // 停止收集
        }
        
        const entity = (collider as any).entity || 0
        const result: SpatialQueryResult = {
          entity,
          collider,
          distance: 0, // 重叠查询距离为0
          position: query.position
        }
        
        results.push(result)
        return true // 继续收集
      },
      query.collisionGroups
    )
    
    return results
  }
  
  /**
   * 点查询
   */
  pointQuery(
    point: PhysicsVector3, 
    worldId?: Entity,
    groups?: InteractionGroups
  ): SpatialQueryResult[] {
    const world = this.worldManager.getWorld(worldId)
    if (!world) {
      console.warn('Physics world not found for point query')
      return []
    }
    
    const results: SpatialQueryResult[] = []
    
    // 执行点查询
    world.intersectionsWithPoint(
      { x: point.x, y: point.y, z: point.z },
      (collider: Collider) => {
        const entity = (collider as any).entity || 0
        const result: SpatialQueryResult = {
          entity,
          collider,
          distance: 0,
          position: point
        }
        
        results.push(result)
        return true
      },
      groups
    )
    
    return results
  }
  
  /**
   * 转换射线投射结果
   */
  private convertRaycastHit(hit: any, ray: Ray): RaycastHit | null {
    if (!hit || !hit.collider) {
      return null
    }
    
    const entity = (hit.collider as any).entity || 0
    const position = ray.pointAt(hit.toi)
    
    return {
      distance: hit.toi,
      position: { x: position.x, y: position.y, z: position.z },
      normal: hit.normal || { x: 0, y: 1, z: 0 },
      rigidBody: hit.collider.parent(),
      collider: hit.collider,
      entity
    }
  }
  
  /**
   * 创建形状描述符
   */
  private createShapeDescriptor(shape: ColliderShape, params: any): any {
    // 这里需要根据形状类型创建对应的Rapier形状描述符
    // 由于Rapier API的复杂性，这里提供基础实现
    
    switch (shape) {
      case ColliderShape.BOX:
        const { width = 1, height = 1, depth = 1 } = params
        return { type: 'cuboid', hx: width / 2, hy: height / 2, hz: depth / 2 }
        
      case ColliderShape.SPHERE:
        const { radius = 1 } = params
        return { type: 'ball', radius }
        
      case ColliderShape.CAPSULE:
        const { height: capHeight = 2, radius: capRadius = 0.5 } = params
        return { type: 'capsule', halfHeight: capHeight / 2, radius: capRadius }
        
      default:
        console.warn(`Unsupported shape type: ${shape}`)
        return null
    }
  }
}

/**
 * 全局碰撞检测器实例
 */
export const collisionDetector = new CollisionDetector()

/**
 * 便捷的射线投射函数
 */
export function raycast(
  origin: PhysicsVector3,
  direction: PhysicsVector3,
  maxDistance: number = 1000,
  worldId?: Entity
): RaycastHit | null {
  return collisionDetector.raycast({
    origin,
    direction,
    maxDistance
  }, worldId)
}

/**
 * 便捷的射线投射所有函数
 */
export function raycastAll(
  origin: PhysicsVector3,
  direction: PhysicsVector3,
  maxDistance: number = 1000,
  worldId?: Entity
): RaycastHit[] {
  return collisionDetector.raycastAll({
    origin,
    direction,
    maxDistance
  }, worldId)
}

/**
 * 便捷的球形重叠查询
 */
export function overlapSphere(
  center: PhysicsVector3,
  radius: number,
  worldId?: Entity,
  maxResults?: number
): SpatialQueryResult[] {
  return collisionDetector.overlap({
    shape: ColliderShape.SPHERE,
    shapeParams: { radius },
    position: center,
    rotation: { x: 0, y: 0, z: 0, w: 1 },
    maxResults
  }, worldId)
}

/**
 * 便捷的盒形重叠查询
 */
export function overlapBox(
  center: PhysicsVector3,
  size: PhysicsVector3,
  worldId?: Entity,
  maxResults?: number
): SpatialQueryResult[] {
  return collisionDetector.overlap({
    shape: ColliderShape.BOX,
    shapeParams: { width: size.x, height: size.y, depth: size.z },
    position: center,
    rotation: { x: 0, y: 0, z: 0, w: 1 },
    maxResults
  }, worldId)
}
