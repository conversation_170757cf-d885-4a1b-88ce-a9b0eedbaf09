/**
 * DL-Engine 刚体组件
 * 定义实体的物理刚体属性
 */

import { defineComponent } from '@dl-engine/engine-ecs'
import { BodyType, PhysicsVector3, PhysicsQuaternion } from '../types/PhysicsTypes'

/**
 * 刚体组件数据
 */
export interface RigidBodyComponentType {
  /** 刚体类型 */
  type: BodyType
  
  /** 质量（仅对动态刚体有效） */
  mass: number
  
  /** 线性速度 */
  linearVelocity: PhysicsVector3
  
  /** 角速度 */
  angularVelocity: PhysicsVector3
  
  /** 线性阻尼 */
  linearDamping: number
  
  /** 角阻尼 */
  angularDamping: number
  
  /** 重力缩放 */
  gravityScale: number
  
  /** 是否启用CCD（连续碰撞检测） */
  enableCCD: boolean
  
  /** 是否锁定旋转 */
  lockRotations: boolean
  
  /** 锁定的旋转轴 */
  lockedAxes: {
    x: boolean
    y: boolean
    z: boolean
  }
  
  /** 是否可以睡眠 */
  canSleep: boolean
  
  /** 睡眠阈值 */
  sleepThreshold: number
  
  /** 是否已初始化 */
  initialized: boolean
  
  /** 上一帧的位置（用于插值） */
  previousPosition: PhysicsVector3
  
  /** 上一帧的旋转（用于插值） */
  previousRotation: PhysicsQuaternion
  
  /** 是否需要同步变换 */
  needsTransformSync: boolean
}

/**
 * 刚体组件默认值
 */
const RigidBodyComponentDefaults: RigidBodyComponentType = {
  type: BodyType.DYNAMIC,
  mass: 1.0,
  linearVelocity: { x: 0, y: 0, z: 0 },
  angularVelocity: { x: 0, y: 0, z: 0 },
  linearDamping: 0.01,
  angularDamping: 0.05,
  gravityScale: 1.0,
  enableCCD: false,
  lockRotations: false,
  lockedAxes: { x: false, y: false, z: false },
  canSleep: true,
  sleepThreshold: 0.01,
  initialized: false,
  previousPosition: { x: 0, y: 0, z: 0 },
  previousRotation: { x: 0, y: 0, z: 0, w: 1 },
  needsTransformSync: false
}

/**
 * 刚体组件定义
 */
export const RigidBodyComponent = defineComponent({
  name: 'RigidBodyComponent',
  schema: RigidBodyComponentDefaults,
  
  onAdd: (entity, component) => {
    console.log(`RigidBody component added to entity ${entity}`)
  },
  
  onRemove: (entity, component) => {
    console.log(`RigidBody component removed from entity ${entity}`)
  },
  
  onSet: (entity, component) => {
    // 标记需要同步变换
    component.needsTransformSync = true
  }
})

/**
 * 刚体组件工具函数
 */
export const RigidBodyComponentUtils = {
  /**
   * 创建动态刚体配置
   */
  createDynamic: (mass = 1.0): Partial<RigidBodyComponentType> => ({
    type: BodyType.DYNAMIC,
    mass,
    canSleep: true
  }),
  
  /**
   * 创建静态刚体配置
   */
  createStatic: (): Partial<RigidBodyComponentType> => ({
    type: BodyType.STATIC,
    mass: 0,
    canSleep: false
  }),
  
  /**
   * 创建运动学刚体配置
   */
  createKinematic: (): Partial<RigidBodyComponentType> => ({
    type: BodyType.KINEMATIC,
    mass: 0,
    canSleep: false
  }),
  
  /**
   * 设置速度
   */
  setVelocity: (
    component: RigidBodyComponentType,
    linear: PhysicsVector3,
    angular?: PhysicsVector3
  ) => {
    component.linearVelocity = { ...linear }
    if (angular) {
      component.angularVelocity = { ...angular }
    }
    component.needsTransformSync = true
  },
  
  /**
   * 设置阻尼
   */
  setDamping: (
    component: RigidBodyComponentType,
    linear: number,
    angular: number
  ) => {
    component.linearDamping = linear
    component.angularDamping = angular
  },
  
  /**
   * 锁定旋转轴
   */
  lockRotationAxes: (
    component: RigidBodyComponentType,
    x: boolean,
    y: boolean,
    z: boolean
  ) => {
    component.lockedAxes.x = x
    component.lockedAxes.y = y
    component.lockedAxes.z = z
    component.lockRotations = x || y || z
    component.needsTransformSync = true
  },
  
  /**
   * 检查是否为动态刚体
   */
  isDynamic: (component: RigidBodyComponentType): boolean => {
    return component.type === BodyType.DYNAMIC
  },
  
  /**
   * 检查是否为静态刚体
   */
  isStatic: (component: RigidBodyComponentType): boolean => {
    return component.type === BodyType.STATIC
  },
  
  /**
   * 检查是否为运动学刚体
   */
  isKinematic: (component: RigidBodyComponentType): boolean => {
    return component.type === BodyType.KINEMATIC
  },
  
  /**
   * 计算动能
   */
  calculateKineticEnergy: (component: RigidBodyComponentType): number => {
    if (!RigidBodyComponentUtils.isDynamic(component)) {
      return 0
    }
    
    const { linearVelocity, angularVelocity, mass } = component
    
    // 线性动能 = 0.5 * m * v²
    const linearKE = 0.5 * mass * (
      linearVelocity.x * linearVelocity.x +
      linearVelocity.y * linearVelocity.y +
      linearVelocity.z * linearVelocity.z
    )
    
    // 角动能 = 0.5 * I * ω² (简化计算，假设惯性张量为单位矩阵)
    const angularKE = 0.5 * mass * (
      angularVelocity.x * angularVelocity.x +
      angularVelocity.y * angularVelocity.y +
      angularVelocity.z * angularVelocity.z
    )
    
    return linearKE + angularKE
  },
  
  /**
   * 检查是否应该睡眠
   */
  shouldSleep: (component: RigidBodyComponentType): boolean => {
    if (!component.canSleep || !RigidBodyComponentUtils.isDynamic(component)) {
      return false
    }
    
    const kineticEnergy = RigidBodyComponentUtils.calculateKineticEnergy(component)
    return kineticEnergy < component.sleepThreshold
  },
  
  /**
   * 重置速度
   */
  resetVelocity: (component: RigidBodyComponentType) => {
    component.linearVelocity = { x: 0, y: 0, z: 0 }
    component.angularVelocity = { x: 0, y: 0, z: 0 }
    component.needsTransformSync = true
  },
  
  /**
   * 应用冲量
   */
  applyImpulse: (
    component: RigidBodyComponentType,
    impulse: PhysicsVector3,
    point?: PhysicsVector3
  ) => {
    if (!RigidBodyComponentUtils.isDynamic(component)) {
      return
    }
    
    // 线性冲量
    const invMass = 1 / component.mass
    component.linearVelocity.x += impulse.x * invMass
    component.linearVelocity.y += impulse.y * invMass
    component.linearVelocity.z += impulse.z * invMass
    
    // 如果指定了作用点，计算角冲量
    if (point) {
      // 简化计算：假设质心在原点，惯性张量为单位矩阵
      const torque = {
        x: point.y * impulse.z - point.z * impulse.y,
        y: point.z * impulse.x - point.x * impulse.z,
        z: point.x * impulse.y - point.y * impulse.x
      }
      
      component.angularVelocity.x += torque.x * invMass
      component.angularVelocity.y += torque.y * invMass
      component.angularVelocity.z += torque.z * invMass
    }
    
    component.needsTransformSync = true
  }
}
