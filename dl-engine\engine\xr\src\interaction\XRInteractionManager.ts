/**
 * DL-Engine XR交互管理器
 * 处理VR/AR控制器、手势识别和交互事件
 */

import {
  Vector3,
  Quaternion,
  Matrix4,
  Raycaster,
  Object3D,
  Group,
  BufferGeometry,
  Line,
  LineBasicMaterial,
  SphereGeometry,
  MeshBasicMaterial,
  Mesh
} from 'three'

import { Entity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * XR输入源类型
 */
export type XRInputSourceType = 'controller' | 'hand' | 'gaze' | 'voice'

/**
 * XR交互事件类型
 */
export type XRInteractionEventType = 
  | 'select' 
  | 'selectstart' 
  | 'selectend'
  | 'squeeze'
  | 'squeezestart'
  | 'squeezeend'
  | 'hover'
  | 'hoverstart'
  | 'hoverend'
  | 'grab'
  | 'release'

/**
 * XR控制器状态
 */
export interface XRControllerState {
  /** 控制器ID */
  id: string
  
  /** 输入源 */
  inputSource: XRInputSource
  
  /** 位置 */
  position: Vector3
  
  /** 旋转 */
  rotation: Quaternion
  
  /** 是否连接 */
  connected: boolean
  
  /** 按钮状态 */
  buttons: { [key: string]: boolean }
  
  /** 轴状态 */
  axes: { [key: string]: number }
  
  /** 射线投射器 */
  raycaster: Raycaster
  
  /** 可视化对象 */
  visualObject: Object3D | null
}

/**
 * XR手部状态
 */
export interface XRHandState {
  /** 手部ID */
  id: string
  
  /** 手部类型 */
  handedness: 'left' | 'right'
  
  /** 关节位置 */
  joints: Map<string, { position: Vector3; rotation: Quaternion }>
  
  /** 手势识别结果 */
  gesture: string | null
  
  /** 是否追踪中 */
  tracking: boolean
}

/**
 * XR交互事件
 */
export interface XRInteractionEvent {
  /** 事件类型 */
  type: XRInteractionEventType
  
  /** 输入源类型 */
  inputType: XRInputSourceType
  
  /** 输入源ID */
  inputId: string
  
  /** 目标对象 */
  target: Object3D | null
  
  /** 交互点 */
  point: Vector3 | null
  
  /** 法向量 */
  normal: Vector3 | null
  
  /** 距离 */
  distance: number
  
  /** 时间戳 */
  timestamp: number
}

/**
 * XR交互状态
 */
export interface XRInteractionState {
  /** 控制器状态 */
  controllers: Map<string, XRControllerState>
  
  /** 手部状态 */
  hands: Map<string, XRHandState>
  
  /** 可交互对象 */
  interactables: Set<Object3D>
  
  /** 当前悬停对象 */
  hoveredObjects: Map<string, Object3D>
  
  /** 当前选中对象 */
  selectedObjects: Map<string, Object3D>
  
  /** 事件监听器 */
  eventListeners: Map<XRInteractionEventType, Set<(event: XRInteractionEvent) => void>>
}

/**
 * XR交互状态定义
 */
export const XRInteractionState = defineState({
  name: 'DLEngine.XRInteraction',
  initial: (): XRInteractionState => ({
    controllers: new Map(),
    hands: new Map(),
    interactables: new Set(),
    hoveredObjects: new Map(),
    selectedObjects: new Map(),
    eventListeners: new Map()
  }),
  
  receptors: {
    /**
     * 添加控制器
     */
    addController: (state, id: string, controller: XRControllerState) => {
      state.controllers.get(NO_PROXY).set(id, controller)
    },
    
    /**
     * 移除控制器
     */
    removeController: (state, id: string) => {
      state.controllers.get(NO_PROXY).delete(id)
    },
    
    /**
     * 更新控制器状态
     */
    updateController: (state, id: string, updates: Partial<XRControllerState>) => {
      const controller = state.controllers.get(NO_PROXY).get(id)
      if (controller) {
        Object.assign(controller, updates)
      }
    },
    
    /**
     * 添加可交互对象
     */
    addInteractable: (state, object: Object3D) => {
      state.interactables.get(NO_PROXY).add(object)
    },
    
    /**
     * 移除可交互对象
     */
    removeInteractable: (state, object: Object3D) => {
      state.interactables.get(NO_PROXY).delete(object)
    }
  }
})

// 导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * XR交互管理器
 */
export class XRInteractionManager {
  private static instance: XRInteractionManager | null = null
  private session: XRSession | null = null
  private referenceSpace: XRReferenceSpace | null = null
  private frame: XRFrame | null = null
  
  /**
   * 获取单例实例
   */
  static getInstance(): XRInteractionManager {
    if (!XRInteractionManager.instance) {
      XRInteractionManager.instance = new XRInteractionManager()
    }
    return XRInteractionManager.instance
  }
  
  /**
   * 初始化交互管理器
   */
  initialize(session: XRSession, referenceSpace: XRReferenceSpace): void {
    this.session = session
    this.referenceSpace = referenceSpace
    
    // 设置事件监听器
    this.setupEventListeners()
    
    console.log('XR Interaction Manager initialized')
  }
  
  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.session) return
    
    // 输入源变化
    this.session.addEventListener('inputsourceschange', (event) => {
      this.handleInputSourcesChange(event)
    })
    
    // 选择事件
    this.session.addEventListener('select', (event) => {
      this.handleSelect(event)
    })
    
    this.session.addEventListener('selectstart', (event) => {
      this.handleSelectStart(event)
    })
    
    this.session.addEventListener('selectend', (event) => {
      this.handleSelectEnd(event)
    })
    
    // 挤压事件
    this.session.addEventListener('squeeze', (event) => {
      this.handleSqueeze(event)
    })
    
    this.session.addEventListener('squeezestart', (event) => {
      this.handleSqueezeStart(event)
    })
    
    this.session.addEventListener('squeezeend', (event) => {
      this.handleSqueezeEnd(event)
    })
  }
  
  /**
   * 更新交互状态
   */
  update(frame: XRFrame): void {
    this.frame = frame
    
    // 更新控制器状态
    this.updateControllers()
    
    // 更新手部追踪
    this.updateHands()
    
    // 执行射线投射
    this.performRaycasting()
  }
  
  /**
   * 更新控制器状态
   */
  private updateControllers(): void {
    if (!this.session || !this.frame || !this.referenceSpace) return
    
    const state = getState(XRInteractionState)
    
    for (const inputSource of this.session.inputSources) {
      if (inputSource.targetRayMode === 'tracked-pointer') {
        const controllerId = this.getInputSourceId(inputSource)
        let controller = state.controllers.get(controllerId)
        
        if (!controller) {
          controller = this.createController(inputSource)
          getMutableState(XRInteractionState).addController(controllerId, controller)
        }
        
        // 更新控制器姿态
        const pose = this.frame.getPose(inputSource.targetRaySpace, this.referenceSpace)
        if (pose) {
          controller.position.copy(pose.transform.position as any)
          controller.rotation.copy(pose.transform.orientation as any)
          
          // 更新射线投射器
          controller.raycaster.ray.origin.copy(controller.position)
          const direction = new Vector3(0, 0, -1).applyQuaternion(controller.rotation)
          controller.raycaster.ray.direction.copy(direction)
        }
        
        // 更新按钮状态
        if (inputSource.gamepad) {
          this.updateGamepadState(controller, inputSource.gamepad)
        }
      }
    }
  }
  
  /**
   * 创建控制器
   */
  private createController(inputSource: XRInputSource): XRControllerState {
    const controller: XRControllerState = {
      id: this.getInputSourceId(inputSource),
      inputSource,
      position: new Vector3(),
      rotation: new Quaternion(),
      connected: true,
      buttons: {},
      axes: {},
      raycaster: new Raycaster(),
      visualObject: this.createControllerVisual()
    }
    
    return controller
  }
  
  /**
   * 创建控制器可视化对象
   */
  private createControllerVisual(): Object3D {
    const group = new Group()
    
    // 创建射线
    const rayGeometry = new BufferGeometry().setFromPoints([
      new Vector3(0, 0, 0),
      new Vector3(0, 0, -1)
    ])
    const rayMaterial = new LineBasicMaterial({ color: 0x00ff00 })
    const ray = new Line(rayGeometry, rayMaterial)
    group.add(ray)
    
    // 创建控制器指示器
    const sphereGeometry = new SphereGeometry(0.01, 8, 8)
    const sphereMaterial = new MeshBasicMaterial({ color: 0xff0000 })
    const sphere = new Mesh(sphereGeometry, sphereMaterial)
    group.add(sphere)
    
    return group
  }
  
  /**
   * 更新手柄状态
   */
  private updateGamepadState(controller: XRControllerState, gamepad: Gamepad): void {
    // 更新按钮状态
    gamepad.buttons.forEach((button, index) => {
      controller.buttons[`button_${index}`] = button.pressed
    })
    
    // 更新轴状态
    gamepad.axes.forEach((axis, index) => {
      controller.axes[`axis_${index}`] = axis
    })
  }
  
  /**
   * 更新手部追踪
   */
  private updateHands(): void {
    // TODO: 实现手部追踪更新
    // 需要WebXR Hand Tracking API支持
  }
  
  /**
   * 执行射线投射
   */
  private performRaycasting(): void {
    const state = getState(XRInteractionState)
    const interactables = Array.from(state.interactables)
    
    for (const [controllerId, controller] of state.controllers) {
      // 执行射线投射
      const intersections = controller.raycaster.intersectObjects(interactables, true)
      
      if (intersections.length > 0) {
        const intersection = intersections[0]
        const object = intersection.object
        
        // 检查是否是新的悬停对象
        const currentHovered = state.hoveredObjects.get(controllerId)
        if (currentHovered !== object) {
          // 触发悬停结束事件
          if (currentHovered) {
            this.dispatchEvent({
              type: 'hoverend',
              inputType: 'controller',
              inputId: controllerId,
              target: currentHovered,
              point: null,
              normal: null,
              distance: 0,
              timestamp: performance.now()
            })
          }
          
          // 更新悬停对象
          getMutableState(XRInteractionState).hoveredObjects.get(NO_PROXY).set(controllerId, object)
          
          // 触发悬停开始事件
          this.dispatchEvent({
            type: 'hoverstart',
            inputType: 'controller',
            inputId: controllerId,
            target: object,
            point: intersection.point,
            normal: intersection.face?.normal || null,
            distance: intersection.distance,
            timestamp: performance.now()
          })
        }
        
        // 触发悬停事件
        this.dispatchEvent({
          type: 'hover',
          inputType: 'controller',
          inputId: controllerId,
          target: object,
          point: intersection.point,
          normal: intersection.face?.normal || null,
          distance: intersection.distance,
          timestamp: performance.now()
        })
        
      } else {
        // 没有交集，清除悬停对象
        const currentHovered = state.hoveredObjects.get(controllerId)
        if (currentHovered) {
          getMutableState(XRInteractionState).hoveredObjects.get(NO_PROXY).delete(controllerId)
          
          this.dispatchEvent({
            type: 'hoverend',
            inputType: 'controller',
            inputId: controllerId,
            target: currentHovered,
            point: null,
            normal: null,
            distance: 0,
            timestamp: performance.now()
          })
        }
      }
    }
  }
  
  /**
   * 处理输入源变化
   */
  private handleInputSourcesChange(event: XRInputSourceChangeEvent): void {
    // 处理新增的输入源
    for (const inputSource of event.added) {
      console.log('Input source added:', inputSource)
    }
    
    // 处理移除的输入源
    for (const inputSource of event.removed) {
      const id = this.getInputSourceId(inputSource)
      getMutableState(XRInteractionState).removeController(id)
      console.log('Input source removed:', inputSource)
    }
  }
  
  /**
   * 处理选择事件
   */
  private handleSelect(event: XRInputSourceEvent): void {
    const controllerId = this.getInputSourceId(event.inputSource)
    const state = getState(XRInteractionState)
    const hoveredObject = state.hoveredObjects.get(controllerId)
    
    if (hoveredObject) {
      this.dispatchEvent({
        type: 'select',
        inputType: 'controller',
        inputId: controllerId,
        target: hoveredObject,
        point: null,
        normal: null,
        distance: 0,
        timestamp: performance.now()
      })
    }
  }
  
  /**
   * 处理选择开始事件
   */
  private handleSelectStart(event: XRInputSourceEvent): void {
    const controllerId = this.getInputSourceId(event.inputSource)
    const state = getState(XRInteractionState)
    const hoveredObject = state.hoveredObjects.get(controllerId)
    
    if (hoveredObject) {
      getMutableState(XRInteractionState).selectedObjects.get(NO_PROXY).set(controllerId, hoveredObject)
      
      this.dispatchEvent({
        type: 'selectstart',
        inputType: 'controller',
        inputId: controllerId,
        target: hoveredObject,
        point: null,
        normal: null,
        distance: 0,
        timestamp: performance.now()
      })
    }
  }
  
  /**
   * 处理选择结束事件
   */
  private handleSelectEnd(event: XRInputSourceEvent): void {
    const controllerId = this.getInputSourceId(event.inputSource)
    const state = getState(XRInteractionState)
    const selectedObject = state.selectedObjects.get(controllerId)
    
    if (selectedObject) {
      getMutableState(XRInteractionState).selectedObjects.get(NO_PROXY).delete(controllerId)
      
      this.dispatchEvent({
        type: 'selectend',
        inputType: 'controller',
        inputId: controllerId,
        target: selectedObject,
        point: null,
        normal: null,
        distance: 0,
        timestamp: performance.now()
      })
    }
  }
  
  /**
   * 处理挤压事件
   */
  private handleSqueeze(event: XRInputSourceEvent): void {
    // TODO: 实现挤压事件处理
  }
  
  private handleSqueezeStart(event: XRInputSourceEvent): void {
    // TODO: 实现挤压开始事件处理
  }
  
  private handleSqueezeEnd(event: XRInputSourceEvent): void {
    // TODO: 实现挤压结束事件处理
  }
  
  /**
   * 获取输入源ID
   */
  private getInputSourceId(inputSource: XRInputSource): string {
    return `${inputSource.handedness}_${inputSource.targetRayMode}`
  }
  
  /**
   * 分发事件
   */
  private dispatchEvent(event: XRInteractionEvent): void {
    const state = getState(XRInteractionState)
    const listeners = state.eventListeners.get(event.type)
    
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(event)
        } catch (error) {
          console.error('Error in XR interaction event listener:', error)
        }
      }
    }
  }
  
  /**
   * 添加事件监听器
   */
  addEventListener(type: XRInteractionEventType, listener: (event: XRInteractionEvent) => void): void {
    const state = getMutableState(XRInteractionState)
    
    if (!state.eventListeners.value.has(type)) {
      state.eventListeners.get(NO_PROXY).set(type, new Set())
    }
    
    state.eventListeners.get(NO_PROXY).get(type)!.add(listener)
  }
  
  /**
   * 移除事件监听器
   */
  removeEventListener(type: XRInteractionEventType, listener: (event: XRInteractionEvent) => void): void {
    const state = getMutableState(XRInteractionState)
    const listeners = state.eventListeners.value.get(type)
    
    if (listeners) {
      listeners.delete(listener)
    }
  }
  
  /**
   * 添加可交互对象
   */
  addInteractable(object: Object3D): void {
    getMutableState(XRInteractionState).addInteractable(object)
  }
  
  /**
   * 移除可交互对象
   */
  removeInteractable(object: Object3D): void {
    getMutableState(XRInteractionState).removeInteractable(object)
  }
}
