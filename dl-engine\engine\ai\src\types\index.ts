/**
 * DL-Engine AI类型定义
 */

/**
 * AI模型类型
 */
export type AIModelType = 'llm' | 'embedding' | 'classification' | 'generation'

/**
 * AI任务类型
 */
export type AITaskType = 
  | 'text_generation'
  | 'text_classification'
  | 'sentiment_analysis'
  | 'keyword_extraction'
  | 'summarization'
  | 'translation'
  | 'question_answering'
  | 'content_recommendation'
  | 'learning_path_generation'

/**
 * AI服务配置
 */
export interface AIServiceConfig {
  /** 服务名称 */
  name: string
  
  /** 服务类型 */
  type: AIModelType
  
  /** 服务端点 */
  endpoint: string
  
  /** API密钥 */
  apiKey?: string
  
  /** 超时时间 */
  timeout: number
  
  /** 最大重试次数 */
  maxRetries: number
  
  /** 是否启用 */
  enabled: boolean
}

/**
 * AI任务请求
 */
export interface AITaskRequest {
  /** 任务ID */
  id: string
  
  /** 任务类型 */
  type: AITaskType
  
  /** 输入数据 */
  input: any
  
  /** 任务参数 */
  parameters?: Record<string, any>
  
  /** 优先级 */
  priority: 'low' | 'medium' | 'high'
  
  /** 超时时间 */
  timeout?: number
}

/**
 * AI任务响应
 */
export interface AITaskResponse {
  /** 任务ID */
  id: string
  
  /** 任务状态 */
  status: 'pending' | 'running' | 'completed' | 'failed'
  
  /** 结果数据 */
  result?: any
  
  /** 错误信息 */
  error?: string
  
  /** 执行时间 */
  executionTime: number
  
  /** 置信度 */
  confidence?: number
  
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * 向量嵌入
 */
export interface Embedding {
  /** 向量维度 */
  dimensions: number
  
  /** 向量数据 */
  vector: number[]
  
  /** 原始文本 */
  text?: string
  
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * 相似度搜索结果
 */
export interface SimilaritySearchResult {
  /** 文档ID */
  id: string
  
  /** 相似度分数 */
  score: number
  
  /** 文档内容 */
  content: string
  
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * 知识图谱节点
 */
export interface KnowledgeNode {
  /** 节点ID */
  id: string
  
  /** 节点类型 */
  type: string
  
  /** 节点标签 */
  label: string
  
  /** 节点属性 */
  properties: Record<string, any>
  
  /** 关联关系 */
  relationships: Array<{
    type: string
    targetId: string
    properties?: Record<string, any>
  }>
}

/**
 * 学习内容元数据
 */
export interface LearningContentMetadata {
  /** 内容ID */
  id: string
  
  /** 内容标题 */
  title: string
  
  /** 内容类型 */
  type: 'lesson' | 'exercise' | 'project' | 'assessment' | 'resource'
  
  /** 学科领域 */
  subject: string
  
  /** 难度等级 */
  difficulty: number
  
  /** 预估学习时间 */
  estimatedDuration: number
  
  /** 学习目标 */
  learningObjectives: string[]
  
  /** 前置知识 */
  prerequisites: string[]
  
  /** 关键词标签 */
  tags: string[]
  
  /** 适用年龄段 */
  ageRange: {
    min: number
    max: number
  }
  
  /** 语言 */
  language: string
  
  /** 创建时间 */
  createdAt: number
  
  /** 更新时间 */
  updatedAt: number
}

/**
 * 个性化推荐参数
 */
export interface PersonalizationParams {
  /** 用户ID */
  userId: string
  
  /** 学习风格权重 */
  learningStyleWeights: {
    visual: number
    auditory: number
    kinesthetic: number
    readingWriting: number
  }
  
  /** 难度偏好 */
  difficultyPreference: number
  
  /** 兴趣领域 */
  interests: string[]
  
  /** 学习目标 */
  goals: string[]
  
  /** 时间约束 */
  timeConstraints: {
    maxSessionDuration: number
    availableTimeSlots: Array<{
      start: number
      end: number
    }>
  }
}

/**
 * AI模型性能指标
 */
export interface ModelPerformanceMetrics {
  /** 模型名称 */
  modelName: string
  
  /** 准确率 */
  accuracy: number
  
  /** 精确率 */
  precision: number
  
  /** 召回率 */
  recall: number
  
  /** F1分数 */
  f1Score: number
  
  /** 平均响应时间 */
  averageResponseTime: number
  
  /** 吞吐量 */
  throughput: number
  
  /** 错误率 */
  errorRate: number
  
  /** 最后评估时间 */
  lastEvaluated: number
}
