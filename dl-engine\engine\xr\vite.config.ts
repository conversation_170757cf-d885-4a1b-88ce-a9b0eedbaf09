import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    target: 'es2022',
    lib: {
      entry: resolve(__dirname, 'index.ts'),
      name: 'DLEngineXR',
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'three',
        '@types/three',
        'three-stdlib',
        '@dl-engine/engine-ecs',
        '@dl-engine/engine-state',
        '@dl-engine/engine-core'
      ]
    },
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@dl-engine/engine-ecs': resolve(__dirname, '../ecs/src'),
      '@dl-engine/engine-state': resolve(__dirname, '../state/src'),
      '@dl-engine/engine-core': resolve(__dirname, '../core/src'),
      '@dl-engine/shared-common': resolve(__dirname, '../../shared/common/src')
    }
  },
  test: {
    environment: 'jsdom',
    globals: true
  }
})
