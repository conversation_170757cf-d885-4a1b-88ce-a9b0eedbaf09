/**
 * DL-Engine 常量定义
 */

// 引擎常量
export const ENGINE_CONSTANTS = {
  VERSION: '1.0.0',
  NAME: 'Digital Learning Engine',
  MAX_ENTITIES: 100000,
  MAX_COMPONENTS_PER_ENTITY: 64,
  TARGET_FPS: 60,
  MAX_DELTA_TIME: 1 / 30 // 最大帧时间 (30fps)
} as const

// 数学常量
export const MATH_CONSTANTS = {
  PI: Math.PI,
  TWO_PI: Math.PI * 2,
  HALF_PI: Math.PI / 2,
  DEG_TO_RAD: Math.PI / 180,
  RAD_TO_DEG: 180 / Math.PI,
  EPSILON: 1e-6
} as const

// 向量常量
export const VECTOR3_ZERO = { x: 0, y: 0, z: 0 } as const
export const VECTOR3_ONE = { x: 1, y: 1, z: 1 } as const
export const VECTOR3_UP = { x: 0, y: 1, z: 0 } as const
export const VECTOR3_DOWN = { x: 0, y: -1, z: 0 } as const
export const VECTOR3_LEFT = { x: -1, y: 0, z: 0 } as const
export const VECTOR3_RIGHT = { x: 1, y: 0, z: 0 } as const
export const VECTOR3_FORWARD = { x: 0, y: 0, z: -1 } as const
export const VECTOR3_BACK = { x: 0, y: 0, z: 1 } as const

// 四元数常量
export const QUATERNION_IDENTITY = { x: 0, y: 0, z: 0, w: 1 } as const

// 网络常量
export const NETWORK_CONSTANTS = {
  MAX_MESSAGE_SIZE: 1024 * 1024, // 1MB
  HEARTBEAT_INTERVAL: 30000, // 30秒
  CONNECTION_TIMEOUT: 60000, // 60秒
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 2000 // 2秒
} as const

// 教育常量
export const EDUCATION_CONSTANTS = {
  MAX_COURSE_TITLE_LENGTH: 100,
  MAX_COURSE_DESCRIPTION_LENGTH: 1000,
  MAX_ASSIGNMENT_TITLE_LENGTH: 100,
  MAX_ASSIGNMENT_DESCRIPTION_LENGTH: 2000,
  MAX_STUDENTS_PER_COURSE: 1000,
  DEFAULT_ASSIGNMENT_DURATION: 7 * 24 * 60 * 60 * 1000 // 7天
} as const

// 用户常量
export const USER_CONSTANTS = {
  MIN_USERNAME_LENGTH: 3,
  MAX_USERNAME_LENGTH: 20,
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,
  PHONE_REGEX: /^1[3-9]\d{9}$/, // 中国手机号正则
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
} as const

// 文件常量
export const FILE_CONSTANTS = {
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_MODEL_TYPES: ['model/gltf-binary', 'model/gltf+json'],
  ALLOWED_AUDIO_TYPES: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg']
} as const

// API常量
export const API_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  API_VERSION: 'v1',
  REQUEST_TIMEOUT: 30000 // 30秒
} as const

// 缓存常量
export const CACHE_CONSTANTS = {
  DEFAULT_TTL: 3600, // 1小时
  USER_SESSION_TTL: 24 * 3600, // 24小时
  ASSET_CACHE_TTL: 7 * 24 * 3600, // 7天
  SCENE_CACHE_TTL: 3600 // 1小时
} as const

// 错误代码
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_REQUEST: 'INVALID_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  
  // 用户相关错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  INVALID_PHONE_NUMBER: 'INVALID_PHONE_NUMBER',
  VERIFICATION_CODE_EXPIRED: 'VERIFICATION_CODE_EXPIRED',
  
  // 项目相关错误
  PROJECT_NOT_FOUND: 'PROJECT_NOT_FOUND',
  PROJECT_ACCESS_DENIED: 'PROJECT_ACCESS_DENIED',
  SCENE_NOT_FOUND: 'SCENE_NOT_FOUND',
  
  // 文件相关错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // 网络相关错误
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  MESSAGE_TOO_LARGE: 'MESSAGE_TOO_LARGE'
} as const
