import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'path'
import dts from 'vite-plugin-dts'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'
  const isTest = command === 'test'

  return {
    // 定义全局常量
    define: {
      __DEV__: isDevelopment,
      __PROD__: isProduction,
      __TEST__: isTest,
      __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    },

    // 插件配置
    plugins: [
      // 生成TypeScript声明文件
      dts({
        include: ['engine/**/*.ts', 'shared/**/*.ts', 'index.ts'],
        exclude: ['**/*.test.ts', '**/*.spec.ts', '**/node_modules/**'],
        outDir: 'dist/types',
        rollupTypes: true,
        insertTypesEntry: true
      }),

      // 构建分析器（仅在分析模式下启用）
      env.ANALYZE && visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true
      })
    ].filter(Boolean),

    build: {
      target: 'es2022',
      lib: {
        entry: {
          index: resolve(__dirname, 'index.ts'),
          core: resolve(__dirname, 'engine/core/index.ts'),
          ecs: resolve(__dirname, 'engine/ecs/index.ts'),
          physics: resolve(__dirname, 'engine/physics/index.ts'),
          state: resolve(__dirname, 'engine/state/index.ts'),
          xr: resolve(__dirname, 'engine/xr/index.ts'),
          ai: resolve(__dirname, 'engine/ai/index.ts')
        },
        name: 'DLEngine',
        formats: isProduction ? ['es', 'cjs', 'umd'] : ['es', 'cjs']
      },
      rollupOptions: {
        external: [
          'react',
          'react-dom',
          'three',
          '@types/three',
          'three-stdlib',
          'bitecs',
          '@hookstate/core',
          '@dimforge/rapier3d',
          'axios',
          'vitest'
        ],
        output: [
          // ES模块输出
          {
            format: 'es',
            entryFileNames: '[name].esm.js',
            chunkFileNames: 'chunks/[name]-[hash].esm.js',
            assetFileNames: 'assets/[name]-[hash][extname]',
            globals: {
              'react': 'React',
              'react-dom': 'ReactDOM',
              'three': 'THREE',
              'bitecs': 'bitECS',
              '@hookstate/core': 'Hookstate',
              '@dimforge/rapier3d': 'RAPIER',
              'axios': 'axios'
            }
          },
          // CommonJS输出
          {
            format: 'cjs',
            entryFileNames: '[name].cjs.js',
            chunkFileNames: 'chunks/[name]-[hash].cjs.js',
            assetFileNames: 'assets/[name]-[hash][extname]',
            exports: 'named'
          },
          // UMD输出（仅生产环境）
          ...(isProduction ? [{
            format: 'umd' as const,
            name: 'DLEngine',
            entryFileNames: '[name].umd.js',
            globals: {
              'react': 'React',
              'react-dom': 'ReactDOM',
              'three': 'THREE',
              'bitecs': 'bitECS',
              '@hookstate/core': 'Hookstate',
              '@dimforge/rapier3d': 'RAPIER',
              'axios': 'axios'
            }
          }] : [])
        ],
        // 代码分割优化
        manualChunks: {
          'three-vendor': ['three', 'three-stdlib'],
          'physics-vendor': ['@dimforge/rapier3d'],
          'state-vendor': ['@hookstate/core'],
          'utils-vendor': ['axios']
        }
      },
      sourcemap: isDevelopment ? true : 'hidden',
      minify: isProduction ? 'terser' : false,
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.debug']
        },
        mangle: {
          safari10: true
        },
        format: {
          comments: false
        }
      } : undefined,
      // 构建优化
      chunkSizeWarningLimit: 1000,
      assetsInlineLimit: 4096,
      reportCompressedSize: isProduction,
      // 输出目录清理
      emptyOutDir: true
    },
    // 性能优化
    optimizeDeps: {
      include: [
        'three',
        'three-stdlib',
        '@hookstate/core',
        'axios'
      ],
      exclude: [
        '@dimforge/rapier3d'  // WASM模块，不需要预构建
      ]
    },

    resolve: {
      alias: {
        '@dl-engine/engine-core': resolve(__dirname, './engine/core'),
        '@dl-engine/engine-ecs': resolve(__dirname, './engine/ecs'),
        '@dl-engine/engine-physics': resolve(__dirname, './engine/physics'),
        '@dl-engine/engine-state': resolve(__dirname, './engine/state'),
        '@dl-engine/engine-xr': resolve(__dirname, './engine/xr'),
        '@dl-engine/engine-ai': resolve(__dirname, './engine/ai'),
        '@dl-engine/shared-common': resolve(__dirname, './shared/common'),
        // 开发时的源码别名
        '@dl-engine/engine-core/src': resolve(__dirname, './engine/core/src'),
        '@dl-engine/engine-ecs/src': resolve(__dirname, './engine/ecs/src'),
        '@dl-engine/engine-physics/src': resolve(__dirname, './engine/physics/src'),
        '@dl-engine/engine-state/src': resolve(__dirname, './engine/state/src'),
        '@dl-engine/engine-xr/src': resolve(__dirname, './engine/xr/src'),
        '@dl-engine/engine-ai/src': resolve(__dirname, './engine/ai/src'),
        '@dl-engine/shared-common/src': resolve(__dirname, './shared/common/src')
      },
      extensions: ['.ts', '.tsx', '.js', '.jsx', '.json', '.wasm']
    },

    // 开发服务器配置
    server: {
      port: 5173,
      host: true,
      cors: true,
      headers: {
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin'
      }
    },

    // 预览服务器配置
    preview: {
      port: 4173,
      host: true,
      cors: true
    },

    // 测试配置
    test: {
      environment: 'jsdom',
      globals: true,
      setupFiles: ['./test/setup.ts'],
      coverage: {
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: './coverage',
        exclude: [
          'node_modules/',
          'test/',
          '**/*.test.ts',
          '**/*.spec.ts',
          '**/types/',
          '**/*.d.ts',
          'dist/',
          'coverage/',
          'scripts/',
          'docs/'
        ],
        thresholds: {
          global: {
            branches: 70,
            functions: 75,
            lines: 80,
            statements: 80
          }
        }
      },
      testTimeout: 10000,
      hookTimeout: 10000,
      teardownTimeout: 5000,
      // 并行测试配置
      pool: 'threads',
      poolOptions: {
        threads: {
          singleThread: false,
          maxThreads: 4,
          minThreads: 1
        }
      }
    },

    // 工作线程配置
    worker: {
      format: 'es',
      plugins: []
    },

    // 环境变量配置
    envPrefix: ['VITE_', 'DL_ENGINE_'],

    // 日志级别
    logLevel: isDevelopment ? 'info' : 'warn',

    // 清除控制台
    clearScreen: false
  }
})
