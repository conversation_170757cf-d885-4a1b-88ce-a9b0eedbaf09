/**
 * DL-Engine 物理世界管理
 * 基于Rapier3D的物理世界创建和管理
 */

import { 
  World, 
  RigidBody, 
  Collider, 
  KinematicCharacterController,
  EventQueue,
  JointSet,
  ImpulseJointSet,
  MultibodyJointSet,
  CCDSolver,
  QueryPipeline,
  PhysicsPipeline,
  IslandManager,
  BroadPhase,
  NarrowPhase,
  Vector3 as RapierVector3
} from '@dimforge/rapier3d'

import { Entity, UndefinedEntity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState } from '@dl-engine/engine-state'
import { PhysicsWorldConfig, PhysicsVector3, PhysicsDebugInfo } from './types/PhysicsTypes'

/**
 * DL-Engine物理世界扩展
 */
export interface DLPhysicsWorld extends World {
  /** 世界ID（实体） */
  id: Entity
  
  /** 时间步长细分数 */
  substeps: number
  
  /** 当前时间步长 */
  timestep: number
  
  /** 刚体映射 */
  rigidBodies: Map<Entity, RigidBody>
  
  /** 碰撞体映射 */
  colliders: Map<Entity, Collider>
  
  /** 角色控制器映射 */
  characterControllers: Map<Entity, KinematicCharacterController>
  
  /** 碰撞事件队列 */
  collisionEventQueue: EventQueue
  
  /** 接触事件队列 */
  contactEventQueue: EventQueue
  
  /** 调试信息 */
  debugInfo: PhysicsDebugInfo
  
  /** 是否启用调试渲染 */
  debugRenderEnabled: boolean
  
  /** 性能统计 */
  performanceStats: {
    lastStepTime: number
    averageStepTime: number
    stepCount: number
  }
}

/**
 * 物理世界状态
 */
export interface PhysicsWorldStateType {
  /** 物理世界映射 */
  worlds: Map<Entity, DLPhysicsWorld>
  
  /** 默认世界ID */
  defaultWorldId: Entity
  
  /** 是否已初始化Rapier */
  rapierInitialized: boolean
  
  /** 全局物理配置 */
  globalConfig: PhysicsWorldConfig
  
  /** 性能监控 */
  performanceMonitoring: {
    enabled: boolean
    maxStepTime: number
    warningThreshold: number
  }
}

/**
 * 默认物理世界配置
 */
const defaultPhysicsConfig: PhysicsWorldConfig = {
  gravity: { x: 0, y: -9.81, z: 0 },
  substeps: 1,
  enableCCD: true,
  integrationParameters: {
    numSolverIterations: 4,
    numAdditionalFrictionIterations: 4,
    predictionDistance: 0.002
  }
}

/**
 * 物理世界状态定义
 */
export const PhysicsWorldState = defineState({
  name: 'DLEngine.PhysicsWorld',
  initial: (): PhysicsWorldStateType => ({
    worlds: new Map(),
    defaultWorldId: UndefinedEntity,
    rapierInitialized: false,
    globalConfig: defaultPhysicsConfig,
    performanceMonitoring: {
      enabled: false,
      maxStepTime: 16.67, // 60fps
      warningThreshold: 10 // 10ms
    }
  }),
  
  receptors: {
    /**
     * 设置Rapier初始化状态
     */
    setRapierInitialized: (state, initialized: boolean) => {
      state.rapierInitialized.set(initialized)
    },
    
    /**
     * 设置默认世界
     */
    setDefaultWorld: (state, worldId: Entity) => {
      state.defaultWorldId.set(worldId)
    },
    
    /**
     * 添加物理世界
     */
    addWorld: (state, worldId: Entity, world: DLPhysicsWorld) => {
      state.worlds.get(NO_PROXY).set(worldId, world)
    },
    
    /**
     * 移除物理世界
     */
    removeWorld: (state, worldId: Entity) => {
      state.worlds.get(NO_PROXY).delete(worldId)
    },
    
    /**
     * 更新全局配置
     */
    updateGlobalConfig: (state, config: Partial<PhysicsWorldConfig>) => {
      state.globalConfig.merge(config)
    },
    
    /**
     * 设置性能监控
     */
    setPerformanceMonitoring: (state, config: Partial<PhysicsWorldStateType['performanceMonitoring']>) => {
      state.performanceMonitoring.merge(config)
    }
  }
})

// 需要导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * 物理世界管理器
 */
export class PhysicsWorldManager {
  private static instance: PhysicsWorldManager | null = null
  
  /**
   * 获取单例实例
   */
  static getInstance(): PhysicsWorldManager {
    if (!PhysicsWorldManager.instance) {
      PhysicsWorldManager.instance = new PhysicsWorldManager()
    }
    return PhysicsWorldManager.instance
  }
  
  /**
   * 初始化Rapier物理引擎
   */
  async initializeRapier(): Promise<void> {
    const state = getMutableState(PhysicsWorldState)
    
    if (state.rapierInitialized.value) {
      return
    }
    
    try {
      // 动态导入Rapier
      const RAPIER = await import('@dimforge/rapier3d')
      await RAPIER.init()
      
      state.setRapierInitialized(true)
      console.log('Rapier3D physics engine initialized successfully')
      
    } catch (error) {
      console.error('Failed to initialize Rapier3D:', error)
      throw error
    }
  }
  
  /**
   * 创建物理世界
   */
  createWorld(worldId: Entity, config?: Partial<PhysicsWorldConfig>): DLPhysicsWorld {
    const state = getMutableState(PhysicsWorldState)
    
    if (!state.rapierInitialized.value) {
      throw new Error('Rapier not initialized. Call initializeRapier() first.')
    }
    
    if (state.worlds.value.has(worldId)) {
      throw new Error(`Physics world with ID ${worldId} already exists`)
    }
    
    // 合并配置
    const worldConfig = { ...state.globalConfig.value, ...config }
    
    // 创建Rapier世界
    const gravity = new RapierVector3(
      worldConfig.gravity.x,
      worldConfig.gravity.y,
      worldConfig.gravity.z
    )
    
    const world = new World(gravity) as DLPhysicsWorld
    
    // 扩展世界属性
    world.id = worldId
    world.substeps = worldConfig.substeps
    world.timestep = 1 / 60 // 默认60fps
    world.rigidBodies = new Map()
    world.colliders = new Map()
    world.characterControllers = new Map()
    world.collisionEventQueue = new EventQueue(true)
    world.contactEventQueue = new EventQueue(true)
    world.debugRenderEnabled = false
    
    // 初始化调试信息
    world.debugInfo = {
      rigidBodyCount: 0,
      colliderCount: 0,
      constraintCount: 0,
      activeRigidBodyCount: 0,
      sleepingRigidBodyCount: 0,
      collisionPairCount: 0,
      stepTime: 0,
      collisionDetectionTime: 0,
      constraintSolvingTime: 0
    }
    
    // 初始化性能统计
    world.performanceStats = {
      lastStepTime: 0,
      averageStepTime: 0,
      stepCount: 0
    }
    
    // 配置积分参数
    if (worldConfig.integrationParameters) {
      const params = worldConfig.integrationParameters
      world.integrationParameters.numSolverIterations = params.numSolverIterations
      world.integrationParameters.numAdditionalFrictionIterations = params.numAdditionalFrictionIterations
      world.integrationParameters.predictionDistance = params.predictionDistance
    }
    
    // 添加到状态
    state.addWorld(worldId, world)
    
    // 设置为默认世界（如果是第一个）
    if (state.defaultWorldId.value === UndefinedEntity) {
      state.setDefaultWorld(worldId)
    }
    
    console.log(`Physics world ${worldId} created successfully`)
    return world
  }
  
  /**
   * 销毁物理世界
   */
  destroyWorld(worldId: Entity): void {
    const state = getMutableState(PhysicsWorldState)
    const world = state.worlds.value.get(worldId)
    
    if (!world) {
      console.warn(`Physics world ${worldId} not found`)
      return
    }
    
    // 清理所有物理对象
    world.rigidBodies.clear()
    world.colliders.clear()
    world.characterControllers.clear()
    
    // 释放Rapier资源
    world.free()
    
    // 从状态中移除
    state.removeWorld(worldId)
    
    // 如果是默认世界，重置默认世界
    if (state.defaultWorldId.value === worldId) {
      const remainingWorlds = Array.from(state.worlds.value.keys())
      state.setDefaultWorld(remainingWorlds.length > 0 ? remainingWorlds[0] : UndefinedEntity)
    }
    
    console.log(`Physics world ${worldId} destroyed`)
  }
  
  /**
   * 获取物理世界
   */
  getWorld(worldId?: Entity): DLPhysicsWorld | null {
    const state = PhysicsWorldState.value
    const targetId = worldId ?? state.defaultWorldId
    
    if (targetId === UndefinedEntity) {
      return null
    }
    
    return state.worlds.get(targetId) || null
  }
  
  /**
   * 获取默认物理世界
   */
  getDefaultWorld(): DLPhysicsWorld | null {
    return this.getWorld()
  }
  
  /**
   * 获取所有物理世界
   */
  getAllWorlds(): DLPhysicsWorld[] {
    const state = PhysicsWorldState.value
    return Array.from(state.worlds.values())
  }
  
  /**
   * 步进物理模拟
   */
  stepWorld(world: DLPhysicsWorld, deltaTime: number): void {
    const startTime = performance.now()
    
    // 计算时间步长
    const timestep = deltaTime / world.substeps
    world.timestep = timestep
    
    // 执行物理步进
    for (let i = 0; i < world.substeps; i++) {
      world.step(world.collisionEventQueue)
    }
    
    // 更新性能统计
    const endTime = performance.now()
    const stepTime = endTime - startTime
    
    world.performanceStats.lastStepTime = stepTime
    world.performanceStats.stepCount++
    
    // 计算平均步进时间
    const alpha = 0.1
    world.performanceStats.averageStepTime = 
      world.performanceStats.averageStepTime * (1 - alpha) + stepTime * alpha
    
    // 更新调试信息
    this.updateDebugInfo(world)
    
    // 性能警告
    const monitoring = PhysicsWorldState.performanceMonitoring.value
    if (monitoring.enabled && stepTime > monitoring.warningThreshold) {
      console.warn(`Physics step took ${stepTime.toFixed(2)}ms (threshold: ${monitoring.warningThreshold}ms)`)
    }
  }
  
  /**
   * 更新调试信息
   */
  private updateDebugInfo(world: DLPhysicsWorld): void {
    world.debugInfo.rigidBodyCount = world.rigidBodies.size
    world.debugInfo.colliderCount = world.colliders.size
    world.debugInfo.stepTime = world.performanceStats.lastStepTime
    
    // 统计活跃和睡眠的刚体
    let activeCount = 0
    let sleepingCount = 0
    
    for (const rigidBody of world.rigidBodies.values()) {
      if (rigidBody.isSleeping()) {
        sleepingCount++
      } else {
        activeCount++
      }
    }
    
    world.debugInfo.activeRigidBodyCount = activeCount
    world.debugInfo.sleepingRigidBodyCount = sleepingCount
  }
  
  /**
   * 设置世界重力
   */
  setGravity(world: DLPhysicsWorld, gravity: PhysicsVector3): void {
    const rapierGravity = new RapierVector3(gravity.x, gravity.y, gravity.z)
    world.gravity = rapierGravity
  }
  
  /**
   * 获取世界重力
   */
  getGravity(world: DLPhysicsWorld): PhysicsVector3 {
    const gravity = world.gravity
    return { x: gravity.x, y: gravity.y, z: gravity.z }
  }
}
