/**
 * DL-Engine 引擎执行函数
 * 系统执行和帧循环管理
 */

import { getState, getMutableState } from '@dl-engine/engine-state'
import { ECSState } from './ECSState'
import { EngineState } from './EngineState'
import { SystemState } from './SystemState'
import { executeSystem, SystemUUID } from './SystemFunctions'
import { nowMilliseconds } from './Timer'

/**
 * 系统组定义
 */
export const InputSystemGroup = 'InputSystemGroup' as SystemUUID
export const SimulationSystemGroup = 'SimulationSystemGroup' as SystemUUID
export const AnimationSystemGroup = 'AnimationSystemGroup' as SystemUUID
export const PresentationSystemGroup = 'PresentationSystemGroup' as SystemUUID

/**
 * 固定时间步长系统组
 */
export const FixedSystemGroups = new Set([SimulationSystemGroup])

/**
 * 执行系统
 * @param elapsedTime 当前帧时间（毫秒，相对于performance.timeOrigin）
 */
export const executeSystems = (elapsedTime: number): void => {
  const ecsState = getMutableState(ECSState)
  const engineState = getState(EngineState)
  
  // 检查引擎是否正在运行
  if (!engineState.isRunning || engineState.isPaused) {
    return
  }
  
  // 更新帧时间
  ecsState.frameTime.set(performance.timeOrigin + elapsedTime)
  
  const start = nowMilliseconds()
  
  // 计算时间增量
  const elapsedSeconds = elapsedTime / 1000
  const maxDelta = ecsState.maxDeltaSeconds.value
  const deltaSeconds = Math.max(0.001, Math.min(maxDelta, elapsedSeconds - ecsState.elapsedSeconds.value))
  
  ecsState.deltaSeconds.set(deltaSeconds)
  ecsState.elapsedSeconds.set(elapsedSeconds)
  
  // 更新模拟时间
  ecsState.simulationTime.set(ecsState.simulationTime.value + deltaSeconds * 1000)
  
  // 更新帧率
  ecsState.updateActualFPS()
  
  try {
    // 执行系统组
    executeSystem(InputSystemGroup)
    executeFixedSystem(SimulationSystemGroup)
    executeSystem(AnimationSystemGroup)
    executeSystem(PresentationSystemGroup)
  } catch (error) {
    console.error('Error executing systems:', error)
    
    // 记录系统错误
    const systemState = getMutableState(SystemState)
    const currentSystem = systemState.currentSystemUUID.value
    if (currentSystem) {
      systemState.recordSystemError(currentSystem, error instanceof Error ? error.message : String(error))
    }
  }
  
  // 更新系统执行统计
  const executionTime = nowMilliseconds() - start
  ecsState.updateSystemStats(executionTime)
  
  // 更新内存统计（每60帧更新一次）
  if (ecsState.frameCount.value % 60 === 0) {
    ecsState.updateMemoryStats()
  }
}

/**
 * 执行固定时间步长系统
 * @param systemUUID 系统UUID
 */
export const executeFixedSystem = (systemUUID: SystemUUID): void => {
  const ecsState = getMutableState(ECSState)
  const fixedDelta = ecsState.fixedDeltaSeconds.value
  
  // 累积时间
  ecsState.fixedTimeAccumulator.set(ecsState.fixedTimeAccumulator.value + ecsState.deltaSeconds.value)
  
  // 执行固定时间步长
  while (ecsState.fixedTimeAccumulator.value >= fixedDelta) {
    // 临时设置固定增量时间
    const originalDelta = ecsState.deltaSeconds.value
    ecsState.deltaSeconds.set(fixedDelta)
    
    // 执行系统
    executeSystem(systemUUID)
    
    // 恢复原始增量时间
    ecsState.deltaSeconds.set(originalDelta)
    
    // 减少累积器
    ecsState.fixedTimeAccumulator.set(ecsState.fixedTimeAccumulator.value - fixedDelta)
  }
}

/**
 * 启动引擎主循环
 */
export const startEngineLoop = (): void => {
  const engineState = getMutableState(EngineState)
  
  if (engineState.isRunning.value) {
    console.warn('Engine loop is already running')
    return
  }
  
  engineState.setRunning(true)
  
  let lastTime = 0
  
  const loop = (currentTime: number) => {
    if (!getState(EngineState).isRunning) {
      return
    }
    
    // 计算帧时间
    const deltaTime = currentTime - lastTime
    lastTime = currentTime
    
    // 执行系统
    executeSystems(currentTime)
    
    // 更新引擎运行时间
    engineState.updateUptime()
    
    // 请求下一帧
    requestAnimationFrame(loop)
  }
  
  // 启动循环
  requestAnimationFrame((time) => {
    lastTime = time
    requestAnimationFrame(loop)
  })
  
  console.log('Engine loop started')
}

/**
 * 停止引擎主循环
 */
export const stopEngineLoop = (): void => {
  const engineState = getMutableState(EngineState)
  engineState.setRunning(false)
  console.log('Engine loop stopped')
}

/**
 * 暂停引擎
 */
export const pauseEngine = (): void => {
  const ecsState = getMutableState(ECSState)
  ecsState.setPaused(true)
  console.log('Engine paused')
}

/**
 * 恢复引擎
 */
export const resumeEngine = (): void => {
  const ecsState = getMutableState(ECSState)
  ecsState.setPaused(false)
  console.log('Engine resumed')
}

/**
 * 单步执行（调试用）
 */
export const stepEngine = (): void => {
  const engineState = getState(EngineState)
  
  if (!engineState.isInitialized) {
    throw new Error('Engine not initialized')
  }
  
  const currentTime = performance.now()
  executeSystems(currentTime)
  
  console.log('Engine stepped')
}

/**
 * 重置引擎统计
 */
export const resetEngineStats = (): void => {
  const ecsState = getMutableState(ECSState)
  const systemState = getMutableState(SystemState)
  
  ecsState.resetStats()
  systemState.resetStats()
  
  console.log('Engine stats reset')
}

/**
 * 获取引擎性能信息
 */
export const getEnginePerformance = () => {
  const ecsState = getState(ECSState)
  const systemState = getState(SystemState)
  
  return {
    fps: ecsState.actualFPS,
    frameTime: ecsState.deltaSeconds * 1000,
    systemStats: ecsState.systemStats,
    memoryStats: ecsState.memoryStats,
    systemPerformance: systemState.executionStats,
    errorStats: systemState.errorStats
  }
}

/**
 * 设置引擎配置
 */
export const configureEngine = (config: {
  targetFPS?: number
  maxDeltaSeconds?: number
  performanceProfiling?: boolean
  debugMode?: boolean
  verboseLogging?: boolean
}) => {
  const ecsState = getMutableState(ECSState)
  const systemState = getMutableState(SystemState)
  const engineState = getMutableState(EngineState)
  
  if (config.targetFPS !== undefined) {
    ecsState.setTargetFPS(config.targetFPS)
  }
  
  if (config.maxDeltaSeconds !== undefined) {
    ecsState.maxDeltaSeconds.set(config.maxDeltaSeconds)
  }
  
  if (config.performanceProfiling !== undefined) {
    ecsState.setPerformanceProfiling(config.performanceProfiling)
    systemState.setPerformanceProfiling(config.performanceProfiling)
  }
  
  if (config.debugMode !== undefined) {
    systemState.setDebugMode(config.debugMode)
    engineState.debugMode.set(config.debugMode)
  }
  
  if (config.verboseLogging !== undefined) {
    systemState.setVerboseLogging(config.verboseLogging)
    engineState.verboseLogging.set(config.verboseLogging)
  }
}

/**
 * 引擎工具函数
 */
export const EngineUtils = {
  /**
   * 检查引擎是否正在运行
   */
  isRunning: (): boolean => {
    return getState(EngineState).isRunning
  },
  
  /**
   * 检查引擎是否暂停
   */
  isPaused: (): boolean => {
    return getState(ECSState).isPaused
  },
  
  /**
   * 获取当前FPS
   */
  getCurrentFPS: (): number => {
    return getState(ECSState).actualFPS
  },
  
  /**
   * 获取帧时间
   */
  getFrameTime: (): number => {
    return getState(ECSState).deltaSeconds * 1000
  },
  
  /**
   * 获取运行时间
   */
  getUptime: (): number => {
    return getState(EngineState).uptime
  },
  
  /**
   * 格式化性能信息
   */
  formatPerformanceInfo: () => {
    const perf = getEnginePerformance()
    return {
      fps: `${perf.fps.toFixed(1)} FPS`,
      frameTime: `${perf.frameTime.toFixed(2)}ms`,
      memory: `${(perf.memoryStats.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
      systems: `${perf.systemStats.systemCount} systems`,
      avgSystemTime: `${perf.systemStats.averageExecutionTime.toFixed(2)}ms`
    }
  }
}
