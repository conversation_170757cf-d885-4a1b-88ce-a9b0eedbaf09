{"name": "@dl-engine/engine-core", "version": "1.0.0", "description": "DL-Engine 核心渲染引擎 - Three.js集成、资产加载、动画系统", "main": "index.ts", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "test": "vitest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"three": "0.176.0", "@types/three": "0.176.0", "three-stdlib": "^2.32.2", "postprocessing": "^6.36.0", "@dl-engine/engine-ecs": "workspace:*", "@dl-engine/engine-state": "workspace:*"}, "devDependencies": {"@types/node": "^22.0.0", "typescript": "5.6.3", "vite": "5.4.8", "vitest": "^2.0.0", "eslint": "^9.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "engines": {"node": ">=22.0.0"}}