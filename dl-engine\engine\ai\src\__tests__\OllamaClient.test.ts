/**
 * DL-Engine Ollama客户端测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { OllamaClient } from '../ollama/OllamaClient'
import axios from 'axios'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

const mockAxiosInstance = {
  get: vi.fn(),
  post: vi.fn(),
  delete: vi.fn(),
  interceptors: {
    request: {
      use: vi.fn()
    },
    response: {
      use: vi.fn()
    }
  }
}

describe('OllamaClient', () => {
  let ollamaClient: OllamaClient
  
  beforeEach(() => {
    // 设置axios.create mock
    mockedAxios.create.mockReturnValue(mockAxiosInstance as any)
    
    ollamaClient = OllamaClient.getInstance()
    
    // 重置所有mock
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    // 清理
  })
  
  describe('Initialization', () => {
    it('should create axios instance with correct config', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:11434',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    })
    
    it('should setup request and response interceptors', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled()
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled()
    })
  })
  
  describe('Connection Management', () => {
    it('should check connection successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: { models: [] } })
      
      const isConnected = await ollamaClient.checkConnection()
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/tags')
      expect(isConnected).toBe(true)
    })
    
    it('should handle connection failure', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection failed'))
      
      const isConnected = await ollamaClient.checkConnection()
      
      expect(isConnected).toBe(false)
    })
    
    it('should update configuration', () => {
      const newConfig = {
        baseURL: 'http://custom-host:11434',
        timeout: 60000
      }
      
      ollamaClient.updateConfig(newConfig)
      
      // 验证配置更新（在实际实现中会更新axios实例）
      expect(() => ollamaClient.updateConfig(newConfig)).not.toThrow()
    })
  })
  
  describe('Model Management', () => {
    it('should get available models', async () => {
      const mockModels = [
        {
          name: 'llama2',
          size: 3800000000,
          modified_at: '2023-01-01T00:00:00Z'
        },
        {
          name: 'codellama',
          size: 7000000000,
          modified_at: '2023-01-02T00:00:00Z'
        }
      ]
      
      mockAxiosInstance.get.mockResolvedValue({ data: { models: mockModels } })
      
      const models = await ollamaClient.getModels()
      
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/tags')
      expect(models).toEqual(mockModels)
    })
    
    it('should handle get models error', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Failed to get models'))
      
      await expect(ollamaClient.getModels()).rejects.toThrow('Failed to get models')
    })
    
    it('should pull model', async () => {
      mockAxiosInstance.post.mockResolvedValue({ data: {} })
      
      await ollamaClient.pullModel('llama2')
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/pull', 
        { name: 'llama2' }, 
        { responseType: 'stream' }
      )
    })
    
    it('should delete model', async () => {
      mockAxiosInstance.delete.mockResolvedValue({ data: {} })
      
      await ollamaClient.deleteModel('llama2')
      
      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/api/delete', 
        { data: { name: 'llama2' } }
      )
    })
  })
  
  describe('Text Generation', () => {
    it('should generate text successfully', async () => {
      const mockResponse = {
        model: 'llama2',
        created_at: '2023-01-01T00:00:00Z',
        response: 'Generated text response',
        done: true,
        total_duration: 1000000000,
        eval_count: 50
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const request = {
        model: 'llama2',
        prompt: 'Hello, how are you?',
        stream: false
      }
      
      const response = await ollamaClient.generate(request)
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/generate', request)
      expect(response).toEqual(mockResponse)
    })
    
    it('should generate text with options', async () => {
      const mockResponse = {
        model: 'llama2',
        created_at: '2023-01-01T00:00:00Z',
        response: 'Generated text with options',
        done: true
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const request = {
        model: 'llama2',
        prompt: 'Explain quantum physics',
        options: {
          temperature: 0.8,
          top_p: 0.9,
          num_predict: 100
        }
      }
      
      const response = await ollamaClient.generate(request)
      
      expect(response).toEqual(mockResponse)
    })
    
    it('should handle generation errors', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Generation failed'))
      
      const request = {
        model: 'llama2',
        prompt: 'Test prompt'
      }
      
      await expect(ollamaClient.generate(request)).rejects.toThrow('Generation failed')
    })
  })
  
  describe('Chat Functionality', () => {
    it('should handle chat conversation', async () => {
      const mockResponse = {
        model: 'llama2',
        created_at: '2023-01-01T00:00:00Z',
        message: {
          role: 'assistant',
          content: 'Hello! How can I help you today?'
        },
        done: true
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const request = {
        model: 'llama2',
        messages: [
          { role: 'user' as const, content: 'Hello' }
        ]
      }
      
      const response = await ollamaClient.chat(request)
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/chat', request)
      expect(response).toEqual(mockResponse)
    })
    
    it('should handle multi-turn conversation', async () => {
      const mockResponse = {
        model: 'llama2',
        created_at: '2023-01-01T00:00:00Z',
        message: {
          role: 'assistant',
          content: 'The capital of France is Paris.'
        },
        done: true
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const request = {
        model: 'llama2',
        messages: [
          { role: 'user' as const, content: 'Hello' },
          { role: 'assistant' as const, content: 'Hi there!' },
          { role: 'user' as const, content: 'What is the capital of France?' }
        ]
      }
      
      const response = await ollamaClient.chat(request)
      
      expect(response.message.content).toContain('Paris')
    })
  })
  
  describe('Embeddings', () => {
    it('should generate embeddings', async () => {
      const mockResponse = {
        embedding: [0.1, 0.2, 0.3, -0.1, -0.2]
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const request = {
        model: 'llama2',
        prompt: 'This is a test sentence'
      }
      
      const response = await ollamaClient.embeddings(request)
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/embeddings', request)
      expect(response).toEqual(mockResponse)
      expect(Array.isArray(response.embedding)).toBe(true)
    })
    
    it('should handle embeddings errors', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Embeddings failed'))
      
      const request = {
        model: 'llama2',
        prompt: 'Test text'
      }
      
      await expect(ollamaClient.embeddings(request)).rejects.toThrow('Embeddings failed')
    })
  })
  
  describe('Statistics', () => {
    it('should track request statistics', async () => {
      const mockResponse = {
        model: 'llama2',
        response: 'Test response',
        done: true,
        eval_count: 25
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const initialStats = ollamaClient.getStats()
      
      await ollamaClient.generate({
        model: 'llama2',
        prompt: 'Test prompt'
      })
      
      const updatedStats = ollamaClient.getStats()
      
      expect(updatedStats.totalRequests).toBe(initialStats.totalRequests + 1)
      expect(updatedStats.successfulRequests).toBe(initialStats.successfulRequests + 1)
      expect(updatedStats.totalTokens).toBe(initialStats.totalTokens + 25)
    })
    
    it('should track failed requests', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Request failed'))
      
      const initialStats = ollamaClient.getStats()
      
      try {
        await ollamaClient.generate({
          model: 'llama2',
          prompt: 'Test prompt'
        })
      } catch (error) {
        // 预期的错误
      }
      
      const updatedStats = ollamaClient.getStats()
      
      expect(updatedStats.totalRequests).toBe(initialStats.totalRequests + 1)
      expect(updatedStats.failedRequests).toBe(initialStats.failedRequests + 1)
    })
    
    it('should calculate average response time', async () => {
      const mockResponse = {
        model: 'llama2',
        response: 'Test response',
        done: true
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      // 执行多个请求
      for (let i = 0; i < 3; i++) {
        await ollamaClient.generate({
          model: 'llama2',
          prompt: `Test prompt ${i}`
        })
      }
      
      const stats = ollamaClient.getStats()
      
      expect(stats.averageResponseTime).toBeGreaterThan(0)
      expect(stats.totalRequests).toBe(3)
    })
  })
  
  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Network error'))
      
      await expect(ollamaClient.generate({
        model: 'llama2',
        prompt: 'Test'
      })).rejects.toThrow('Network error')
    })
    
    it('should handle timeout errors', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Timeout'))
      
      await expect(ollamaClient.generate({
        model: 'llama2',
        prompt: 'Test'
      })).rejects.toThrow('Timeout')
    })
    
    it('should handle invalid model errors', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Model not found'))
      
      await expect(ollamaClient.generate({
        model: 'nonexistent-model',
        prompt: 'Test'
      })).rejects.toThrow('Model not found')
    })
  })
  
  describe('Singleton Pattern', () => {
    it('should return same instance', () => {
      const instance1 = OllamaClient.getInstance()
      const instance2 = OllamaClient.getInstance()
      
      expect(instance1).toBe(instance2)
    })
    
    it('should maintain state across getInstance calls', () => {
      const instance1 = OllamaClient.getInstance()
      instance1.updateConfig({ timeout: 45000 })
      
      const instance2 = OllamaClient.getInstance()
      
      // 两个实例应该是同一个对象
      expect(instance1).toBe(instance2)
    })
  })
  
  describe('Performance', () => {
    it('should handle concurrent requests', async () => {
      const mockResponse = {
        model: 'llama2',
        response: 'Concurrent response',
        done: true
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const promises = []
      for (let i = 0; i < 5; i++) {
        promises.push(ollamaClient.generate({
          model: 'llama2',
          prompt: `Concurrent prompt ${i}`
        }))
      }
      
      const responses = await Promise.all(promises)
      
      expect(responses).toHaveLength(5)
      responses.forEach(response => {
        expect(response.response).toBe('Concurrent response')
      })
    })
    
    it('should handle large prompts efficiently', async () => {
      const largePrompt = 'A'.repeat(10000) // 10KB prompt
      
      const mockResponse = {
        model: 'llama2',
        response: 'Response to large prompt',
        done: true
      }
      
      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })
      
      const startTime = performance.now()
      
      await ollamaClient.generate({
        model: 'llama2',
        prompt: largePrompt
      })
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // 请求处理应该很快（不包括实际的AI推理时间）
      expect(duration).toBeLessThan(100)
    })
  })
})
