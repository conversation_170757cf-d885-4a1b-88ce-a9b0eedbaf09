/**
 * DL-Engine XR工具函数
 */

import { Vector3, Quaternion, Matrix4 } from 'three'

/**
 * 将XR姿态转换为Three.js对象
 */
export function xrPoseToThree(pose: XRPose): { position: Vector3; rotation: Quaternion } {
  const position = new Vector3(
    pose.transform.position.x,
    pose.transform.position.y,
    pose.transform.position.z
  )
  
  const rotation = new Quaternion(
    pose.transform.orientation.x,
    pose.transform.orientation.y,
    pose.transform.orientation.z,
    pose.transform.orientation.w
  )
  
  return { position, rotation }
}

/**
 * 将Three.js变换转换为XR变换
 */
export function threeToXRTransform(position: Vector3, rotation: Quaternion): XRTransform {
  return {
    position: {
      x: position.x,
      y: position.y,
      z: position.z
    },
    orientation: {
      x: rotation.x,
      y: rotation.y,
      z: rotation.z,
      w: rotation.w
    }
  }
}

/**
 * 检查WebXR功能支持
 */
export async function checkXRSupport(): Promise<{
  webxr: boolean
  immersiveVR: boolean
  immersiveAR: boolean
  inline: boolean
}> {
  const result = {
    webxr: false,
    immersiveVR: false,
    immersiveAR: false,
    inline: false
  }
  
  if (!navigator.xr) {
    return result
  }
  
  result.webxr = true
  
  try {
    result.immersiveVR = await navigator.xr.isSessionSupported('immersive-vr')
    result.immersiveAR = await navigator.xr.isSessionSupported('immersive-ar')
    result.inline = await navigator.xr.isSessionSupported('inline')
  } catch (error) {
    console.warn('Error checking XR session support:', error)
  }
  
  return result
}

/**
 * 创建XR会话选项
 */
export function createXRSessionOptions(features: {
  handTracking?: boolean
  eyeTracking?: boolean
  planeDetection?: boolean
  meshDetection?: boolean
  lightEstimation?: boolean
}): XRSessionInit {
  const optionalFeatures: string[] = []
  const requiredFeatures: string[] = []
  
  if (features.handTracking) {
    optionalFeatures.push('hand-tracking')
  }
  
  if (features.eyeTracking) {
    optionalFeatures.push('eye-tracking')
  }
  
  if (features.planeDetection) {
    optionalFeatures.push('plane-detection')
  }
  
  if (features.meshDetection) {
    optionalFeatures.push('mesh-detection')
  }
  
  if (features.lightEstimation) {
    optionalFeatures.push('light-estimation')
  }
  
  return {
    optionalFeatures,
    requiredFeatures
  }
}

/**
 * 计算控制器射线与平面的交点
 */
export function rayPlaneIntersection(
  rayOrigin: Vector3,
  rayDirection: Vector3,
  planePoint: Vector3,
  planeNormal: Vector3
): Vector3 | null {
  const denominator = planeNormal.dot(rayDirection)
  
  if (Math.abs(denominator) < 0.0001) {
    return null // 射线与平面平行
  }
  
  const t = planeNormal.dot(planePoint.clone().sub(rayOrigin)) / denominator
  
  if (t < 0) {
    return null // 交点在射线起点后面
  }
  
  return rayOrigin.clone().add(rayDirection.clone().multiplyScalar(t))
}

/**
 * 获取手势识别结果
 */
export function recognizeHandGesture(hand: XRHand): string | null {
  // TODO: 实现手势识别算法
  // 这里可以实现各种手势识别，如：
  // - 握拳
  // - 指向
  // - 抓取
  // - 放开
  // - 点赞
  // - OK手势
  // 等等
  
  return null
}

/**
 * 计算两个XR空间之间的变换
 */
export function getSpaceTransform(
  frame: XRFrame,
  fromSpace: XRSpace,
  toSpace: XRSpace
): Matrix4 | null {
  const pose = frame.getPose(fromSpace, toSpace)
  if (!pose) {
    return null
  }
  
  const matrix = new Matrix4()
  const position = new Vector3(
    pose.transform.position.x,
    pose.transform.position.y,
    pose.transform.position.z
  )
  const rotation = new Quaternion(
    pose.transform.orientation.x,
    pose.transform.orientation.y,
    pose.transform.orientation.z,
    pose.transform.orientation.w
  )
  
  matrix.compose(position, rotation, new Vector3(1, 1, 1))
  return matrix
}
