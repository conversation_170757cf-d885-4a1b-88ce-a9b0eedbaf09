/**
 * DL-Engine 全量构建脚本
 * 按依赖顺序构建所有模块
 */

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import { join } from 'path'

interface BuildResult {
  module: string
  success: boolean
  duration: number
  error?: string
}

interface BuildSummary {
  totalModules: number
  successfulBuilds: number
  failedBuilds: number
  totalDuration: number
  results: BuildResult[]
}

// 按依赖顺序排列的模块列表
const buildOrder = [
  'shared/common',      // 最基础的共享模块
  'engine/state',       // 状态管理，被其他模块依赖
  'engine/ecs',         // ECS系统，核心架构
  'engine/physics',     // 物理引擎
  'engine/core',        // 核心渲染引擎，依赖ECS和状态管理
  'engine/xr',          // XR模块，依赖核心模块
  'engine/ai'           // AI模块，依赖状态管理
]

/**
 * 构建单个模块
 */
async function buildModule(modulePath: string): Promise<BuildResult> {
  const fullPath = join(process.cwd(), modulePath)
  const packageJsonPath = join(fullPath, 'package.json')
  
  if (!existsSync(packageJsonPath)) {
    return {
      module: modulePath,
      success: false,
      duration: 0,
      error: 'Module not found or no package.json'
    }
  }
  
  console.log(`\n🔨 Building ${modulePath}...`)
  
  const startTime = Date.now()
  
  try {
    // 首先安装依赖（如果需要）
    if (existsSync(join(fullPath, 'node_modules'))) {
      console.log(`📦 Dependencies already installed for ${modulePath}`)
    } else {
      console.log(`📦 Installing dependencies for ${modulePath}...`)
      execSync('npm install', {
        cwd: fullPath,
        stdio: 'pipe'
      })
    }
    
    // 运行类型检查
    console.log(`🔍 Type checking ${modulePath}...`)
    execSync('npm run type-check', {
      cwd: fullPath,
      stdio: 'pipe'
    })
    
    // 运行构建
    console.log(`⚙️  Building ${modulePath}...`)
    execSync('npm run build', {
      cwd: fullPath,
      stdio: 'pipe'
    })
    
    const duration = Date.now() - startTime
    
    console.log(`✅ ${modulePath} built successfully (${duration}ms)`)
    
    return {
      module: modulePath,
      success: true,
      duration
    }
    
  } catch (error: any) {
    const duration = Date.now() - startTime
    
    console.log(`❌ ${modulePath} build failed (${duration}ms)`)
    console.log(`Error: ${error.message}`)
    
    return {
      module: modulePath,
      success: false,
      duration,
      error: error.message
    }
  }
}

/**
 * 清理所有模块的构建产物
 */
async function cleanAll(): Promise<void> {
  console.log('🧹 Cleaning all build artifacts...')
  
  for (const module of buildOrder) {
    const fullPath = join(process.cwd(), module)
    const distPath = join(fullPath, 'dist')
    
    if (existsSync(distPath)) {
      try {
        execSync('rm -rf dist', { cwd: fullPath })
        console.log(`🗑️  Cleaned ${module}/dist`)
      } catch (error) {
        console.log(`⚠️  Failed to clean ${module}/dist`)
      }
    }
  }
}

/**
 * 验证构建产物
 */
function validateBuild(modulePath: string): boolean {
  const fullPath = join(process.cwd(), modulePath)
  const distPath = join(fullPath, 'dist')
  
  if (!existsSync(distPath)) {
    return false
  }
  
  // 检查是否有.js文件
  try {
    const files = execSync('find dist -name "*.js" | head -1', {
      cwd: fullPath,
      encoding: 'utf8'
    }).trim()
    
    return files.length > 0
  } catch {
    return false
  }
}

/**
 * 生成构建报告
 */
function generateReport(summary: BuildSummary): void {
  console.log('\n' + '='.repeat(60))
  console.log('🏗️  DL-Engine Build Summary')
  console.log('='.repeat(60))
  
  console.log(`📦 Total Modules: ${summary.totalModules}`)
  console.log(`✅ Successful: ${summary.successfulBuilds}`)
  console.log(`❌ Failed: ${summary.failedBuilds}`)
  console.log(`⏱️  Total Duration: ${(summary.totalDuration / 1000).toFixed(1)}s`)
  
  console.log('\n📋 Detailed Results:')
  console.log('-'.repeat(60))
  
  summary.results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    const duration = (result.duration / 1000).toFixed(1)
    
    console.log(`${status} ${result.module} - ${duration}s`)
    
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
  })
  
  console.log('\n' + '='.repeat(60))
  
  if (summary.failedBuilds === 0) {
    console.log('🎉 All modules built successfully!')
    console.log('📦 Build artifacts are ready in each module\'s dist/ directory')
  } else {
    console.log(`⚠️  ${summary.failedBuilds} module(s) failed to build`)
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)
  const shouldClean = args.includes('--clean')
  const skipValidation = args.includes('--skip-validation')
  
  console.log('🚀 Starting DL-Engine build process...')
  console.log(`Building ${buildOrder.length} modules in dependency order`)
  
  if (shouldClean) {
    await cleanAll()
  }
  
  const startTime = Date.now()
  const results: BuildResult[] = []
  
  // 按依赖顺序构建所有模块
  for (const module of buildOrder) {
    const result = await buildModule(module)
    results.push(result)
    
    // 如果构建失败，停止后续构建
    if (!result.success) {
      console.log(`\n💥 Build failed for ${module}, stopping build process`)
      break
    }
    
    // 验证构建产物
    if (!skipValidation && !validateBuild(module)) {
      console.log(`\n⚠️  Build validation failed for ${module}`)
      result.success = false
      result.error = 'Build validation failed - no output files found'
      break
    }
  }
  
  const totalDuration = Date.now() - startTime
  const successfulBuilds = results.filter(r => r.success).length
  const failedBuilds = results.filter(r => !r.success).length
  
  const summary: BuildSummary = {
    totalModules: buildOrder.length,
    successfulBuilds,
    failedBuilds,
    totalDuration,
    results
  }
  
  // 生成报告
  generateReport(summary)
  
  // 设置退出码
  process.exit(failedBuilds > 0 ? 1 : 0)
}

/**
 * 构建单个指定模块
 */
async function buildSingle(moduleName: string): Promise<void> {
  if (!buildOrder.includes(moduleName)) {
    console.error(`❌ Unknown module: ${moduleName}`)
    console.log(`Available modules: ${buildOrder.join(', ')}`)
    process.exit(1)
  }
  
  console.log(`🚀 Building single module: ${moduleName}`)
  
  const result = await buildModule(moduleName)
  
  if (result.success) {
    console.log(`\n✅ ${moduleName} built successfully!`)
    
    if (!validateBuild(moduleName)) {
      console.log(`⚠️  Warning: Build validation failed for ${moduleName}`)
    }
  } else {
    console.log(`\n❌ ${moduleName} build failed!`)
    if (result.error) {
      console.log(`Error: ${result.error}`)
    }
    process.exit(1)
  }
}

// 处理命令行参数
if (require.main === module) {
  const args = process.argv.slice(2)
  const moduleArg = args.find(arg => arg.startsWith('--module='))
  
  if (moduleArg) {
    const moduleName = moduleArg.split('=')[1]
    buildSingle(moduleName).catch(error => {
      console.error('Build failed:', error)
      process.exit(1)
    })
  } else {
    main().catch(error => {
      console.error('Build process failed:', error)
      process.exit(1)
    })
  }
}

export { main as buildAll, buildSingle }
