/**
 * DL-Engine XR类型定义
 */

export interface XRVector3 {
  x: number
  y: number
  z: number
}

export interface XRQuaternion {
  x: number
  y: number
  z: number
  w: number
}

export interface XRTransform {
  position: XRVector3
  orientation: XRQuaternion
}

export interface XRPose {
  transform: XRTransform
  emulatedPosition: boolean
}

export interface XRViewport {
  x: number
  y: number
  width: number
  height: number
}

export interface XRView {
  eye: 'left' | 'right' | 'none'
  projectionMatrix: Float32Array
  transform: XRTransform
  viewport?: XRViewport
}

export interface XRFrame {
  session: XRSession
  getPose(space: XRSpace, baseSpace: XRSpace): XRPose | null
  getViewerPose(referenceSpace: XRReferenceSpace): XRViewerPose | null
}

export interface XRViewerPose extends XRPose {
  views: XRView[]
}

export interface XRSpace {
  // XR空间基础接口
}

export interface XRReferenceSpace extends XRSpace {
  getOffsetReferenceSpace(originOffset: XRRigidTransform): XRReferenceSpace
}

export interface XRRigidTransform {
  position: DOMPointReadOnly
  orientation: DOMPointReadOnly
  matrix: Float32Array
  inverse: XRRigidTransform
}

export interface XRInputSource {
  handedness: 'none' | 'left' | 'right'
  targetRayMode: 'gaze' | 'tracked-pointer' | 'screen'
  targetRaySpace: XRSpace
  gripSpace?: XRSpace
  gamepad?: Gamepad
  profiles: string[]
  hand?: XRHand
}

export interface XRHand extends Map<string, XRJointSpace> {
  readonly WRIST: number
  readonly THUMB_METACARPAL: number
  readonly THUMB_PHALANX_PROXIMAL: number
  readonly THUMB_PHALANX_DISTAL: number
  readonly THUMB_TIP: number
  readonly INDEX_FINGER_METACARPAL: number
  readonly INDEX_FINGER_PHALANX_PROXIMAL: number
  readonly INDEX_FINGER_PHALANX_INTERMEDIATE: number
  readonly INDEX_FINGER_PHALANX_DISTAL: number
  readonly INDEX_FINGER_TIP: number
  readonly MIDDLE_FINGER_METACARPAL: number
  readonly MIDDLE_FINGER_PHALANX_PROXIMAL: number
  readonly MIDDLE_FINGER_PHALANX_INTERMEDIATE: number
  readonly MIDDLE_FINGER_PHALANX_DISTAL: number
  readonly MIDDLE_FINGER_TIP: number
  readonly RING_FINGER_METACARPAL: number
  readonly RING_FINGER_PHALANX_PROXIMAL: number
  readonly RING_FINGER_PHALANX_INTERMEDIATE: number
  readonly RING_FINGER_PHALANX_DISTAL: number
  readonly RING_FINGER_TIP: number
  readonly PINKY_FINGER_METACARPAL: number
  readonly PINKY_FINGER_PHALANX_PROXIMAL: number
  readonly PINKY_FINGER_PHALANX_INTERMEDIATE: number
  readonly PINKY_FINGER_PHALANX_DISTAL: number
  readonly PINKY_FINGER_TIP: number
}

export interface XRJointSpace extends XRSpace {
  readonly jointName: string
}

export interface XRSession extends EventTarget {
  readonly inputSources: ReadonlyArray<XRInputSource>
  readonly visibilityState: 'visible' | 'visible-blurred' | 'hidden'
  readonly frameRate?: number
  readonly supportedFrameRates?: Float32Array
  
  requestReferenceSpace(type: XRReferenceSpaceType): Promise<XRReferenceSpace>
  requestAnimationFrame(callback: XRFrameRequestCallback): number
  cancelAnimationFrame(handle: number): void
  end(): Promise<void>
  updateRenderState(state?: XRRenderStateInit): void
}

export type XRReferenceSpaceType = 
  | 'viewer' 
  | 'local' 
  | 'local-floor' 
  | 'bounded-floor' 
  | 'unbounded'

export interface XRRenderStateInit {
  depthNear?: number
  depthFar?: number
  inlineVerticalFieldOfView?: number
  baseLayer?: XRWebGLLayer
  layers?: XRLayer[]
}

export interface XRWebGLLayer {
  readonly antialias: boolean
  readonly ignoreDepthValues: boolean
  readonly framebuffer: WebGLFramebuffer | null
  readonly framebufferWidth: number
  readonly framebufferHeight: number
  
  getViewport(view: XRView): XRViewport | null
}

export interface XRLayer {
  // XR图层基础接口
}

export type XRFrameRequestCallback = (time: DOMHighResTimeStamp, frame: XRFrame) => void

export interface XRSessionInit {
  optionalFeatures?: string[]
  requiredFeatures?: string[]
}

export interface XRInputSourceEvent extends Event {
  readonly frame: XRFrame
  readonly inputSource: XRInputSource
}

export interface XRInputSourceChangeEvent extends Event {
  readonly session: XRSession
  readonly added: ReadonlyArray<XRInputSource>
  readonly removed: ReadonlyArray<XRInputSource>
}

export interface XRSessionEvent extends Event {
  readonly session: XRSession
}

// 扩展Navigator接口
declare global {
  interface Navigator {
    xr?: XRSystem
  }
  
  interface XRSystem extends EventTarget {
    isSessionSupported(mode: string): Promise<boolean>
    requestSession(mode: string, options?: XRSessionInit): Promise<XRSession>
  }
}
