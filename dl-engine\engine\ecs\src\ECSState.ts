/**
 * DL-Engine ECS状态管理
 * 管理ECS系统的全局状态
 */

import { defineState } from '@dl-engine/engine-state'
import { Timer } from './Timer'

/**
 * ECS系统状态接口
 */
export interface ECSStateType {
  /** 当前帧时间（毫秒） */
  frameTime: number
  
  /** 已运行时间（秒） */
  elapsedSeconds: number
  
  /** 帧间隔时间（秒） */
  deltaSeconds: number
  
  /** 最大帧间隔时间（秒） */
  maxDeltaSeconds: number
  
  /** 模拟时间（毫秒） */
  simulationTime: number
  
  /** 固定时间步长（秒） */
  fixedDeltaSeconds: number
  
  /** 固定时间累积器 */
  fixedTimeAccumulator: number
  
  /** 目标帧率 */
  targetFPS: number
  
  /** 实际帧率 */
  actualFPS: number
  
  /** 帧计数器 */
  frameCount: number
  
  /** 上次FPS计算时间 */
  lastFPSTime: number
  
  /** 定时器实例 */
  timer: Timer | null
  
  /** 是否暂停 */
  isPaused: boolean
  
  /** 是否启用性能分析 */
  performanceProfilingEnabled: boolean
  
  /** 系统执行统计 */
  systemStats: {
    totalExecutionTime: number
    systemCount: number
    averageExecutionTime: number
  }
  
  /** 内存使用统计 */
  memoryStats: {
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  }
}

/**
 * ECS状态默认值
 */
const ECSStateDefaults: ECSStateType = {
  frameTime: 0,
  elapsedSeconds: 0,
  deltaSeconds: 0,
  maxDeltaSeconds: 1 / 30, // 最大30fps
  simulationTime: 0,
  fixedDeltaSeconds: 1 / 60, // 60fps固定时间步长
  fixedTimeAccumulator: 0,
  targetFPS: 60,
  actualFPS: 0,
  frameCount: 0,
  lastFPSTime: 0,
  timer: null,
  isPaused: false,
  performanceProfilingEnabled: false,
  systemStats: {
    totalExecutionTime: 0,
    systemCount: 0,
    averageExecutionTime: 0
  },
  memoryStats: {
    usedJSHeapSize: 0,
    totalJSHeapSize: 0,
    jsHeapSizeLimit: 0
  }
}

/**
 * ECS状态定义
 */
export const ECSState = defineState({
  name: 'DLEngine.ECS',
  initial: () => ECSStateDefaults,
  
  receptors: {
    /**
     * 设置帧时间
     */
    setFrameTime: (state, frameTime: number) => {
      state.frameTime.set(frameTime)
    },
    
    /**
     * 设置增量时间
     */
    setDeltaTime: (state, deltaSeconds: number) => {
      state.deltaSeconds.set(Math.max(0.001, Math.min(state.maxDeltaSeconds.value, deltaSeconds)))
    },
    
    /**
     * 更新模拟时间
     */
    updateSimulationTime: (state, deltaTime: number) => {
      state.simulationTime.set(state.simulationTime.value + deltaTime * 1000)
    },
    
    /**
     * 设置目标帧率
     */
    setTargetFPS: (state, fps: number) => {
      state.targetFPS.set(Math.max(1, Math.min(240, fps)))
      state.fixedDeltaSeconds.set(1 / fps)
    },
    
    /**
     * 更新实际帧率
     */
    updateActualFPS: (state) => {
      const currentTime = performance.now()
      state.frameCount.set(state.frameCount.value + 1)
      
      if (currentTime - state.lastFPSTime.value >= 1000) {
        const fps = state.frameCount.value / ((currentTime - state.lastFPSTime.value) / 1000)
        state.actualFPS.set(Math.round(fps))
        state.frameCount.set(0)
        state.lastFPSTime.set(currentTime)
      }
    },
    
    /**
     * 设置暂停状态
     */
    setPaused: (state, paused: boolean) => {
      state.isPaused.set(paused)
    },
    
    /**
     * 设置性能分析状态
     */
    setPerformanceProfiling: (state, enabled: boolean) => {
      state.performanceProfilingEnabled.set(enabled)
    },
    
    /**
     * 更新系统统计
     */
    updateSystemStats: (state, executionTime: number) => {
      const stats = state.systemStats
      stats.totalExecutionTime.set(stats.totalExecutionTime.value + executionTime)
      stats.systemCount.set(stats.systemCount.value + 1)
      stats.averageExecutionTime.set(stats.totalExecutionTime.value / stats.systemCount.value)
    },
    
    /**
     * 更新内存统计
     */
    updateMemoryStats: (state) => {
      if (performance.memory) {
        const memory = performance.memory
        state.memoryStats.usedJSHeapSize.set(memory.usedJSHeapSize)
        state.memoryStats.totalJSHeapSize.set(memory.totalJSHeapSize)
        state.memoryStats.jsHeapSizeLimit.set(memory.jsHeapSizeLimit)
      }
    },
    
    /**
     * 重置统计
     */
    resetStats: (state) => {
      state.systemStats.set({
        totalExecutionTime: 0,
        systemCount: 0,
        averageExecutionTime: 0
      })
      state.frameCount.set(0)
      state.lastFPSTime.set(performance.now())
    },
    
    /**
     * 设置定时器
     */
    setTimer: (state, timer: Timer | null) => {
      state.timer.set(timer)
    }
  }
})

/**
 * ECS状态工具函数
 */
export const ECSStateUtils = {
  /**
   * 获取当前帧率
   */
  getCurrentFPS: (): number => {
    return ECSState.actualFPS.value
  },
  
  /**
   * 获取帧时间（毫秒）
   */
  getFrameTime: (): number => {
    return ECSState.frameTime.value
  },
  
  /**
   * 获取增量时间（秒）
   */
  getDeltaTime: (): number => {
    return ECSState.deltaSeconds.value
  },
  
  /**
   * 获取固定增量时间（秒）
   */
  getFixedDeltaTime: (): number => {
    return ECSState.fixedDeltaSeconds.value
  },
  
  /**
   * 检查是否暂停
   */
  isPaused: (): boolean => {
    return ECSState.isPaused.value
  },
  
  /**
   * 获取性能统计
   */
  getPerformanceStats: () => {
    return {
      fps: ECSState.actualFPS.value,
      frameTime: ECSState.deltaSeconds.value * 1000,
      systemStats: ECSState.systemStats.value,
      memoryStats: ECSState.memoryStats.value
    }
  },
  
  /**
   * 格式化内存大小
   */
  formatMemorySize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`
  }
}
