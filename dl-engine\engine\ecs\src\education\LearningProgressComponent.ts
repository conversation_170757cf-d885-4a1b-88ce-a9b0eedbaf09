/**
 * DL-Engine 学习进度组件
 * 跟踪学习者的学习进度和成就
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 学习进度数据
 */
export interface LearningProgressData {
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 章节ID */
  chapterId: string
  
  /** 当前进度百分比 (0-100) */
  progressPercentage: number
  
  /** 完成的任务数量 */
  completedTasks: number
  
  /** 总任务数量 */
  totalTasks: number
  
  /** 学习时长（分钟） */
  studyTimeMinutes: number
  
  /** 开始时间 */
  startTime: Date
  
  /** 最后更新时间 */
  lastUpdateTime: Date
  
  /** 是否完成 */
  isCompleted: boolean
  
  /** 成绩分数 (0-100) */
  score: number
  
  /** 学习状态 */
  status: LearningStatus
  
  /** 学习目标 */
  objectives: LearningObjective[]
  
  /** 学习成就 */
  achievements: Achievement[]
  
  /** 学习笔记 */
  notes: string[]
  
  /** 错误记录 */
  mistakes: LearningMistake[]
  
  /** 学习偏好 */
  preferences: LearningPreferences
}

/**
 * 学习状态枚举
 */
export enum LearningStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REVIEW = 'review'
}

/**
 * 学习目标
 */
export interface LearningObjective {
  id: string
  title: string
  description: string
  isCompleted: boolean
  completionTime?: Date
  difficulty: 'easy' | 'medium' | 'hard'
  category: string
}

/**
 * 成就
 */
export interface Achievement {
  id: string
  name: string
  description: string
  iconUrl?: string
  unlockedAt: Date
  points: number
  category: 'progress' | 'skill' | 'time' | 'social' | 'special'
}

/**
 * 学习错误记录
 */
export interface LearningMistake {
  id: string
  questionId: string
  incorrectAnswer: string
  correctAnswer: string
  timestamp: Date
  attempts: number
  category: string
  difficulty: number
}

/**
 * 学习偏好
 */
export interface LearningPreferences {
  /** 学习风格 */
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'reading'
  
  /** 首选语言 */
  preferredLanguage: string
  
  /** 难度偏好 */
  difficultyPreference: 'adaptive' | 'easy' | 'medium' | 'hard'
  
  /** 学习节奏 */
  pace: 'slow' | 'normal' | 'fast'
  
  /** 是否启用提示 */
  hintsEnabled: boolean
  
  /** 是否启用音效 */
  soundEnabled: boolean
  
  /** 是否启用动画 */
  animationsEnabled: boolean
}

/**
 * 学习进度组件
 */
export const LearningProgressComponent = defineComponent({
  name: 'LearningProgress',
  schema: {
    learnerId: '',
    courseId: '',
    chapterId: '',
    progressPercentage: 0,
    completedTasks: 0,
    totalTasks: 0,
    studyTimeMinutes: 0,
    startTime: new Date(),
    lastUpdateTime: new Date(),
    isCompleted: false,
    score: 0,
    status: LearningStatus.NOT_STARTED,
    objectives: [] as LearningObjective[],
    achievements: [] as Achievement[],
    notes: [] as string[],
    mistakes: [] as LearningMistake[],
    preferences: {
      learningStyle: 'visual',
      preferredLanguage: 'zh-CN',
      difficultyPreference: 'adaptive',
      pace: 'normal',
      hintsEnabled: true,
      soundEnabled: true,
      animationsEnabled: true
    } as LearningPreferences
  } as LearningProgressData,
  
  onAdd: (entity, component) => {
    console.log(`Learning progress started for learner ${component.learnerId} in course ${component.courseId}`)
    component.startTime = new Date()
    component.lastUpdateTime = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Learning progress removed for learner ${component.learnerId}`)
  },
  
  onSet: (entity, component) => {
    component.lastUpdateTime = new Date()
    
    // 自动计算进度百分比
    if (component.totalTasks > 0) {
      component.progressPercentage = Math.round((component.completedTasks / component.totalTasks) * 100)
    }
    
    // 检查是否完成
    if (component.progressPercentage >= 100 && !component.isCompleted) {
      component.isCompleted = true
      component.status = LearningStatus.COMPLETED
      console.log(`Learning completed for learner ${component.learnerId}!`)
    }
  }
})

/**
 * 学习进度工具函数
 */
export const LearningProgressUtils = {
  /**
   * 更新学习进度
   */
  updateProgress: (progress: LearningProgressData, completedTasks: number): void => {
    progress.completedTasks = Math.min(completedTasks, progress.totalTasks)
    progress.progressPercentage = Math.round((progress.completedTasks / progress.totalTasks) * 100)
    progress.lastUpdateTime = new Date()
    
    if (progress.progressPercentage >= 100) {
      progress.isCompleted = true
      progress.status = LearningStatus.COMPLETED
    }
  },
  
  /**
   * 添加学习时间
   */
  addStudyTime: (progress: LearningProgressData, minutes: number): void => {
    progress.studyTimeMinutes += minutes
    progress.lastUpdateTime = new Date()
  },
  
  /**
   * 添加成就
   */
  addAchievement: (progress: LearningProgressData, achievement: Achievement): void => {
    if (!progress.achievements.find(a => a.id === achievement.id)) {
      progress.achievements.push(achievement)
      console.log(`Achievement unlocked: ${achievement.name}`)
    }
  },
  
  /**
   * 添加学习目标
   */
  addObjective: (progress: LearningProgressData, objective: LearningObjective): void => {
    progress.objectives.push(objective)
  },
  
  /**
   * 完成学习目标
   */
  completeObjective: (progress: LearningProgressData, objectiveId: string): void => {
    const objective = progress.objectives.find(o => o.id === objectiveId)
    if (objective && !objective.isCompleted) {
      objective.isCompleted = true
      objective.completionTime = new Date()
    }
  },
  
  /**
   * 记录错误
   */
  recordMistake: (progress: LearningProgressData, mistake: LearningMistake): void => {
    progress.mistakes.push(mistake)
  },
  
  /**
   * 获取学习统计
   */
  getStats: (progress: LearningProgressData) => ({
    progressPercentage: progress.progressPercentage,
    completedObjectives: progress.objectives.filter(o => o.isCompleted).length,
    totalObjectives: progress.objectives.length,
    achievementCount: progress.achievements.length,
    totalStudyTime: progress.studyTimeMinutes,
    averageScore: progress.score,
    mistakeCount: progress.mistakes.length,
    status: progress.status
  })
}
