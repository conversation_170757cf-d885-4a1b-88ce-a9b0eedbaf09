/**
 * DL-Engine AI服务管理器测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  AIServiceManager,
  AIServiceType,
  type AIServiceConfig
} from '../../services/AIServiceManager'

// Mock各种AI服务
const mockOllamaClient = {
  listModels: vi.fn().mockResolvedValue([]),
  generate: vi.fn().mockResolvedValue({ response: 'test response' }),
  chat: vi.fn().mockResolvedValue({ message: { content: 'test chat' } }),
  embed: vi.fn().mockResolvedValue({ embedding: [0.1, 0.2, 0.3] })
}

const mockEducationAssistant = {
  explainConcept: vi.fn().mockResolvedValue('Concept explanation'),
  generateQuestions: vi.fn().mockResolvedValue([]),
  evaluateAnswer: vi.fn().mockResolvedValue({ isCorrect: true, score: 100 }),
  generateLearningRecommendations: vi.fn().mockResolvedValue({
    recommendations: [],
    nextTopics: [],
    studyPlan: ''
  }),
  answerQuestion: vi.fn().mockResolvedValue('Answer')
}

const mockLearningAnalytics = {
  analyzeProgress: vi.fn().mockResolvedValue({}),
  generateReport: vi.fn().mockResolvedValue(''),
  predictPerformance: vi.fn().mockResolvedValue({})
}

const mockTextProcessor = {
  tokenize: vi.fn().mockReturnValue([]),
  extractKeywords: vi.fn().mockReturnValue([]),
  analyzeSentiment: vi.fn().mockReturnValue({ score: 0.5, label: 'neutral' }),
  summarize: vi.fn().mockResolvedValue('')
}

// Mock AI服务类
vi.mock('../../ollama/OllamaClient', () => ({
  OllamaClient: {
    getInstance: vi.fn(() => mockOllamaClient)
  },
  EducationAIAssistant: {
    getInstance: vi.fn(() => mockEducationAssistant)
  }
}))

vi.mock('../../analytics/LearningAnalytics', () => ({
  LearningAnalytics: {
    getInstance: vi.fn(() => mockLearningAnalytics)
  }
}))

vi.mock('../../nlp/TextProcessor', () => ({
  TextProcessor: {
    getInstance: vi.fn(() => mockTextProcessor)
  }
}))

describe('AIServiceManager', () => {
  let aiServiceManager: AIServiceManager
  
  beforeEach(() => {
    aiServiceManager = AIServiceManager.getInstance()
    
    // 重置所有mock
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    // 清理
    aiServiceManager = null as any
  })
  
  describe('Singleton Pattern', () => {
    it('should return same instance', () => {
      const instance1 = AIServiceManager.getInstance()
      const instance2 = AIServiceManager.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })
  
  describe('Initialization', () => {
    it('should initialize with default configuration', async () => {
      await aiServiceManager.initialize()
      
      // 验证所有默认服务都被初始化
      expect(aiServiceManager.isServiceAvailable(AIServiceType.OLLAMA)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.EDUCATION_ASSISTANT)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.LEARNING_ANALYTICS)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.TEXT_PROCESSOR)).toBe(true)
    })
    
    it('should handle Ollama connection failure gracefully', async () => {
      mockOllamaClient.listModels.mockRejectedValueOnce(new Error('Connection failed'))
      
      await aiServiceManager.initialize()
      
      // Ollama服务应该标记为不可用，但其他服务仍然可用
      expect(aiServiceManager.isServiceAvailable(AIServiceType.OLLAMA)).toBe(false)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.EDUCATION_ASSISTANT)).toBe(true)
    })
    
    it('should initialize only enabled services', async () => {
      const customConfig: Partial<AIServiceConfig> = {
        enabledServices: [AIServiceType.EDUCATION_ASSISTANT, AIServiceType.TEXT_PROCESSOR]
      }
      
      aiServiceManager.updateConfig(customConfig)
      await aiServiceManager.initialize()
      
      expect(aiServiceManager.isServiceAvailable(AIServiceType.EDUCATION_ASSISTANT)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.TEXT_PROCESSOR)).toBe(true)
    })
  })
  
  describe('Service Access', () => {
    beforeEach(async () => {
      await aiServiceManager.initialize()
    })
    
    it('should provide access to Ollama client', () => {
      const ollamaClient = aiServiceManager.getOllamaClient()
      
      expect(ollamaClient).toBe(mockOllamaClient)
    })
    
    it('should provide access to education assistant', () => {
      const educationAssistant = aiServiceManager.getEducationAssistant()
      
      expect(educationAssistant).toBe(mockEducationAssistant)
    })
    
    it('should provide access to learning analytics', () => {
      const learningAnalytics = aiServiceManager.getLearningAnalytics()
      
      expect(learningAnalytics).toBe(mockLearningAnalytics)
    })
    
    it('should provide access to text processor', () => {
      const textProcessor = aiServiceManager.getTextProcessor()
      
      expect(textProcessor).toBe(mockTextProcessor)
    })
    
    it('should return null for disabled services', () => {
      const customConfig: Partial<AIServiceConfig> = {
        enabledServices: [AIServiceType.EDUCATION_ASSISTANT]
      }
      
      aiServiceManager.updateConfig(customConfig)
      
      // 这些服务应该返回null，因为它们没有被启用
      expect(aiServiceManager.getOllamaClient()).toBeNull()
      expect(aiServiceManager.getLearningAnalytics()).toBeNull()
      expect(aiServiceManager.getTextProcessor()).toBeNull()
    })
  })
  
  describe('Service Status', () => {
    beforeEach(async () => {
      await aiServiceManager.initialize()
    })
    
    it('should report service availability correctly', () => {
      expect(aiServiceManager.isServiceAvailable(AIServiceType.OLLAMA)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.EDUCATION_ASSISTANT)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.LEARNING_ANALYTICS)).toBe(true)
      expect(aiServiceManager.isServiceAvailable(AIServiceType.TEXT_PROCESSOR)).toBe(true)
    })
    
    it('should return all service status', () => {
      const allStatus = aiServiceManager.getAllServiceStatus()
      
      expect(allStatus).toHaveLength(4)
      
      const ollamaStatus = allStatus.find(s => s.type === AIServiceType.OLLAMA)
      expect(ollamaStatus).toBeDefined()
      expect(ollamaStatus?.enabled).toBe(true)
      expect(ollamaStatus?.connected).toBe(true)
    })
  })
  
  describe('Configuration Management', () => {
    it('should update configuration', () => {
      const newConfig: Partial<AIServiceConfig> = {
        defaultLanguage: 'en-US',
        defaultModel: 'llama3.2:7b',
        responseTimeout: 60000
      }
      
      aiServiceManager.updateConfig(newConfig)
      
      // 配置应该被更新（这里我们无法直接验证，但可以通过行为验证）
      expect(() => aiServiceManager.updateConfig(newConfig)).not.toThrow()
    })
  })
  
  describe('Caching System', () => {
    beforeEach(async () => {
      await aiServiceManager.initialize()
    })
    
    it('should set and get cache', () => {
      const testData = { result: 'test data' }
      const cacheKey = 'test_key'
      
      aiServiceManager.setCache(cacheKey, testData)
      const retrieved = aiServiceManager.getCache(cacheKey)
      
      expect(retrieved).toEqual(testData)
    })
    
    it('should return null for non-existent cache', () => {
      const retrieved = aiServiceManager.getCache('non_existent_key')
      
      expect(retrieved).toBeNull()
    })
    
    it('should handle cache expiration', () => {
      // 设置一个很短的过期时间
      aiServiceManager.updateConfig({ cacheExpiration: 0.001 })  // 0.001分钟 = 0.06秒
      
      const testData = { result: 'test data' }
      const cacheKey = 'expiring_key'
      
      aiServiceManager.setCache(cacheKey, testData)
      
      // 等待缓存过期
      return new Promise(resolve => {
        setTimeout(() => {
          const retrieved = aiServiceManager.getCache(cacheKey)
          expect(retrieved).toBeNull()
          resolve(undefined)
        }, 100)  // 等待100ms
      })
    })
    
    it('should respect cache disabled setting', () => {
      aiServiceManager.updateConfig({ enableCache: false })
      
      const testData = { result: 'test data' }
      const cacheKey = 'disabled_cache_key'
      
      aiServiceManager.setCache(cacheKey, testData)
      const retrieved = aiServiceManager.getCache(cacheKey)
      
      // 当缓存被禁用时，应该返回null
      expect(retrieved).toBeNull()
    })
  })
  
  describe('Error Handling', () => {
    it('should handle service initialization errors gracefully', async () => {
      // 模拟所有服务初始化失败
      mockOllamaClient.listModels.mockRejectedValue(new Error('Ollama failed'))
      
      // 应该不会抛出错误
      await expect(aiServiceManager.initialize()).resolves.not.toThrow()
      
      // 但服务应该标记为不可用
      expect(aiServiceManager.isServiceAvailable(AIServiceType.OLLAMA)).toBe(false)
    })
    
    it('should handle unknown service types gracefully', () => {
      const unknownService = aiServiceManager.getService('unknown_service' as AIServiceType)
      
      expect(unknownService).toBeNull()
    })
  })
  
  describe('Performance', () => {
    beforeEach(async () => {
      await aiServiceManager.initialize()
    })
    
    it('should initialize services efficiently', async () => {
      const startTime = performance.now()
      
      // 重新初始化
      await aiServiceManager.initialize()
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // 初始化应该在合理时间内完成
      expect(duration).toBeLessThan(1000)  // 1秒内完成
    })
    
    it('should handle multiple concurrent service calls', async () => {
      const promises = []
      
      // 并发调用多个服务
      for (let i = 0; i < 10; i++) {
        promises.push(
          Promise.resolve(aiServiceManager.getEducationAssistant()?.explainConcept('test concept'))
        )
      }
      
      const results = await Promise.all(promises)
      
      // 所有调用都应该成功
      expect(results).toHaveLength(10)
      results.forEach(result => {
        expect(result).toBe('Concept explanation')
      })
    })
  })
  
  describe('Integration Tests', () => {
    beforeEach(async () => {
      await aiServiceManager.initialize()
    })
    
    it('should support complete AI workflow', async () => {
      // 1. 获取教育助手
      const educationAssistant = aiServiceManager.getEducationAssistant()
      expect(educationAssistant).toBeDefined()
      
      // 2. 解释概念
      const explanation = await educationAssistant?.explainConcept('variables', 'beginner')
      expect(explanation).toBe('Concept explanation')
      
      // 3. 生成问题
      const questions = await educationAssistant?.generateQuestions('variables', 'multiple_choice', 3)
      expect(questions).toEqual([])
      
      // 4. 评估答案
      const evaluation = await educationAssistant?.evaluateAnswer(
        'What is a variable?',
        'A container for data',
        'A container for storing data values'
      )
      expect(evaluation?.isCorrect).toBe(true)
      
      // 5. 获取学习建议
      const recommendations = await educationAssistant?.generateLearningRecommendations(
        {
          level: 'beginner',
          strengths: ['logic'],
          weaknesses: ['syntax'],
          interests: ['games'],
          learningStyle: 'visual'
        },
        'variables'
      )
      expect(recommendations).toBeDefined()
    })
  })
})
