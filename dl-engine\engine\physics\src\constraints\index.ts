/**
 * DL-Engine 约束系统模块
 * 导出所有约束相关的类型和功能
 */

// 约束系统
export * from './ConstraintSystem'

// 约束组件
export * from './ConstraintComponent'

// 约束类型常量
export const CONSTRAINT_TYPES = {
  FIXED: 'fixed',
  REVOLUTE: 'revolute',
  PRISMATIC: 'prismatic',
  SPHERICAL: 'spherical',
  ROPE: 'rope',
  SPRING: 'spring',
  DISTANCE: 'distance'
} as const

/**
 * 约束模块初始化
 */
export function initializeConstraintSystem(): void {
  console.log('🔗 Constraint system initialized')
  console.log('Available constraint types:', Object.values(CONSTRAINT_TYPES))
}

/**
 * 约束系统统计
 */
export function getConstraintSystemStats() {
  return {
    availableTypes: Object.keys(CONSTRAINT_TYPES).length,
    types: Object.values(CONSTRAINT_TYPES)
  }
}
