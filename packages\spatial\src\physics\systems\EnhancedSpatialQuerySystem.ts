/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { defineQuery, defineSystem, Entity, getComponent } from '@ir-engine/ecs'
import { SimulationSystemGroup } from '@ir-engine/ecs/src/SystemGroups'
import { Vector3, Box3, <PERSON><PERSON>, <PERSON> } from 'three'

import { Physics } from '../classes/Physics'
import { RigidBodyComponent } from '../components/RigidBodyComponent'
import { ColliderComponent } from '../components/ColliderComponent'
import { SceneComponent } from '../../renderer/components/SceneComponents'
import { TransformComponent } from '../../transform/components/TransformComponent'

// Enhanced spatial query types
export interface SpatialQueryResult {
  entity: Entity
  distance: number
  point: Vector3
  normal?: Vector3
  collider?: any
}

export interface SpatialQueryOptions {
  maxDistance?: number
  maxResults?: number
  includeStatic?: boolean
  includeDynamic?: boolean
  includeKinematic?: boolean
  collisionGroups?: number
  excludeEntities?: Entity[]
}

// Spatial index for fast queries
class SpatialIndex {
  private gridSize: number
  private grid: Map<string, Set<Entity>>
  private entityPositions: Map<Entity, Vector3>

  constructor(gridSize: number = 10) {
    this.gridSize = gridSize
    this.grid = new Map()
    this.entityPositions = new Map()
  }

  private getGridKey(position: Vector3): string {
    const x = Math.floor(position.x / this.gridSize)
    const y = Math.floor(position.y / this.gridSize)
    const z = Math.floor(position.z / this.gridSize)
    return `${x},${y},${z}`
  }

  addEntity(entity: Entity, position: Vector3) {
    const key = this.getGridKey(position)
    
    // Remove from old position if exists
    this.removeEntity(entity)
    
    // Add to new position
    if (!this.grid.has(key)) {
      this.grid.set(key, new Set())
    }
    this.grid.get(key)!.add(entity)
    this.entityPositions.set(entity, position.clone())
  }

  removeEntity(entity: Entity) {
    const oldPosition = this.entityPositions.get(entity)
    if (oldPosition) {
      const oldKey = this.getGridKey(oldPosition)
      const oldGrid = this.grid.get(oldKey)
      if (oldGrid) {
        oldGrid.delete(entity)
        if (oldGrid.size === 0) {
          this.grid.delete(oldKey)
        }
      }
      this.entityPositions.delete(entity)
    }
  }

  queryRadius(center: Vector3, radius: number): Entity[] {
    const results: Entity[] = []
    const radiusSquared = radius * radius
    
    // Calculate grid bounds
    const minX = Math.floor((center.x - radius) / this.gridSize)
    const maxX = Math.floor((center.x + radius) / this.gridSize)
    const minY = Math.floor((center.y - radius) / this.gridSize)
    const maxY = Math.floor((center.y + radius) / this.gridSize)
    const minZ = Math.floor((center.z - radius) / this.gridSize)
    const maxZ = Math.floor((center.z + radius) / this.gridSize)

    // Check all relevant grid cells
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        for (let z = minZ; z <= maxZ; z++) {
          const key = `${x},${y},${z}`
          const entities = this.grid.get(key)
          if (entities) {
            for (const entity of entities) {
              const position = this.entityPositions.get(entity)
              if (position && position.distanceToSquared(center) <= radiusSquared) {
                results.push(entity)
              }
            }
          }
        }
      }
    }

    return results
  }

  queryBox(box: Box3): Entity[] {
    const results: Entity[] = []
    
    // Calculate grid bounds
    const minX = Math.floor(box.min.x / this.gridSize)
    const maxX = Math.floor(box.max.x / this.gridSize)
    const minY = Math.floor(box.min.y / this.gridSize)
    const maxY = Math.floor(box.max.y / this.gridSize)
    const minZ = Math.floor(box.min.z / this.gridSize)
    const maxZ = Math.floor(box.max.z / this.gridSize)

    // Check all relevant grid cells
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        for (let z = minZ; z <= maxZ; z++) {
          const key = `${x},${y},${z}`
          const entities = this.grid.get(key)
          if (entities) {
            for (const entity of entities) {
              const position = this.entityPositions.get(entity)
              if (position && box.containsPoint(position)) {
                results.push(entity)
              }
            }
          }
        }
      }
    }

    return results
  }
}

// Global spatial index
const spatialIndex = new SpatialIndex(10) // 10 unit grid

// Queries
const spatialEntityQuery = defineQuery([TransformComponent, ColliderComponent])
const sceneQuery = defineQuery([SceneComponent])

/**
 * Enhanced raycast with spatial indexing
 */
export const enhancedRaycast = (
  origin: Vector3,
  direction: Vector3,
  options: SpatialQueryOptions = {}
): SpatialQueryResult[] => {
  const {
    maxDistance = 1000,
    maxResults = 10,
    includeStatic = true,
    includeDynamic = true,
    includeKinematic = true,
    excludeEntities = []
  } = options

  const results: SpatialQueryResult[] = []
  const ray = new Ray(origin, direction.normalize())

  // Use physics world raycast for accurate results
  const scenes = sceneQuery()
  for (const sceneEntity of scenes) {
    const world = Physics.getWorld(sceneEntity)
    if (!world) continue

    const hits = Physics.castRay(world, {
      type: 'Closest',
      origin,
      direction,
      maxDistance,
      collisionGroups: options.collisionGroups
    })

    for (const hit of hits) {
      if (!hit.entity || excludeEntities.includes(hit.entity)) continue

      const rigidBody = getComponent(hit.entity, RigidBodyComponent)
      if (rigidBody) {
        // Filter by body type
        if (!includeStatic && rigidBody.type === 'fixed') continue
        if (!includeDynamic && rigidBody.type === 'dynamic') continue
        if (!includeKinematic && rigidBody.type === 'kinematic') continue
      }

      results.push({
        entity: hit.entity,
        distance: hit.distance,
        point: hit.position,
        normal: hit.normal,
        collider: hit.collider
      })

      if (results.length >= maxResults) break
    }
  }

  return results.sort((a, b) => a.distance - b.distance)
}

/**
 * Enhanced sphere query with spatial indexing
 */
export const enhancedSphereQuery = (
  center: Vector3,
  radius: number,
  options: SpatialQueryOptions = {}
): SpatialQueryResult[] => {
  const {
    maxResults = 50,
    includeStatic = true,
    includeDynamic = true,
    includeKinematic = true,
    excludeEntities = []
  } = options

  const results: SpatialQueryResult[] = []
  
  // Use spatial index for initial filtering
  const candidates = spatialIndex.queryRadius(center, radius)
  
  for (const entity of candidates) {
    if (excludeEntities.includes(entity)) continue

    const transform = getComponent(entity, TransformComponent)
    const rigidBody = getComponent(entity, RigidBodyComponent)
    
    if (!transform) continue

    // Filter by body type
    if (rigidBody) {
      if (!includeStatic && rigidBody.type === 'fixed') continue
      if (!includeDynamic && rigidBody.type === 'dynamic') continue
      if (!includeKinematic && rigidBody.type === 'kinematic') continue
    }

    const distance = center.distanceTo(transform.position)
    if (distance <= radius) {
      results.push({
        entity,
        distance,
        point: transform.position.clone()
      })
    }

    if (results.length >= maxResults) break
  }

  return results.sort((a, b) => a.distance - b.distance)
}

/**
 * Enhanced box query with spatial indexing
 */
export const enhancedBoxQuery = (
  box: Box3,
  options: SpatialQueryOptions = {}
): SpatialQueryResult[] => {
  const {
    maxResults = 50,
    includeStatic = true,
    includeDynamic = true,
    includeKinematic = true,
    excludeEntities = []
  } = options

  const results: SpatialQueryResult[] = []
  const center = box.getCenter(new Vector3())
  
  // Use spatial index for initial filtering
  const candidates = spatialIndex.queryBox(box)
  
  for (const entity of candidates) {
    if (excludeEntities.includes(entity)) continue

    const transform = getComponent(entity, TransformComponent)
    const rigidBody = getComponent(entity, RigidBodyComponent)
    
    if (!transform) continue

    // Filter by body type
    if (rigidBody) {
      if (!includeStatic && rigidBody.type === 'fixed') continue
      if (!includeDynamic && rigidBody.type === 'dynamic') continue
      if (!includeKinematic && rigidBody.type === 'kinematic') continue
    }

    if (box.containsPoint(transform.position)) {
      const distance = center.distanceTo(transform.position)
      results.push({
        entity,
        distance,
        point: transform.position.clone()
      })
    }

    if (results.length >= maxResults) break
  }

  return results.sort((a, b) => a.distance - b.distance)
}

const execute = () => {
  // Update spatial index
  const entities = spatialEntityQuery()
  
  for (const entity of entities) {
    const transform = getComponent(entity, TransformComponent)
    if (transform) {
      spatialIndex.addEntity(entity, transform.position)
    }
  }
}

export const EnhancedSpatialQuerySystem = defineSystem({
  uuid: 'ee.engine.EnhancedSpatialQuerySystem',
  insert: { after: SimulationSystemGroup },
  execute
})

export const SpatialQueryFunctions = {
  enhancedRaycast,
  enhancedSphereQuery,
  enhancedBoxQuery,
  spatialIndex
}
