/**
 * DL-Engine 全量测试运行器
 * 运行所有模块的测试并生成报告
 */

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import { join } from 'path'

interface TestResult {
  module: string
  passed: boolean
  duration: number
  coverage?: number
  error?: string
}

interface TestSummary {
  totalModules: number
  passedModules: number
  failedModules: number
  totalDuration: number
  averageCoverage: number
  results: TestResult[]
}

const modules = [
  'engine/ecs',
  'engine/core', 
  'engine/physics',
  'engine/state',
  'engine/xr',
  'engine/ai',
  'shared/common'
]

/**
 * 运行单个模块的测试
 */
async function runModuleTests(modulePath: string): Promise<TestResult> {
  const fullPath = join(process.cwd(), modulePath)
  const packageJsonPath = join(fullPath, 'package.json')
  
  if (!existsSync(packageJsonPath)) {
    return {
      module: modulePath,
      passed: false,
      duration: 0,
      error: 'Module not found or no package.json'
    }
  }
  
  console.log(`\n🧪 Running tests for ${modulePath}...`)
  
  const startTime = Date.now()
  
  try {
    // 运行测试
    const output = execSync('npm run test', {
      cwd: fullPath,
      encoding: 'utf8',
      stdio: 'pipe'
    })
    
    const duration = Date.now() - startTime
    
    // 解析测试输出以获取覆盖率信息
    const coverage = parseCoverage(output)
    
    console.log(`✅ ${modulePath} tests passed (${duration}ms)`)
    if (coverage !== undefined) {
      console.log(`📊 Coverage: ${coverage}%`)
    }
    
    return {
      module: modulePath,
      passed: true,
      duration,
      coverage
    }
    
  } catch (error: any) {
    const duration = Date.now() - startTime
    
    console.log(`❌ ${modulePath} tests failed (${duration}ms)`)
    console.log(`Error: ${error.message}`)
    
    return {
      module: modulePath,
      passed: false,
      duration,
      error: error.message
    }
  }
}

/**
 * 解析测试输出中的覆盖率信息
 */
function parseCoverage(output: string): number | undefined {
  // 尝试从vitest输出中解析覆盖率
  const coverageMatch = output.match(/All files\s+\|\s+([\d.]+)/)
  if (coverageMatch) {
    return parseFloat(coverageMatch[1])
  }
  
  // 尝试从其他格式解析
  const percentMatch = output.match(/(\d+(?:\.\d+)?)%/)
  if (percentMatch) {
    return parseFloat(percentMatch[1])
  }
  
  return undefined
}

/**
 * 生成测试报告
 */
function generateReport(summary: TestSummary): void {
  console.log('\n' + '='.repeat(60))
  console.log('🎯 DL-Engine Test Summary')
  console.log('='.repeat(60))
  
  console.log(`📦 Total Modules: ${summary.totalModules}`)
  console.log(`✅ Passed: ${summary.passedModules}`)
  console.log(`❌ Failed: ${summary.failedModules}`)
  console.log(`⏱️  Total Duration: ${summary.totalDuration}ms`)
  
  if (summary.averageCoverage > 0) {
    console.log(`📊 Average Coverage: ${summary.averageCoverage.toFixed(1)}%`)
  }
  
  console.log('\n📋 Detailed Results:')
  console.log('-'.repeat(60))
  
  summary.results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const coverage = result.coverage !== undefined ? ` (${result.coverage}%)` : ''
    
    console.log(`${status} ${result.module} - ${result.duration}ms${coverage}`)
    
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
  })
  
  console.log('\n' + '='.repeat(60))
  
  if (summary.failedModules === 0) {
    console.log('🎉 All tests passed!')
  } else {
    console.log(`⚠️  ${summary.failedModules} module(s) failed`)
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  console.log('🚀 Starting DL-Engine test suite...')
  console.log(`Testing ${modules.length} modules`)
  
  const startTime = Date.now()
  const results: TestResult[] = []
  
  // 运行所有模块的测试
  for (const module of modules) {
    const result = await runModuleTests(module)
    results.push(result)
  }
  
  const totalDuration = Date.now() - startTime
  const passedModules = results.filter(r => r.passed).length
  const failedModules = results.filter(r => !r.passed).length
  
  // 计算平均覆盖率
  const coverageResults = results.filter(r => r.coverage !== undefined)
  const averageCoverage = coverageResults.length > 0
    ? coverageResults.reduce((sum, r) => sum + (r.coverage || 0), 0) / coverageResults.length
    : 0
  
  const summary: TestSummary = {
    totalModules: modules.length,
    passedModules,
    failedModules,
    totalDuration,
    averageCoverage,
    results
  }
  
  // 生成报告
  generateReport(summary)
  
  // 设置退出码
  process.exit(failedModules > 0 ? 1 : 0)
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('Test runner failed:', error)
    process.exit(1)
  })
}

export { main as runAllTests }
