/**
 * DL-Engine 状态管理函数
 * 基于Hookstate的响应式状态管理
 */

import {
  extend,
  ExtensionFactory,
  hookstate,
  SetInitialStateAction,
  SetPartialStateAction,
  State,
  useHookstate
} from '@hookstate/core'
import { Identifiable, identifiable } from '@hookstate/identifiable'

import { DeepReadonly, StateDefinition, ReceptorMap, DLEngineStore } from './types'
import { startReactor } from './ReactorFunctions'
import { DLEngineFlux } from './StoreFunctions'

// 重新导出Hookstate核心功能
export * from '@hookstate/core'
export { useHookstate as useState } from '@hookstate/core'
export * from '@hookstate/identifiable'

/**
 * 无代理选项
 */
export const NO_PROXY = { noproxy: true }
export const NO_PROXY_STEALTH = { noproxy: true, stealth: true }

/**
 * 状态定义注册表
 */
export const StateDefinitions = new Map<string, StateDefinition<any, any, any, any>>()

/**
 * 设置初始状态
 */
export const setInitialState = (def: StateDefinition<any, any, any, any>) => {
  const initial = typeof def.initial === 'function' ? (def.initial as any)() : JSON.parse(JSON.stringify(def.initial))
  
  if (DLEngineFlux.store.stateMap[def.name]) {
    DLEngineFlux.store.stateMap[def.name].set(initial)
  } else {
    DLEngineFlux.store.stateMap[def.name] = hookstate(initial, extend(identifiable(def.name), def.extension))
    
    if (def.reactor) {
      const reactor = startReactor(def.reactor, `State - ${def.name}`)
      DLEngineFlux.store.stateReactors[def.name] = reactor
    }
  }
}

/**
 * 定义状态
 * @param definition 状态定义
 * @returns 状态定义对象
 */
export function defineState<S, I = {}, E = {}, R extends ReceptorMap = {}, StateExtras = Record<string, any>>(
  definition: StateDefinition<S, I, E, R> & StateExtras
) {
  if (StateDefinitions.has(definition.name)) {
    throw new Error(`State ${definition.name} already defined`)
  }
  
  StateDefinitions.set(definition.name, definition)
  return definition as StateDefinition<S, I, E, R> & { _TYPE: S } & StateExtras
}

/**
 * 获取可变状态
 * @param StateDefinition 状态定义
 * @returns 可变状态对象
 */
export function getMutableState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  StateDefinition: StateDefinition<S, I, E, R>
) {
  if (!DLEngineFlux.store.stateMap[StateDefinition.name]) {
    setInitialState(StateDefinition)
  }
  return DLEngineFlux.store.stateMap[StateDefinition.name] as State<S, I & E & Identifiable>
}

/**
 * 获取只读状态
 * @param StateDefinition 状态定义
 * @returns 只读状态快照
 */
export function getState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  StateDefinition: StateDefinition<S, I, E, R>
): DeepReadonly<S> {
  if (!DLEngineFlux.store.stateMap[StateDefinition.name]) {
    setInitialState(StateDefinition)
  }
  return DLEngineFlux.store.stateMap[StateDefinition.name].get(NO_PROXY) as DeepReadonly<S>
}

/**
 * 使用状态Hook
 * @param StateDefinition 状态定义
 * @returns 状态Hook
 */
export function useMutableState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  StateDefinition: StateDefinition<S, I, E, R>
) {
  return useHookstate(getMutableState(StateDefinition))
}

/**
 * 检查状态是否存在
 * @param StateDefinition 状态定义
 * @returns 是否存在
 */
export function hasState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  StateDefinition: StateDefinition<S, I, E, R>
): boolean {
  return !!DLEngineFlux.store.stateMap[StateDefinition.name]
}

/**
 * 重置状态到初始值
 * @param StateDefinition 状态定义
 */
export function resetState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  StateDefinition: StateDefinition<S, I, E, R>
): void {
  if (DLEngineFlux.store.stateMap[StateDefinition.name]) {
    const initial = typeof StateDefinition.initial === 'function' 
      ? (StateDefinition.initial as any)() 
      : JSON.parse(JSON.stringify(StateDefinition.initial))
    DLEngineFlux.store.stateMap[StateDefinition.name].set(initial)
  }
}

/**
 * 销毁状态
 * @param StateDefinition 状态定义
 */
export function destroyState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  StateDefinition: StateDefinition<S, I, E, R>
): void {
  // 停止反应器
  if (DLEngineFlux.store.stateReactors[StateDefinition.name]) {
    DLEngineFlux.store.stateReactors[StateDefinition.name].stop()
    delete DLEngineFlux.store.stateReactors[StateDefinition.name]
  }
  
  // 删除状态
  if (DLEngineFlux.store.stateMap[StateDefinition.name]) {
    delete DLEngineFlux.store.stateMap[StateDefinition.name]
  }
  
  // 从定义注册表中移除
  StateDefinitions.delete(StateDefinition.name)
}

/**
 * 获取所有状态名称
 * @returns 状态名称数组
 */
export function getAllStateNames(): string[] {
  return Array.from(StateDefinitions.keys())
}

/**
 * 获取状态定义
 * @param name 状态名称
 * @returns 状态定义或undefined
 */
export function getStateDefinition(name: string): StateDefinition<any, any, any, any> | undefined {
  return StateDefinitions.get(name)
}

/**
 * 状态命名空间键（用于本地存储）
 */
export const stateNamespaceKey = 'dl-engine'

/**
 * 本地存储同步API
 */
export interface SyncStateWithLocalAPI {
  /** 同步到本地存储 */
  syncToLocal: () => void
  
  /** 从本地存储同步 */
  syncFromLocal: () => void
  
  /** 清除本地存储 */
  clearLocal: () => void
}

/**
 * 创建本地存储同步扩展
 * @param keys 要同步的键
 * @returns 扩展工厂
 */
export function syncStateWithLocalStorage<S>(
  keys: string[]
): ExtensionFactory<S, {}, SyncStateWithLocalAPI> {
  return () => {
    if (!globalThis.localStorage) return {}

    return {
      onInit: (state) => {
        const newVals = {} as SetPartialStateAction<S>
        for (const key of keys) {
          const storedValue = localStorage.getItem(`${stateNamespaceKey}.${state.identifier}.${key}`)
          if (storedValue !== null && storedValue !== 'undefined') {
            try {
              newVals[key] = JSON.parse(storedValue)
            } catch (error) {
              console.warn(`Failed to parse stored value for key ${key}:`, error)
            }
          }
        }
        state.merge(newVals)
      },
      
      onSet: (state, { path }, rootState) => {
        if (path.length > 1) return
        const keysToUpdate = path.length ? [(path as string[])[0]] : keys
        
        for (const key of keysToUpdate) {
          if (!keys.includes(key)) return
          
          const storageKey = `${stateNamespaceKey}.${rootState.identifier}.${key}`
          const value = rootState[key]?.get(NO_PROXY)
          
          // 存储标志，即使设置为false或null
          if (value === undefined) {
            localStorage.removeItem(storageKey)
          } else {
            try {
              localStorage.setItem(storageKey, JSON.stringify(value))
            } catch (error) {
              console.warn(`Failed to store value for key ${key}:`, error)
            }
          }
        }
      },
      
      // 扩展API
      syncToLocal: () => {
        const state = DLEngineFlux.store.stateMap[stateNamespaceKey]
        if (!state) return
        
        for (const key of keys) {
          const storageKey = `${stateNamespaceKey}.${state.identifier}.${key}`
          const value = state[key]?.get(NO_PROXY)
          
          if (value === undefined) {
            localStorage.removeItem(storageKey)
          } else {
            try {
              localStorage.setItem(storageKey, JSON.stringify(value))
            } catch (error) {
              console.warn(`Failed to sync ${key} to local storage:`, error)
            }
          }
        }
      },
      
      syncFromLocal: () => {
        const state = DLEngineFlux.store.stateMap[stateNamespaceKey]
        if (!state) return
        
        const newVals = {} as SetPartialStateAction<S>
        for (const key of keys) {
          const storedValue = localStorage.getItem(`${stateNamespaceKey}.${state.identifier}.${key}`)
          if (storedValue !== null && storedValue !== 'undefined') {
            try {
              newVals[key] = JSON.parse(storedValue)
            } catch (error) {
              console.warn(`Failed to sync ${key} from local storage:`, error)
            }
          }
        }
        state.merge(newVals)
      },
      
      clearLocal: () => {
        const state = DLEngineFlux.store.stateMap[stateNamespaceKey]
        if (!state) return
        
        for (const key of keys) {
          const storageKey = `${stateNamespaceKey}.${state.identifier}.${key}`
          localStorage.removeItem(storageKey)
        }
      }
    }
  }
}

/**
 * 状态工具函数
 */
export const StateUtils = {
  /**
   * 深度克隆状态值
   */
  deepClone: <T>(value: T): T => {
    return JSON.parse(JSON.stringify(value))
  },
  
  /**
   * 比较两个状态值是否相等
   */
  isEqual: (a: any, b: any): boolean => {
    return JSON.stringify(a) === JSON.stringify(b)
  },
  
  /**
   * 获取状态的序列化大小
   */
  getSerializedSize: (state: any): number => {
    return new Blob([JSON.stringify(state)]).size
  },
  
  /**
   * 验证状态结构
   */
  validateState: (state: any, schema: any): boolean => {
    // 简单的结构验证，实际项目中可以使用更复杂的验证库
    try {
      JSON.stringify(state)
      return true
    } catch {
      return false
    }
  }
}
