/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { defineQuery, defineSystem, Entity, getComponent, hasComponent } from '@ir-engine/ecs'
import { ECSState } from '@ir-engine/ecs/src/ECSState'
import { SimulationSystemGroup } from '@ir-engine/ecs/src/SystemGroups'
import { getState } from '@ir-engine/hyperflux'
import { Vector3, Quaternion } from 'three'

import { Physics } from '../classes/Physics'
import { RigidBodyComponent, RigidBodyDynamicTagComponent } from '../components/RigidBodyComponent'
import { 
  PhysicsStateSnapshot, 
  getPhysicsStateHistory, 
  PHYSICS_INTERPOLATION_THRESHOLD,
  PHYSICS_EXTRAPOLATION_THRESHOLD 
} from '../PhysicsSerialization'

// Query for entities that need physics prediction
const physicsPredictionQuery = defineQuery([RigidBodyComponent, RigidBodyDynamicTagComponent])

// Prediction state management
interface PredictionState {
  lastConfirmedState: PhysicsStateSnapshot | null
  predictedStates: PhysicsStateSnapshot[]
  rollbackRequired: boolean
}

const predictionStates = new Map<Entity, PredictionState>()

/**
 * Interpolates between two physics states
 */
const interpolatePhysicsState = (
  stateA: PhysicsStateSnapshot,
  stateB: PhysicsStateSnapshot,
  alpha: number
): PhysicsStateSnapshot => {
  return {
    timestamp: stateA.timestamp + (stateB.timestamp - stateA.timestamp) * alpha,
    position: {
      x: stateA.position.x + (stateB.position.x - stateA.position.x) * alpha,
      y: stateA.position.y + (stateB.position.y - stateA.position.y) * alpha,
      z: stateA.position.z + (stateB.position.z - stateA.position.z) * alpha
    },
    rotation: {
      x: stateA.rotation.x,
      y: stateA.rotation.y,
      z: stateA.rotation.z,
      w: stateA.rotation.w
    }, // Quaternion slerp would be more accurate
    linearVelocity: {
      x: stateA.linearVelocity.x + (stateB.linearVelocity.x - stateA.linearVelocity.x) * alpha,
      y: stateA.linearVelocity.y + (stateB.linearVelocity.y - stateA.linearVelocity.y) * alpha,
      z: stateA.linearVelocity.z + (stateB.linearVelocity.z - stateA.linearVelocity.z) * alpha
    },
    angularVelocity: {
      x: stateA.angularVelocity.x + (stateB.angularVelocity.x - stateA.angularVelocity.x) * alpha,
      y: stateA.angularVelocity.y + (stateB.angularVelocity.y - stateA.angularVelocity.y) * alpha,
      z: stateA.angularVelocity.z + (stateB.angularVelocity.z - stateA.angularVelocity.z) * alpha
    },
    isSleeping: alpha < 0.5 ? stateA.isSleeping : stateB.isSleeping,
    flags: stateA.flags | stateB.flags
  }
}

/**
 * Extrapolates physics state based on velocity
 */
const extrapolatePhysicsState = (
  state: PhysicsStateSnapshot,
  deltaTime: number
): PhysicsStateSnapshot => {
  const dt = deltaTime / 1000 // Convert to seconds
  
  return {
    ...state,
    timestamp: state.timestamp + deltaTime,
    position: {
      x: state.position.x + state.linearVelocity.x * dt,
      y: state.position.y + state.linearVelocity.y * dt,
      z: state.position.z + state.linearVelocity.z * dt
    }
    // Angular velocity extrapolation would require quaternion math
  }
}

/**
 * Applies physics state to rigid body component
 */
const applyPhysicsState = (entity: Entity, state: PhysicsStateSnapshot) => {
  const rigidBody = getComponent(entity, RigidBodyComponent)
  if (!rigidBody) return

  rigidBody.position.set(state.position.x, state.position.y, state.position.z)
  rigidBody.rotation.set(state.rotation.x, state.rotation.y, state.rotation.z, state.rotation.w)
  rigidBody.linearVelocity.set(state.linearVelocity.x, state.linearVelocity.y, state.linearVelocity.z)
  rigidBody.angularVelocity.set(state.angularVelocity.x, state.angularVelocity.y, state.angularVelocity.z)

  // Apply to physics world
  const world = Physics.getWorld(entity)
  if (world && world.Rigidbodies.has(entity)) {
    Physics.setRigidbodyPose(
      world,
      entity,
      rigidBody.position,
      rigidBody.rotation,
      rigidBody.linearVelocity,
      rigidBody.angularVelocity
    )
  }
}

/**
 * Performs client-side prediction for physics entities
 */
const performPrediction = (entity: Entity) => {
  const currentTime = getState(ECSState).simulationTime
  const stateHistory = getPhysicsStateHistory(entity)
  
  if (stateHistory.length === 0) return

  // Get prediction state
  let predictionState = predictionStates.get(entity)
  if (!predictionState) {
    predictionState = {
      lastConfirmedState: null,
      predictedStates: [],
      rollbackRequired: false
    }
    predictionStates.set(entity, predictionState)
  }

  // Find the most recent confirmed state
  const latestState = stateHistory[stateHistory.length - 1]
  const timeDiff = currentTime - latestState.timestamp

  if (timeDiff <= PHYSICS_INTERPOLATION_THRESHOLD) {
    // Interpolation case: we have recent enough data
    if (stateHistory.length >= 2) {
      const prevState = stateHistory[stateHistory.length - 2]
      const alpha = Math.min(timeDiff / (latestState.timestamp - prevState.timestamp), 1)
      const interpolatedState = interpolatePhysicsState(prevState, latestState, alpha)
      applyPhysicsState(entity, interpolatedState)
    } else {
      applyPhysicsState(entity, latestState)
    }
  } else if (timeDiff <= PHYSICS_EXTRAPOLATION_THRESHOLD) {
    // Extrapolation case: predict future state
    const extrapolatedState = extrapolatePhysicsState(latestState, timeDiff)
    applyPhysicsState(entity, extrapolatedState)
  } else {
    // Too much lag, just use the latest state
    applyPhysicsState(entity, latestState)
  }

  predictionState.lastConfirmedState = latestState
}

/**
 * Handles rollback when authoritative state differs from prediction
 */
const handleRollback = (entity: Entity, authoritativeState: PhysicsStateSnapshot) => {
  const predictionState = predictionStates.get(entity)
  if (!predictionState || !predictionState.lastConfirmedState) return

  // Check if rollback is needed
  const positionDiff = Math.sqrt(
    Math.pow(authoritativeState.position.x - predictionState.lastConfirmedState.position.x, 2) +
    Math.pow(authoritativeState.position.y - predictionState.lastConfirmedState.position.y, 2) +
    Math.pow(authoritativeState.position.z - predictionState.lastConfirmedState.position.z, 2)
  )

  const ROLLBACK_THRESHOLD = 0.1 // 10cm difference triggers rollback
  if (positionDiff > ROLLBACK_THRESHOLD) {
    // Perform rollback
    applyPhysicsState(entity, authoritativeState)
    predictionState.rollbackRequired = true
    predictionState.predictedStates = []
  }
}

const execute = () => {
  const entities = physicsPredictionQuery()
  
  for (const entity of entities) {
    performPrediction(entity)
  }
}

export const PhysicsPredictionSystem = defineSystem({
  uuid: 'ee.engine.PhysicsPredictionSystem',
  insert: { after: SimulationSystemGroup },
  execute
})

export const PhysicsPredictionFunctions = {
  interpolatePhysicsState,
  extrapolatePhysicsState,
  applyPhysicsState,
  performPrediction,
  handleRollback
}
