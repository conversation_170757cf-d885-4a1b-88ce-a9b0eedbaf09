/**
 * DL-Engine 配置验证脚本
 * 验证所有TypeScript配置文件的正确性
 */

import { existsSync, readFileSync } from 'fs'
import { join } from 'path'

interface ValidationResult {
  file: string
  valid: boolean
  errors: string[]
  warnings: string[]
}

const modules = [
  'engine/core',
  'engine/ecs', 
  'engine/physics',
  'engine/state',
  'engine/xr',
  'engine/ai',
  'shared/common'
]

/**
 * 验证单个tsconfig.json文件
 */
function validateTsConfig(modulePath: string): ValidationResult {
  const configPath = join(process.cwd(), modulePath, 'tsconfig.json')
  const result: ValidationResult = {
    file: configPath,
    valid: true,
    errors: [],
    warnings: []
  }
  
  if (!existsSync(configPath)) {
    result.valid = false
    result.errors.push('tsconfig.json file not found')
    return result
  }
  
  try {
    const configContent = readFileSync(configPath, 'utf8')
    const config = JSON.parse(configContent)
    
    // 检查必需的配置
    if (!config.compilerOptions) {
      result.errors.push('Missing compilerOptions')
      result.valid = false
    } else {
      const options = config.compilerOptions
      
      // 检查composite设置
      if (options.composite !== true) {
        result.errors.push('Missing or invalid composite setting')
        result.valid = false
      }
      
      // 检查noEmit设置
      if (options.noEmit !== false) {
        result.errors.push('noEmit should be false for buildable modules')
        result.valid = false
      }
      
      // 检查allowImportingTsExtensions设置
      if (options.allowImportingTsExtensions !== false) {
        result.errors.push('allowImportingTsExtensions should be false when noEmit is false')
        result.valid = false
      }
      
      // 检查输出目录
      if (!options.outDir) {
        result.errors.push('Missing outDir setting')
        result.valid = false
      }
      
      // 检查根目录
      if (!options.rootDir) {
        result.errors.push('Missing rootDir setting')
        result.valid = false
      }
    }
    
    // 检查include配置
    if (!config.include || !Array.isArray(config.include)) {
      result.errors.push('Missing or invalid include setting')
      result.valid = false
    }
    
    // 检查extends配置
    if (!config.extends) {
      result.warnings.push('No extends configuration found')
    }
    
  } catch (error: any) {
    result.valid = false
    result.errors.push(`JSON parse error: ${error.message}`)
  }
  
  return result
}

/**
 * 验证package.json文件
 */
function validatePackageJson(modulePath: string): ValidationResult {
  const packagePath = join(process.cwd(), modulePath, 'package.json')
  const result: ValidationResult = {
    file: packagePath,
    valid: true,
    errors: [],
    warnings: []
  }
  
  if (!existsSync(packagePath)) {
    result.valid = false
    result.errors.push('package.json file not found')
    return result
  }
  
  try {
    const packageContent = readFileSync(packagePath, 'utf8')
    const pkg = JSON.parse(packageContent)
    
    // 检查必需的字段
    if (!pkg.name) {
      result.errors.push('Missing package name')
      result.valid = false
    }
    
    if (!pkg.version) {
      result.errors.push('Missing package version')
      result.valid = false
    }
    
    // 检查脚本
    if (!pkg.scripts) {
      result.warnings.push('No scripts defined')
    } else {
      const requiredScripts = ['build', 'test', 'type-check']
      for (const script of requiredScripts) {
        if (!pkg.scripts[script]) {
          result.warnings.push(`Missing ${script} script`)
        }
      }
    }
    
    // 检查依赖
    if (!pkg.dependencies && !pkg.devDependencies) {
      result.warnings.push('No dependencies defined')
    }
    
  } catch (error: any) {
    result.valid = false
    result.errors.push(`JSON parse error: ${error.message}`)
  }
  
  return result
}

/**
 * 生成验证报告
 */
function generateReport(results: ValidationResult[]): void {
  console.log('\n' + '='.repeat(60))
  console.log('🔍 DL-Engine Configuration Validation Report')
  console.log('='.repeat(60))
  
  const validFiles = results.filter(r => r.valid).length
  const invalidFiles = results.filter(r => !r.valid).length
  const totalWarnings = results.reduce((sum, r) => sum + r.warnings.length, 0)
  
  console.log(`📊 Summary:`)
  console.log(`   ✅ Valid files: ${validFiles}`)
  console.log(`   ❌ Invalid files: ${invalidFiles}`)
  console.log(`   ⚠️  Total warnings: ${totalWarnings}`)
  
  console.log('\n📋 Detailed Results:')
  console.log('-'.repeat(60))
  
  results.forEach(result => {
    const status = result.valid ? '✅' : '❌'
    const fileName = result.file.replace(process.cwd(), '.')
    
    console.log(`${status} ${fileName}`)
    
    if (result.errors.length > 0) {
      result.errors.forEach(error => {
        console.log(`   ❌ Error: ${error}`)
      })
    }
    
    if (result.warnings.length > 0) {
      result.warnings.forEach(warning => {
        console.log(`   ⚠️  Warning: ${warning}`)
      })
    }
  })
  
  console.log('\n' + '='.repeat(60))
  
  if (invalidFiles === 0) {
    console.log('🎉 All configuration files are valid!')
  } else {
    console.log(`⚠️  ${invalidFiles} configuration file(s) need attention`)
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  console.log('🚀 Starting DL-Engine configuration validation...')
  
  const results: ValidationResult[] = []
  
  // 验证根目录配置
  results.push(validateTsConfig('.'))
  results.push(validatePackageJson('.'))
  
  // 验证各模块配置
  for (const module of modules) {
    console.log(`🔍 Validating ${module}...`)
    results.push(validateTsConfig(module))
    results.push(validatePackageJson(module))
  }
  
  // 生成报告
  generateReport(results)
  
  // 设置退出码
  const hasErrors = results.some(r => !r.valid)
  process.exit(hasErrors ? 1 : 0)
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('Validation failed:', error)
    process.exit(1)
  })
}

export { main as validateConfig }
