/**
 * DL-Engine ECS引擎测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { Engine } from '../Engine'
import { Entity, UndefinedEntity } from '../Entity'
import { defineComponent, setComponent, getComponent, hasComponent, removeComponent } from '../ComponentFunctions'
import { defineSystem } from '../SystemFunctions'

// 测试组件
const TestComponent = defineComponent({
  name: 'TestComponent',
  schema: {
    value: { type: 'number', default: 0 },
    name: { type: 'string', default: '' }
  }
})

const AnotherComponent = defineComponent({
  name: 'AnotherComponent',
  schema: {
    active: { type: 'boolean', default: true }
  }
})

describe('Engine', () => {
  let engine: Engine
  
  beforeEach(() => {
    engine = new Engine()
  })
  
  afterEach(() => {
    engine.destroy()
  })
  
  describe('Entity Management', () => {
    it('should create entities with unique IDs', () => {
      const entity1 = engine.createEntity()
      const entity2 = engine.createEntity()
      
      expect(entity1).not.toBe(UndefinedEntity)
      expect(entity2).not.toBe(UndefinedEntity)
      expect(entity1).not.toBe(entity2)
    })
    
    it('should create entities with names', () => {
      const entity = engine.createEntity('TestEntity')
      expect(entity).not.toBe(UndefinedEntity)
      
      const entityName = engine.getEntityName(entity)
      expect(entityName).toBe('TestEntity')
    })
    
    it('should remove entities', () => {
      const entity = engine.createEntity()
      expect(engine.entityExists(entity)).toBe(true)
      
      engine.removeEntity(entity)
      expect(engine.entityExists(entity)).toBe(false)
    })
    
    it('should get all entities', () => {
      const entity1 = engine.createEntity()
      const entity2 = engine.createEntity()
      
      const entities = engine.getAllEntities()
      expect(entities).toContain(entity1)
      expect(entities).toContain(entity2)
      expect(entities.length).toBeGreaterThanOrEqual(2)
    })
  })
  
  describe('Component Management', () => {
    let entity: Entity
    
    beforeEach(() => {
      entity = engine.createEntity()
    })
    
    it('should add components to entities', () => {
      setComponent(entity, TestComponent, { value: 42, name: 'test' })
      
      expect(hasComponent(entity, TestComponent)).toBe(true)
      
      const component = getComponent(entity, TestComponent)
      expect(component.value).toBe(42)
      expect(component.name).toBe('test')
    })
    
    it('should update component values', () => {
      setComponent(entity, TestComponent, { value: 10 })
      
      let component = getComponent(entity, TestComponent)
      expect(component.value).toBe(10)
      
      setComponent(entity, TestComponent, { value: 20 })
      
      component = getComponent(entity, TestComponent)
      expect(component.value).toBe(20)
    })
    
    it('should remove components from entities', () => {
      setComponent(entity, TestComponent, { value: 42 })
      expect(hasComponent(entity, TestComponent)).toBe(true)
      
      removeComponent(entity, TestComponent)
      expect(hasComponent(entity, TestComponent)).toBe(false)
    })
    
    it('should handle multiple components on same entity', () => {
      setComponent(entity, TestComponent, { value: 42 })
      setComponent(entity, AnotherComponent, { active: false })
      
      expect(hasComponent(entity, TestComponent)).toBe(true)
      expect(hasComponent(entity, AnotherComponent)).toBe(true)
      
      const testComp = getComponent(entity, TestComponent)
      const anotherComp = getComponent(entity, AnotherComponent)
      
      expect(testComp.value).toBe(42)
      expect(anotherComp.active).toBe(false)
    })
  })
  
  describe('System Management', () => {
    let entity: Entity
    let systemExecuted: boolean
    
    beforeEach(() => {
      entity = engine.createEntity()
      systemExecuted = false
    })
    
    it('should execute systems', () => {
      const TestSystem = defineSystem({
        uuid: 'test-system',
        execute: () => {
          systemExecuted = true
        }
      })
      
      engine.addSystem(TestSystem)
      engine.executeSystem(TestSystem.uuid)
      
      expect(systemExecuted).toBe(true)
    })
    
    it('should execute systems with queries', () => {
      let processedEntities: Entity[] = []
      
      const TestSystem = defineSystem({
        uuid: 'query-system',
        execute: () => {
          const entities = engine.getEntitiesWithComponents([TestComponent])
          processedEntities = entities
        }
      })
      
      // 创建带组件的实体
      setComponent(entity, TestComponent, { value: 100 })
      const entity2 = engine.createEntity()
      setComponent(entity2, TestComponent, { value: 200 })
      
      // 创建不带组件的实体
      const entity3 = engine.createEntity()
      
      engine.addSystem(TestSystem)
      engine.executeSystem(TestSystem.uuid)
      
      expect(processedEntities).toContain(entity)
      expect(processedEntities).toContain(entity2)
      expect(processedEntities).not.toContain(entity3)
      expect(processedEntities.length).toBe(2)
    })
    
    it('should remove systems', () => {
      const TestSystem = defineSystem({
        uuid: 'removable-system',
        execute: () => {
          systemExecuted = true
        }
      })
      
      engine.addSystem(TestSystem)
      expect(engine.hasSystem(TestSystem.uuid)).toBe(true)
      
      engine.removeSystem(TestSystem.uuid)
      expect(engine.hasSystem(TestSystem.uuid)).toBe(false)
      
      // 系统被移除后不应该执行
      engine.executeSystem(TestSystem.uuid)
      expect(systemExecuted).toBe(false)
    })
  })
  
  describe('Engine Lifecycle', () => {
    it('should start and stop engine', () => {
      expect(engine.isRunning()).toBe(false)
      
      engine.start()
      expect(engine.isRunning()).toBe(true)
      
      engine.stop()
      expect(engine.isRunning()).toBe(false)
    })
    
    it('should execute frame updates', () => {
      let frameCount = 0
      
      const FrameSystem = defineSystem({
        uuid: 'frame-system',
        execute: () => {
          frameCount++
        }
      })
      
      engine.addSystem(FrameSystem)
      
      // 手动执行帧更新
      engine.executeFrame(16.67) // ~60fps
      expect(frameCount).toBe(1)
      
      engine.executeFrame(16.67)
      expect(frameCount).toBe(2)
    })
    
    it('should handle delta time in systems', () => {
      let receivedDeltaTime = 0
      
      const DeltaSystem = defineSystem({
        uuid: 'delta-system',
        execute: (deltaTime: number) => {
          receivedDeltaTime = deltaTime
        }
      })
      
      engine.addSystem(DeltaSystem)
      engine.executeFrame(33.33) // ~30fps
      
      expect(receivedDeltaTime).toBe(33.33)
    })
  })
  
  describe('Performance', () => {
    it('should handle large numbers of entities efficiently', () => {
      const startTime = performance.now()
      
      // 创建大量实体
      const entities: Entity[] = []
      for (let i = 0; i < 1000; i++) {
        const entity = engine.createEntity(`Entity_${i}`)
        setComponent(entity, TestComponent, { value: i })
        entities.push(entity)
      }
      
      const creationTime = performance.now() - startTime
      expect(creationTime).toBeLessThan(100) // 应该在100ms内完成
      
      // 查询性能测试
      const queryStartTime = performance.now()
      const queriedEntities = engine.getEntitiesWithComponents([TestComponent])
      const queryTime = performance.now() - queryStartTime
      
      expect(queriedEntities.length).toBe(1000)
      expect(queryTime).toBeLessThan(10) // 查询应该很快
    })
    
    it('should clean up removed entities efficiently', () => {
      // 创建实体
      const entities: Entity[] = []
      for (let i = 0; i < 100; i++) {
        const entity = engine.createEntity()
        setComponent(entity, TestComponent, { value: i })
        entities.push(entity)
      }
      
      // 移除一半实体
      for (let i = 0; i < 50; i++) {
        engine.removeEntity(entities[i])
      }
      
      // 验证清理
      const remainingEntities = engine.getEntitiesWithComponents([TestComponent])
      expect(remainingEntities.length).toBe(50)
      
      // 验证被移除的实体确实不存在
      for (let i = 0; i < 50; i++) {
        expect(engine.entityExists(entities[i])).toBe(false)
      }
    })
  })
  
  describe('Error Handling', () => {
    it('should handle invalid entity operations gracefully', () => {
      const invalidEntity = 99999 as Entity
      
      expect(() => {
        setComponent(invalidEntity, TestComponent, { value: 42 })
      }).not.toThrow()
      
      expect(hasComponent(invalidEntity, TestComponent)).toBe(false)
      expect(engine.entityExists(invalidEntity)).toBe(false)
    })
    
    it('should handle system execution errors', () => {
      const ErrorSystem = defineSystem({
        uuid: 'error-system',
        execute: () => {
          throw new Error('Test error')
        }
      })
      
      engine.addSystem(ErrorSystem)
      
      // 系统错误不应该崩溃引擎
      expect(() => {
        engine.executeSystem(ErrorSystem.uuid)
      }).not.toThrow()
    })
  })
})
